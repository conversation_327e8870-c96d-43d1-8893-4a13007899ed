<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخزون</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .item-image {
            width: 50px;
            height: 50px;
            border-radius: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.2rem;
        }

        .stock-level {
            font-weight: bold;
        }

        .stock-high {
            color: #28a745;
        }

        .stock-medium {
            color: #ffc107;
        }

        .stock-low {
            color: #dc3545;
        }

        .stock-out {
            color: #6c757d;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            font-weight: 600;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 2rem;
        }

        .warehouse-badge {
            font-size: 0.7rem;
            padding: 0.25rem 0.5rem;
            margin: 0.1rem;
        }

        .movement-in {
            color: #28a745;
            font-weight: bold;
        }

        .movement-out {
            color: #dc3545;
            font-weight: bold;
        }

        .movement-transfer {
            color: #17a2b8;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html" class="active">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-box-seam text-primary me-2"></i>
                    إدارة المخزون
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddItemModal()">
                        <i class="bi bi-plus-lg me-1"></i>إضافة صنف
                    </button>
                    <button type="button" class="btn btn-success" onclick="showMovementModal()">
                        <i class="bi bi-arrow-up-down me-1"></i>حركة مخزون
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showInventoryModal()">
                        <i class="bi bi-clipboard-check me-1"></i>جرد
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>

            <!-- إحصائيات المخزون -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #28a745;">
                        <div class="icon text-success">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="number text-success" id="totalItems">25</div>
                        <div class="label">إجمالي الأصناف</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #17a2b8;">
                        <div class="icon text-info">
                            <i class="bi bi-building"></i>
                        </div>
                        <div class="number text-info" id="totalWarehouses">4</div>
                        <div class="label">المخازن</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #ffc107;">
                        <div class="icon text-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="number text-warning" id="lowStockItems">3</div>
                        <div class="label">أصناف منخفضة</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #dc3545;">
                        <div class="icon text-danger">
                            <i class="bi bi-x-circle"></i>
                        </div>
                        <div class="number text-danger" id="outOfStockItems">1</div>
                        <div class="label">أصناف منتهية</div>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="card">
                <div class="card-header p-0">
                    <ul class="nav nav-tabs" id="inventoryTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="items-tab" data-bs-toggle="tab" data-bs-target="#items" type="button" role="tab">
                                <i class="bi bi-box-seam me-2"></i>الأصناف
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="warehouses-tab" data-bs-toggle="tab" data-bs-target="#warehouses" type="button" role="tab">
                                <i class="bi bi-building me-2"></i>المخازن
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="movements-tab" data-bs-toggle="tab" data-bs-target="#movements" type="button" role="tab">
                                <i class="bi bi-arrow-up-down me-2"></i>حركات المخزون
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                                <i class="bi bi-graph-up me-2"></i>تقارير المخزون
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="inventoryTabContent">
                    <!-- تبويب الأصناف -->
                    <div class="tab-pane fade show active" id="items" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="searchInput" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الأصناف...">
                            </div>
                            <div class="col-md-3">
                                <label for="categoryFilter" class="form-label">الفئة</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <option value="flour">دقيق ومواد أساسية</option>
                                    <option value="ingredients">مكونات</option>
                                    <option value="packaging">مواد تعبئة</option>
                                    <option value="tools">أدوات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="stockFilter" class="form-label">حالة المخزون</label>
                                <select class="form-select" id="stockFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="high">مخزون عالي</option>
                                    <option value="medium">مخزون متوسط</option>
                                    <option value="low">مخزون منخفض</option>
                                    <option value="out">منتهي</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="bi bi-x-lg me-1"></i>مسح
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الأصناف -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="itemsTable">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الاسم</th>
                                        <th>الكود</th>
                                        <th>الفئة</th>
                                        <th>الوحدة</th>
                                        <th>المخزون الكلي</th>
                                        <th>الحد الأدنى</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب المخازن -->
                    <div class="tab-pane fade" id="warehouses" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <button type="button" class="btn btn-primary" onclick="showAddWarehouseModal()">
                                    <i class="bi bi-plus-lg me-1"></i>إضافة مخزن
                                </button>
                            </div>
                        </div>

                        <div class="row" id="warehousesGrid">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- تبويب حركات المخزون -->
                    <div class="tab-pane fade" id="movements" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="movementDateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="movementDateFrom">
                            </div>
                            <div class="col-md-3">
                                <label for="movementDateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="movementDateTo">
                            </div>
                            <div class="col-md-3">
                                <label for="movementTypeFilter" class="form-label">نوع الحركة</label>
                                <select class="form-select" id="movementTypeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="in">وارد</option>
                                    <option value="out">صادر</option>
                                    <option value="transfer">تحويل</option>
                                    <option value="adjustment">تسوية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="loadMovements()">
                                        <i class="bi bi-search me-1"></i>عرض الحركات
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="movementsTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الصنف</th>
                                        <th>نوع الحركة</th>
                                        <th>المخزن</th>
                                        <th>الكمية</th>
                                        <th>المرجع</th>
                                        <th>المستخدم</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="movementsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب تقارير المخزون -->
                    <div class="tab-pane fade" id="reports" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">تقرير المخزون حسب الفئة</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="categoryChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">الأصناف الأكثر حركة</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush" id="topMovingItems">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">تقرير قيمة المخزون</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>الفئة</th>
                                                        <th>عدد الأصناف</th>
                                                        <th>إجمالي الكمية</th>
                                                        <th>متوسط السعر</th>
                                                        <th>إجمالي القيمة</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="inventoryValueTableBody">
                                                    <!-- سيتم ملؤها بـ JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة صنف جديد -->
    <div class="modal fade" id="addItemModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة صنف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addItemForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">المعلومات الأساسية</h6>

                                <div class="mb-3">
                                    <label for="itemCode" class="form-label">كود الصنف *</label>
                                    <input type="text" class="form-control" id="itemCode" required>
                                </div>

                                <div class="mb-3">
                                    <label for="itemName" class="form-label">اسم الصنف *</label>
                                    <input type="text" class="form-control" id="itemName" required>
                                </div>

                                <div class="mb-3">
                                    <label for="itemCategory" class="form-label">الفئة *</label>
                                    <select class="form-select" id="itemCategory" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="flour">دقيق ومواد أساسية</option>
                                        <option value="ingredients">مكونات</option>
                                        <option value="packaging">مواد تعبئة</option>
                                        <option value="tools">أدوات</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="itemUnit" class="form-label">وحدة القياس *</label>
                                    <select class="form-select" id="itemUnit" required>
                                        <option value="">اختر الوحدة</option>
                                        <option value="kg">كيلوجرام</option>
                                        <option value="g">جرام</option>
                                        <option value="l">لتر</option>
                                        <option value="ml">مليلتر</option>
                                        <option value="piece">قطعة</option>
                                        <option value="box">صندوق</option>
                                        <option value="bag">كيس</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="itemDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="itemDescription" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">إعدادات المخزون</h6>

                                <div class="mb-3">
                                    <label for="minStock" class="form-label">الحد الأدنى للمخزون *</label>
                                    <input type="number" class="form-control" id="minStock" step="0.001" required>
                                </div>

                                <div class="mb-3">
                                    <label for="maxStock" class="form-label">الحد الأقصى للمخزون</label>
                                    <input type="number" class="form-control" id="maxStock" step="0.001">
                                </div>

                                <div class="mb-3">
                                    <label for="reorderPoint" class="form-label">نقطة إعادة الطلب</label>
                                    <input type="number" class="form-control" id="reorderPoint" step="0.001">
                                </div>

                                <div class="mb-3">
                                    <label for="costPrice" class="form-label">سعر التكلفة</label>
                                    <input type="number" class="form-control" id="costPrice" step="0.01">
                                </div>

                                <div class="mb-3">
                                    <label for="sellingPrice" class="form-label">سعر البيع</label>
                                    <input type="number" class="form-control" id="sellingPrice" step="0.01">
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isActive" checked>
                                    <label class="form-check-label" for="isActive">
                                        صنف نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <hr>

                        <h6 class="text-primary mb-3">الأرصدة الافتتاحية في المخازن</h6>
                        <div id="warehouseStocks">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveItem()">حفظ الصنف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة حركة المخزون -->
    <div class="modal fade" id="movementModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حركة مخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="movementForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="movementType" class="form-label">نوع الحركة *</label>
                                    <select class="form-select" id="movementType" required onchange="toggleMovementFields()">
                                        <option value="">اختر نوع الحركة</option>
                                        <option value="in">وارد</option>
                                        <option value="out">صادر</option>
                                        <option value="transfer">تحويل</option>
                                        <option value="adjustment">تسوية</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="movementItem" class="form-label">الصنف *</label>
                                    <select class="form-select" id="movementItem" required onchange="updateItemWarehouses()">
                                        <option value="">اختر الصنف</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="movementWarehouse" class="form-label">المخزن *</label>
                                    <select class="form-select" id="movementWarehouse" required>
                                        <option value="">اختر المخزن</option>
                                    </select>
                                    <div class="form-text">الرصيد الحالي: <span id="currentStock">0</span></div>
                                </div>

                                <div class="mb-3" id="toWarehouseField" style="display: none;">
                                    <label for="toWarehouse" class="form-label">إلى المخزن *</label>
                                    <select class="form-select" id="toWarehouse">
                                        <option value="">اختر المخزن</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="movementQuantity" class="form-label">الكمية *</label>
                                    <input type="number" class="form-control" id="movementQuantity" step="0.001" required>
                                </div>

                                <div class="mb-3">
                                    <label for="movementDate" class="form-label">التاريخ *</label>
                                    <input type="date" class="form-control" id="movementDate" required>
                                </div>

                                <div class="mb-3">
                                    <label for="movementReference" class="form-label">المرجع</label>
                                    <input type="text" class="form-control" id="movementReference" placeholder="رقم الفاتورة أو المرجع...">
                                </div>

                                <div class="mb-3">
                                    <label for="movementNotes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="movementNotes" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveMovement()">تنفيذ الحركة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مخزن -->
    <div class="modal fade" id="addWarehouseModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مخزن جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addWarehouseForm">
                        <div class="mb-3">
                            <label for="warehouseCode" class="form-label">كود المخزن *</label>
                            <input type="text" class="form-control" id="warehouseCode" required>
                        </div>

                        <div class="mb-3">
                            <label for="warehouseName" class="form-label">اسم المخزن *</label>
                            <input type="text" class="form-control" id="warehouseName" required>
                        </div>

                        <div class="mb-3">
                            <label for="warehouseLocation" class="form-label">الموقع</label>
                            <input type="text" class="form-control" id="warehouseLocation">
                        </div>

                        <div class="mb-3">
                            <label for="warehouseManager" class="form-label">المسؤول</label>
                            <input type="text" class="form-control" id="warehouseManager">
                        </div>

                        <div class="mb-3">
                            <label for="warehouseDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="warehouseDescription" rows="3"></textarea>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="warehouseActive" checked>
                            <label class="form-check-label" for="warehouseActive">
                                مخزن نشط
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveWarehouse()">حفظ المخزن</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة الجرد -->
    <div class="modal fade" id="inventoryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">جرد المخزون</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        سيتم مقارنة الكميات الفعلية مع الكميات المسجلة في النظام وإنشاء حركات تسوية عند الحاجة.
                    </div>

                    <form id="inventoryForm">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="inventoryWarehouse" class="form-label">المخزن *</label>
                                <select class="form-select" id="inventoryWarehouse" required onchange="loadInventoryItems()">
                                    <option value="">اختر المخزن</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="inventoryDate" class="form-label">تاريخ الجرد *</label>
                                <input type="date" class="form-control" id="inventoryDate" required>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="inventoryTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الكمية المسجلة</th>
                                        <th>الكمية الفعلية</th>
                                        <th>الفرق</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="inventoryTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="processInventory()">تنفيذ الجرد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Inventory JS Files -->
    <script src="inventory-functions.js"></script>
    <script src="inventory-modals.js"></script>
    <script src="inventory-reports.js"></script>
    <script src="print-system.js"></script>
    <script src="system-init.js"></script>

    <script>
        // بيانات المخازن التجريبية
        let warehouses = [
            {
                id: 1,
                code: 'WH001',
                name: 'المخزن الرئيسي',
                location: 'الطابق الأرضي',
                manager: 'أحمد المحاسب',
                description: 'المخزن الرئيسي للمواد الأساسية',
                isActive: true
            },
            {
                id: 2,
                code: 'WH002',
                name: 'مخزن المكونات',
                location: 'الطابق الثاني',
                manager: 'فاطمة المخزنية',
                description: 'مخزن المكونات والإضافات',
                isActive: true
            },
            {
                id: 3,
                code: 'WH003',
                name: 'مخزن التعبئة',
                location: 'قسم التعبئة',
                manager: 'محمد العامل',
                description: 'مخزن مواد التعبئة والتغليف',
                isActive: true
            },
            {
                id: 4,
                code: 'WH004',
                name: 'مخزن الأدوات',
                location: 'المستودع الخلفي',
                manager: 'سارة المشرفة',
                description: 'مخزن الأدوات والمعدات',
                isActive: true
            }
        ];

        // بيانات الأصناف التجريبية
        let items = [
            {
                id: 1,
                code: 'ITEM001',
                name: 'دقيق أبيض',
                category: 'flour',
                unit: 'kg',
                description: 'دقيق أبيض عالي الجودة',
                minStock: 100,
                maxStock: 1000,
                reorderPoint: 200,
                costPrice: 800,
                sellingPrice: 1000,
                isActive: true,
                warehouseStocks: {
                    1: 500,
                    2: 200,
                    3: 0,
                    4: 0
                }
            },
            {
                id: 2,
                code: 'ITEM002',
                name: 'سكر أبيض',
                category: 'ingredients',
                unit: 'kg',
                description: 'سكر أبيض ناعم',
                minStock: 50,
                maxStock: 500,
                reorderPoint: 100,
                costPrice: 1200,
                sellingPrice: 1500,
                isActive: true,
                warehouseStocks: {
                    1: 150,
                    2: 100,
                    3: 0,
                    4: 0
                }
            },
            {
                id: 3,
                code: 'ITEM003',
                name: 'خميرة فورية',
                category: 'ingredients',
                unit: 'g',
                description: 'خميرة فورية للخبز',
                minStock: 1000,
                maxStock: 10000,
                reorderPoint: 2000,
                costPrice: 5,
                sellingPrice: 8,
                isActive: true,
                warehouseStocks: {
                    1: 3000,
                    2: 5000,
                    3: 0,
                    4: 0
                }
            },
            {
                id: 4,
                code: 'ITEM004',
                name: 'أكياس ورقية',
                category: 'packaging',
                unit: 'piece',
                description: 'أكياس ورقية للخبز',
                minStock: 1000,
                maxStock: 10000,
                reorderPoint: 2000,
                costPrice: 2,
                sellingPrice: 3,
                isActive: true,
                warehouseStocks: {
                    1: 0,
                    2: 0,
                    3: 5000,
                    4: 0
                }
            },
            {
                id: 5,
                code: 'ITEM005',
                name: 'ملح طعام',
                category: 'ingredients',
                unit: 'kg',
                description: 'ملح طعام نقي',
                minStock: 20,
                maxStock: 200,
                reorderPoint: 50,
                costPrice: 300,
                sellingPrice: 400,
                isActive: true,
                warehouseStocks: {
                    1: 80,
                    2: 30,
                    3: 0,
                    4: 0
                }
            },
            {
                id: 6,
                code: 'ITEM006',
                name: 'زيت نباتي',
                category: 'ingredients',
                unit: 'l',
                description: 'زيت نباتي للطبخ',
                minStock: 10,
                maxStock: 100,
                reorderPoint: 25,
                costPrice: 2000,
                sellingPrice: 2500,
                isActive: true,
                warehouseStocks: {
                    1: 15,
                    2: 20,
                    3: 0,
                    4: 0
                }
            }
        ];

        // بيانات حركات المخزون التجريبية
        let movements = [
            {
                id: 1,
                itemId: 1,
                itemName: 'دقيق أبيض',
                type: 'in',
                warehouseId: 1,
                warehouseName: 'المخزن الرئيسي',
                quantity: 200,
                date: '2024-01-15',
                reference: 'PO-001',
                user: 'أحمد المحاسب',
                notes: 'شراء دفعة جديدة'
            },
            {
                id: 2,
                itemId: 2,
                itemName: 'سكر أبيض',
                type: 'out',
                warehouseId: 1,
                warehouseName: 'المخزن الرئيسي',
                quantity: 50,
                date: '2024-01-14',
                reference: 'SO-001',
                user: 'فاطمة المخزنية',
                notes: 'استخدام في الإنتاج'
            },
            {
                id: 3,
                itemId: 3,
                itemName: 'خميرة فورية',
                type: 'transfer',
                warehouseId: 1,
                warehouseName: 'المخزن الرئيسي',
                quantity: 1000,
                date: '2024-01-13',
                reference: 'TR-001',
                user: 'محمد العامل',
                notes: 'تحويل إلى مخزن المكونات'
            }
        ];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }
    </script>
</body>
</html>