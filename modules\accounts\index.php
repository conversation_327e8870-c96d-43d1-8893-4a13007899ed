<?php
/**
 * شجرة الحسابات المحاسبية
 * Chart of Accounts
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('accounts');

$page_title = 'شجرة الحسابات المحاسبية';

// معالجة الحذف
if (isset($_GET['delete']) && isset($_GET['id'])) {
    checkCSRF();
    
    try {
        $accountId = intval($_GET['id']);
        
        // التحقق من وجود حسابات فرعية
        $subAccounts = queryOne(
            "SELECT COUNT(*) as count FROM chart_of_accounts WHERE parent_id = ?",
            [$accountId]
        );
        
        if ($subAccounts['count'] > 0) {
            throw new Exception('لا يمكن حذف الحساب لوجود حسابات فرعية');
        }
        
        // التحقق من وجود قيود محاسبية
        $journalEntries = queryOne(
            "SELECT COUNT(*) as count FROM journal_entry_details WHERE account_id = ?",
            [$accountId]
        );
        
        if ($journalEntries['count'] > 0) {
            throw new Exception('لا يمكن حذف الحساب لوجود قيود محاسبية مرتبطة به');
        }
        
        // الحصول على بيانات الحساب قبل الحذف
        $account = queryOne(
            "SELECT * FROM chart_of_accounts WHERE id = ?",
            [$accountId]
        );
        
        if (!$account) {
            throw new Exception('الحساب غير موجود');
        }
        
        // حذف الحساب
        execute("DELETE FROM chart_of_accounts WHERE id = ?", [$accountId]);
        
        // تسجيل النشاط
        logUserActivity('delete', 'chart_of_accounts', $accountId, $account, null);
        
        showMessage('تم حذف الحساب بنجاح', 'success');
        
    } catch (Exception $e) {
        showMessage($e->getMessage(), 'error');
        logError("خطأ في حذف الحساب: " . $e->getMessage());
    }
    
    header('Location: index.php');
    exit();
}

// الحصول على جميع الحسابات مع ترتيب هرمي
try {
    $accounts = query(
        "SELECT a.*, p.account_name as parent_name,
                (SELECT COUNT(*) FROM chart_of_accounts WHERE parent_id = a.id) as sub_accounts_count,
                (SELECT COUNT(*) FROM journal_entry_details WHERE account_id = a.id) as entries_count
         FROM chart_of_accounts a
         LEFT JOIN chart_of_accounts p ON a.parent_id = p.id
         ORDER BY a.account_code"
    );
    
    // تنظيم الحسابات في شكل هرمي
    $accountsTree = [];
    $accountsFlat = [];
    
    foreach ($accounts as $account) {
        $accountsFlat[$account['id']] = $account;
        if ($account['parent_id'] == null) {
            $accountsTree[] = $account;
        }
    }
    
} catch (Exception $e) {
    $accounts = [];
    $accountsTree = [];
    logError("خطأ في تحميل الحسابات: " . $e->getMessage());
    showMessage('خطأ في تحميل البيانات', 'error');
}

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-diagram-3 text-primary me-2"></i>
        شجرة الحسابات المحاسبية
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="create.php" class="btn btn-primary">
                <i class="bi bi-plus-lg me-1"></i>
                إضافة حساب جديد
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="stats-card" style="border-right-color: #28a745;">
            <div class="icon text-success">
                <i class="bi bi-cash-stack"></i>
            </div>
            <div class="number text-success">
                <?php 
                $assetAccounts = array_filter($accounts, function($acc) { return $acc['account_type'] === 'asset'; });
                echo count($assetAccounts);
                ?>
            </div>
            <div class="label">حسابات الأصول</div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="border-right-color: #dc3545;">
            <div class="icon text-danger">
                <i class="bi bi-credit-card"></i>
            </div>
            <div class="number text-danger">
                <?php 
                $liabilityAccounts = array_filter($accounts, function($acc) { return $acc['account_type'] === 'liability'; });
                echo count($liabilityAccounts);
                ?>
            </div>
            <div class="label">حسابات الخصوم</div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="border-right-color: #17a2b8;">
            <div class="icon text-info">
                <i class="bi bi-graph-up"></i>
            </div>
            <div class="number text-info">
                <?php 
                $revenueAccounts = array_filter($accounts, function($acc) { return $acc['account_type'] === 'revenue'; });
                echo count($revenueAccounts);
                ?>
            </div>
            <div class="label">حسابات الإيرادات</div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="stats-card" style="border-right-color: #ffc107;">
            <div class="icon text-warning">
                <i class="bi bi-graph-down"></i>
            </div>
            <div class="number text-warning">
                <?php 
                $expenseAccounts = array_filter($accounts, function($acc) { return $acc['account_type'] === 'expense'; });
                echo count($expenseAccounts);
                ?>
            </div>
            <div class="label">حسابات المصروفات</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label for="search-input" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search-input" placeholder="البحث في الحسابات...">
            </div>
            <div class="col-md-3">
                <label for="type-filter" class="form-label">نوع الحساب</label>
                <select class="form-select" id="type-filter">
                    <option value="">جميع الأنواع</option>
                    <option value="asset">أصول</option>
                    <option value="liability">خصوم</option>
                    <option value="equity">حقوق الملكية</option>
                    <option value="revenue">إيرادات</option>
                    <option value="expense">مصروفات</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="level-filter" class="form-label">المستوى</label>
                <select class="form-select" id="level-filter">
                    <option value="">جميع المستويات</option>
                    <option value="1">المستوى الأول</option>
                    <option value="2">المستوى الثاني</option>
                    <option value="3">المستوى الثالث</option>
                    <option value="4">المستوى الرابع</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="bi bi-x-lg me-1"></i>
                        مسح الفلاتر
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول الحسابات -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة الحسابات
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="accounts-table">
                <thead>
                    <tr>
                        <th>رمز الحساب</th>
                        <th>اسم الحساب</th>
                        <th>النوع</th>
                        <th>الطبيعة</th>
                        <th>المستوى</th>
                        <th>الحساب الأب</th>
                        <th>الرصيد الحالي</th>
                        <th>الحالة</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($accounts as $account): ?>
                        <tr data-type="<?php echo $account['account_type']; ?>" 
                            data-level="<?php echo $account['level']; ?>"
                            data-search="<?php echo strtolower($account['account_code'] . ' ' . $account['account_name']); ?>">
                            <td>
                                <strong><?php echo htmlspecialchars($account['account_code']); ?></strong>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <?php 
                                    // إضافة مسافات للمستويات الفرعية
                                    for ($i = 1; $i < $account['level']; $i++) {
                                        echo '<span class="me-3"></span>';
                                    }
                                    
                                    if ($account['level'] > 1) {
                                        echo '<i class="bi bi-arrow-return-right text-muted me-2"></i>';
                                    }
                                    ?>
                                    <span><?php echo htmlspecialchars($account['account_name']); ?></span>
                                    <?php if ($account['is_main']): ?>
                                        <span class="badge bg-primary ms-2">رئيسي</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td>
                                <?php
                                $typeClass = '';
                                $typeText = '';
                                switch ($account['account_type']) {
                                    case 'asset':
                                        $typeClass = 'success';
                                        $typeText = 'أصول';
                                        break;
                                    case 'liability':
                                        $typeClass = 'danger';
                                        $typeText = 'خصوم';
                                        break;
                                    case 'equity':
                                        $typeClass = 'secondary';
                                        $typeText = 'حقوق الملكية';
                                        break;
                                    case 'revenue':
                                        $typeClass = 'info';
                                        $typeText = 'إيرادات';
                                        break;
                                    case 'expense':
                                        $typeClass = 'warning';
                                        $typeText = 'مصروفات';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo $account['account_nature'] === 'debit' ? 'primary' : 'secondary'; ?>">
                                    <?php echo $account['account_nature'] === 'debit' ? 'مدين' : 'دائن'; ?>
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-light text-dark"><?php echo $account['level']; ?></span>
                            </td>
                            <td>
                                <?php if ($account['parent_name']): ?>
                                    <small class="text-muted"><?php echo htmlspecialchars($account['parent_name']); ?></small>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <strong class="<?php echo $account['current_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                    <?php echo formatMoney($account['current_balance']); ?>
                                </strong>
                            </td>
                            <td>
                                <?php if ($account['is_active']): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="view.php?id=<?php echo $account['id']; ?>" 
                                       class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $account['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <?php if ($account['sub_accounts_count'] == 0 && $account['entries_count'] == 0): ?>
                                        <a href="?delete=1&id=<?php echo $account['id']; ?>&csrf_token=<?php echo generateCSRFToken(); ?>" 
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا الحساب؟')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    // تهيئة البحث والفلاتر
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-input');
        const typeFilter = document.getElementById('type-filter');
        const levelFilter = document.getElementById('level-filter');
        const tableRows = document.querySelectorAll('#accounts-table tbody tr');
        
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedType = typeFilter.value;
            const selectedLevel = levelFilter.value;
            
            tableRows.forEach(row => {
                const searchData = row.getAttribute('data-search');
                const rowType = row.getAttribute('data-type');
                const rowLevel = row.getAttribute('data-level');
                
                let showRow = true;
                
                // فلتر البحث
                if (searchTerm && !searchData.includes(searchTerm)) {
                    showRow = false;
                }
                
                // فلتر النوع
                if (selectedType && rowType !== selectedType) {
                    showRow = false;
                }
                
                // فلتر المستوى
                if (selectedLevel && rowLevel !== selectedLevel) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            });
        }
        
        searchInput.addEventListener('input', filterTable);
        typeFilter.addEventListener('change', filterTable);
        levelFilter.addEventListener('change', filterTable);
    });
    
    function clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('type-filter').value = '';
        document.getElementById('level-filter').value = '';
        
        // إظهار جميع الصفوف
        document.querySelectorAll('#accounts-table tbody tr').forEach(row => {
            row.style.display = '';
        });
    }
";

include __DIR__ . '/../../includes/footer.php';
?>
