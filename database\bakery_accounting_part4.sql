-- ===================================================
-- قاعدة بيانات نظام المحاسبة للمخبز - الجزء الرابع
-- Bakery Accounting System Database - Part 4
-- ===================================================

-- ===================================================
-- 19. جدول الأصول الثابتة
-- ===================================================
CREATE TABLE `fixed_assets` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_code` varchar(20) NOT NULL UNIQUE COMMENT 'رمز الأصل',
  `asset_name` varchar(255) NOT NULL COMMENT 'اسم الأصل',
  `asset_category` varchar(100) DEFAULT NULL COMMENT 'فئة الأصل',
  `purchase_date` date NOT NULL COMMENT 'تاريخ الشراء',
  `purchase_cost` decimal(15,3) NOT NULL COMMENT 'تكلفة الشراء',
  `useful_life_years` int(3) NOT NULL COMMENT 'العمر الإنتاجي بالسنوات',
  `depreciation_method` enum('straight_line','declining_balance','units_production') DEFAULT 'straight_line' COMMENT 'طريقة الإهلاك',
  `depreciation_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'معدل الإهلاك',
  `salvage_value` decimal(15,3) DEFAULT 0.000 COMMENT 'القيمة المتبقية',
  `accumulated_depreciation` decimal(15,3) DEFAULT 0.000 COMMENT 'مجمع الإهلاك',
  `book_value` decimal(15,3) DEFAULT 0.000 COMMENT 'القيمة الدفترية',
  `location` varchar(255) DEFAULT NULL COMMENT 'الموقع',
  `responsible_employee_id` int(11) DEFAULT NULL COMMENT 'الموظف المسؤول',
  `account_id` int(11) DEFAULT NULL COMMENT 'حساب الأصل',
  `depreciation_account_id` int(11) DEFAULT NULL COMMENT 'حساب مجمع الإهلاك',
  `expense_account_id` int(11) DEFAULT NULL COMMENT 'حساب مصروف الإهلاك',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_assets_responsible` (`responsible_employee_id`),
  KEY `fk_assets_account` (`account_id`),
  KEY `fk_assets_depreciation_account` (`depreciation_account_id`),
  KEY `fk_assets_expense_account` (`expense_account_id`),
  KEY `fk_assets_created_by` (`created_by`),
  CONSTRAINT `fk_assets_responsible` FOREIGN KEY (`responsible_employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `fk_assets_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_assets_depreciation_account` FOREIGN KEY (`depreciation_account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_assets_expense_account` FOREIGN KEY (`expense_account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_assets_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الأصول الثابتة';

-- ===================================================
-- 20. جدول إهلاك الأصول
-- ===================================================
CREATE TABLE `asset_depreciation` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `asset_id` int(11) NOT NULL COMMENT 'الأصل',
  `depreciation_date` date NOT NULL COMMENT 'تاريخ الإهلاك',
  `depreciation_amount` decimal(15,3) NOT NULL COMMENT 'مبلغ الإهلاك',
  `accumulated_depreciation` decimal(15,3) NOT NULL COMMENT 'مجمع الإهلاك',
  `book_value` decimal(15,3) NOT NULL COMMENT 'القيمة الدفترية',
  `journal_entry_id` int(11) DEFAULT NULL COMMENT 'القيد المحاسبي',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_depreciation_asset` (`asset_id`),
  KEY `fk_depreciation_journal` (`journal_entry_id`),
  KEY `fk_depreciation_created_by` (`created_by`),
  KEY `idx_depreciation_date` (`depreciation_date`),
  CONSTRAINT `fk_depreciation_asset` FOREIGN KEY (`asset_id`) REFERENCES `fixed_assets` (`id`),
  CONSTRAINT `fk_depreciation_journal` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`),
  CONSTRAINT `fk_depreciation_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إهلاك الأصول';

-- ===================================================
-- 21. جدول حركة المخزون
-- ===================================================
CREATE TABLE `inventory_movements` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `movement_date` date NOT NULL COMMENT 'تاريخ الحركة',
  `item_id` int(11) NOT NULL COMMENT 'الصنف',
  `movement_type` enum('in','out','transfer','adjustment','production') NOT NULL COMMENT 'نوع الحركة',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `quantity_in` decimal(10,3) DEFAULT 0.000 COMMENT 'الكمية الداخلة',
  `quantity_out` decimal(10,3) DEFAULT 0.000 COMMENT 'الكمية الخارجة',
  `unit_id` int(11) NOT NULL COMMENT 'الوحدة',
  `unit_cost` decimal(10,3) DEFAULT 0.000 COMMENT 'تكلفة الوحدة',
  `total_cost` decimal(15,3) DEFAULT 0.000 COMMENT 'إجمالي التكلفة',
  `balance_quantity` decimal(10,3) DEFAULT 0.000 COMMENT 'رصيد الكمية',
  `balance_value` decimal(15,3) DEFAULT 0.000 COMMENT 'رصيد القيمة',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_movements_item` (`item_id`),
  KEY `fk_movements_unit` (`unit_id`),
  KEY `fk_movements_created_by` (`created_by`),
  KEY `idx_movements_date` (`movement_date`),
  KEY `idx_movements_type` (`movement_type`),
  KEY `idx_movements_reference` (`reference_type`, `reference_id`),
  CONSTRAINT `fk_movements_item` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`),
  CONSTRAINT `fk_movements_unit` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `fk_movements_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='حركة المخزون';

-- ===================================================
-- 22. جدول عمليات الإنتاج
-- ===================================================
CREATE TABLE `production_orders` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `order_number` varchar(20) NOT NULL UNIQUE COMMENT 'رقم أمر الإنتاج',
  `order_date` date NOT NULL COMMENT 'تاريخ الأمر',
  `product_id` int(11) NOT NULL COMMENT 'المنتج',
  `quantity_to_produce` decimal(10,3) NOT NULL COMMENT 'الكمية المطلوب إنتاجها',
  `quantity_produced` decimal(10,3) DEFAULT 0.000 COMMENT 'الكمية المنتجة',
  `unit_id` int(11) NOT NULL COMMENT 'الوحدة',
  `total_material_cost` decimal(15,3) DEFAULT 0.000 COMMENT 'إجمالي تكلفة المواد',
  `total_labor_cost` decimal(15,3) DEFAULT 0.000 COMMENT 'إجمالي تكلفة العمالة',
  `total_overhead_cost` decimal(15,3) DEFAULT 0.000 COMMENT 'إجمالي التكاليف الإضافية',
  `total_production_cost` decimal(15,3) DEFAULT 0.000 COMMENT 'إجمالي تكلفة الإنتاج',
  `unit_production_cost` decimal(10,3) DEFAULT 0.000 COMMENT 'تكلفة الوحدة المنتجة',
  `status` enum('pending','in_progress','completed','cancelled') DEFAULT 'pending' COMMENT 'الحالة',
  `start_date` date DEFAULT NULL COMMENT 'تاريخ البدء',
  `completion_date` date DEFAULT NULL COMMENT 'تاريخ الإنجاز',
  `journal_entry_id` int(11) DEFAULT NULL COMMENT 'القيد المحاسبي',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_production_product` (`product_id`),
  KEY `fk_production_unit` (`unit_id`),
  KEY `fk_production_journal` (`journal_entry_id`),
  KEY `fk_production_created_by` (`created_by`),
  KEY `idx_production_date` (`order_date`),
  KEY `idx_production_status` (`status`),
  CONSTRAINT `fk_production_product` FOREIGN KEY (`product_id`) REFERENCES `items` (`id`),
  CONSTRAINT `fk_production_unit` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `fk_production_journal` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`),
  CONSTRAINT `fk_production_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='أوامر الإنتاج';

-- ===================================================
-- إعداد المفاتيح الخارجية
-- ===================================================
SET FOREIGN_KEY_CHECKS = 1;
