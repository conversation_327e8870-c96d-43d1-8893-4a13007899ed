            </main>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.3.6/js/buttons.print.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo BASE_URL; ?>/assets/js/app.js"></script>
    
    <!-- Additional JS -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <script>
        // تهيئة الشريط الجانبي للشاشات الصغيرة
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.getElementById('sidebar');
            
            if (sidebarToggle && sidebar) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
                
                // إغلاق الشريط الجانبي عند النقر خارجه
                document.addEventListener('click', function(e) {
                    if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                        sidebar.classList.remove('show');
                    }
                });
            }
            
            // تهيئة التلميحات
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
            
            // تهيئة النوافذ المنبثقة
            const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function(popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });
        
        // دالة لتأكيد الحذف
        function confirmDelete(message = 'هل أنت متأكد من الحذف؟') {
            return confirm(message);
        }
        
        // دالة لعرض رسالة نجاح
        function showSuccessMessage(message) {
            AppUtils.showAlert(message, 'success');
        }
        
        // دالة لعرض رسالة خطأ
        function showErrorMessage(message) {
            AppUtils.showAlert(message, 'error');
        }
        
        // دالة لإعادة تحميل الصفحة
        function reloadPage() {
            location.reload();
        }
        
        // دالة للانتقال إلى صفحة
        function goToPage(url) {
            window.location.href = url;
        }
        
        // تهيئة جداول البيانات
        $(document).ready(function() {
            // تهيئة جداول البيانات الأساسية
            if ($('.data-table').length > 0) {
                $('.data-table').each(function() {
                    const tableId = $(this).attr('id');
                    if (tableId) {
                        AppUtils.initDataTable(tableId);
                    }
                });
            }
            
            // تهيئة النماذج
            $('form').on('submit', function(e) {
                const form = this;
                const submitBtn = $(form).find('button[type="submit"]');
                
                // تعطيل الزر لمنع الإرسال المتكرر
                submitBtn.prop('disabled', true);
                
                // إعادة تفعيل الزر بعد ثانيتين
                setTimeout(function() {
                    submitBtn.prop('disabled', false);
                }, 2000);
            });
            
            // تهيئة حقول البحث
            $('.search-input').on('keyup', function() {
                const searchTerm = $(this).val().toLowerCase();
                const targetTable = $(this).data('target');
                
                if (targetTable) {
                    $(`#${targetTable} tbody tr`).each(function() {
                        const rowText = $(this).text().toLowerCase();
                        if (rowText.includes(searchTerm)) {
                            $(this).show();
                        } else {
                            $(this).hide();
                        }
                    });
                }
            });
        });
        
        // معلومات النظام
        console.log('نظام محاسبة المخبز v1.0.0 - تطوير AnwarSoft');
    </script>
    
    <!-- Inline Scripts -->
    <?php if (isset($inline_scripts)): ?>
        <script>
            <?php echo $inline_scripts; ?>
        </script>
    <?php endif; ?>
</body>
</html>
