// وظائف إدارة المنتجات والوصفات

// عرض المنتجات
function displayProducts(dataToShow = products) {
    const tbody = document.getElementById('productsTableBody');
    tbody.innerHTML = '';
    
    dataToShow.forEach(product => {
        const row = document.createElement('tr');
        
        const categoryText = {
            'bread': 'خبز',
            'pastry': 'معجنات',
            'cake': 'كعك وحلويات',
            'biscuit': 'بسكويت'
        }[product.category];
        
        const categoryClass = {
            'bread': 'primary',
            'pastry': 'success',
            'cake': 'warning',
            'biscuit': 'info'
        }[product.category];
        
        const unitText = {
            'piece': 'قطعة',
            'kg': 'كيلوجرام',
            'g': 'جرام',
            'box': 'صندوق',
            'dozen': 'دستة'
        }[product.unit];
        
        // حساب هامش الربح
        const totalCost = product.productionCost + product.laborCost + product.overheadCost;
        const profitMargin = totalCost > 0 ? ((product.sellingPrice - totalCost) / product.sellingPrice * 100).toFixed(1) : 0;
        
        row.innerHTML = `
            <td>
                <div class="product-image">${product.name.charAt(0)}</div>
            </td>
            <td>
                <strong>${product.name}</strong>
                <br><small class="text-muted">${product.description}</small>
            </td>
            <td><code>${product.code}</code></td>
            <td><span class="badge bg-${categoryClass}">${categoryText}</span></td>
            <td>${product.sellingPrice.toLocaleString()} ر.ي</td>
            <td>${totalCost.toLocaleString()} ر.ي</td>
            <td><span class="badge bg-${profitMargin > 30 ? 'success' : profitMargin > 15 ? 'warning' : 'danger'}">${profitMargin}%</span></td>
            <td><span class="badge bg-${product.isActive ? 'success' : 'secondary'}">${product.isActive ? 'نشط' : 'غير نشط'}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewProductDetails(${product.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="editProduct(${product.id})" title="تعديل">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="viewRecipe(${product.id})" title="الوصفة">
                        <i class="bi bi-list-check"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="produceProduct(${product.id})" title="إنتاج">
                        <i class="bi bi-gear"></i>
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// تحديث الإحصائيات
function updateStats() {
    const stats = {
        totalProducts: products.length,
        totalRecipes: recipes.length,
        avgProductionCost: 0,
        damagedProducts: 0
    };
    
    // حساب متوسط تكلفة الإنتاج
    const totalCost = products.reduce((sum, product) => {
        return sum + (product.productionCost + product.laborCost + product.overheadCost);
    }, 0);
    stats.avgProductionCost = products.length > 0 ? (totalCost / products.length).toFixed(0) : 0;
    
    // حساب المنتجات التالفة اليوم
    const today = new Date().toISOString().split('T')[0];
    stats.damagedProducts = damages.filter(damage => damage.date === today)
        .reduce((sum, damage) => sum + damage.quantity, 0);
    
    document.getElementById('totalProducts').textContent = stats.totalProducts;
    document.getElementById('totalRecipes').textContent = stats.totalRecipes;
    document.getElementById('avgProductionCost').textContent = stats.avgProductionCost.toLocaleString();
    document.getElementById('damagedProducts').textContent = stats.damagedProducts;
}

// فلترة المنتجات
function filterProducts() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    
    let filtered = products.filter(product => {
        const matchesSearch = !searchTerm || 
            product.name.toLowerCase().includes(searchTerm) ||
            product.code.toLowerCase().includes(searchTerm) ||
            product.description.toLowerCase().includes(searchTerm);
        
        const matchesCategory = !categoryFilter || product.category === categoryFilter;
        
        const matchesStatus = !statusFilter || 
            (statusFilter === 'active' && product.isActive) ||
            (statusFilter === 'inactive' && !product.isActive);
        
        return matchesSearch && matchesCategory && matchesStatus;
    });
    
    displayProducts(filtered);
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    displayProducts();
}

// إظهار نافذة إضافة منتج
function showAddProductModal() {
    document.getElementById('addProductForm').reset();
    document.getElementById('isActiveProduct').checked = true;
    const modal = new bootstrap.Modal(document.getElementById('addProductModal'));
    modal.show();
}

// حفظ منتج جديد
function saveProduct() {
    const newProduct = {
        id: products.length + 1,
        code: document.getElementById('productCode').value,
        name: document.getElementById('productName').value,
        category: document.getElementById('productCategory').value,
        unit: document.getElementById('productUnit').value,
        description: document.getElementById('productDescription').value,
        sellingPrice: parseFloat(document.getElementById('sellingPriceProduct').value),
        productionCost: parseFloat(document.getElementById('productionCost').value) || 0,
        laborCost: parseFloat(document.getElementById('laborCostProduct').value) || 0,
        overheadCost: parseFloat(document.getElementById('overheadCostProduct').value) || 0,
        shelfLife: parseInt(document.getElementById('shelfLife').value) || 1,
        isActive: document.getElementById('isActiveProduct').checked
    };
    
    products.push(newProduct);
    displayProducts();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addProductModal'));
    modal.hide();
    
    alert('تم إضافة المنتج بنجاح!');
}

// عرض تفاصيل المنتج
function viewProductDetails(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const totalCost = product.productionCost + product.laborCost + product.overheadCost;
    const profitMargin = totalCost > 0 ? ((product.sellingPrice - totalCost) / product.sellingPrice * 100).toFixed(1) : 0;
    
    alert(`تفاصيل المنتج:
الاسم: ${product.name}
الكود: ${product.code}
الفئة: ${product.category}
سعر البيع: ${product.sellingPrice} ر.ي
تكلفة الإنتاج: ${totalCost} ر.ي
هامش الربح: ${profitMargin}%
مدة الصلاحية: ${product.shelfLife} يوم`);
}

// تحميل قائمة المنتجات في النوافذ
function populateProductSelects() {
    const selects = ['recipeProductFilter', 'recipeProduct', 'productionProductSelect', 'damageProduct'];
    
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            const currentValue = select.value;
            select.innerHTML = '<option value="">اختر المنتج</option>';
            
            products.filter(p => p.isActive).forEach(product => {
                const option = document.createElement('option');
                option.value = product.id;
                option.textContent = `${product.name} (${product.code})`;
                select.appendChild(option);
            });
            
            if (currentValue) select.value = currentValue;
        }
    });
}

// تحميل قائمة المكونات من المخزون
function populateIngredientSelect() {
    const select = document.getElementById('ingredientSelect');
    if (!select) return;
    
    select.innerHTML = '<option value="">اختر المكون</option>';
    
    // استخدام بيانات المخزون من inventory.html
    if (typeof items !== 'undefined') {
        items.filter(item => item.isActive).forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} (${item.code})`;
            option.dataset.unitPrice = item.costPrice || 0;
            select.appendChild(option);
        });
    }
}

// تحويل الوحدات
function convertUnits() {
    const fromValue = parseFloat(document.getElementById('fromValue').value) || 0;
    const fromUnit = document.getElementById('fromUnit').value;
    const toUnit = document.getElementById('toUnit').value;
    
    let result = 0;
    
    // تحويل الوزن
    if (['kg', 'g'].includes(fromUnit) && ['kg', 'g'].includes(toUnit)) {
        const fromGrams = fromValue * unitConversions.weight[fromUnit];
        result = fromGrams / unitConversions.weight[toUnit];
    }
    // تحويل الحجم
    else if (['l', 'ml'].includes(fromUnit) && ['l', 'ml'].includes(toUnit)) {
        const fromMl = fromValue * unitConversions.volume[fromUnit];
        result = fromMl / unitConversions.volume[toUnit];
    }
    // نفس الوحدة
    else if (fromUnit === toUnit) {
        result = fromValue;
    }
    
    document.getElementById('toValue').value = result.toFixed(3);
}

// تحويل سريع في النافذة المنبثقة
function quickConvert() {
    const fromValue = parseFloat(document.getElementById('quickFromValue').value) || 0;
    const fromUnit = document.getElementById('quickFromUnit').value;
    const toUnit = document.getElementById('quickToUnit').value;
    
    let result = 0;
    
    // تحويل الوزن
    if (['kg', 'g'].includes(fromUnit) && ['kg', 'g'].includes(toUnit)) {
        const fromGrams = fromValue * unitConversions.weight[fromUnit];
        result = fromGrams / unitConversions.weight[toUnit];
    }
    // تحويل الحجم
    else if (['l', 'ml'].includes(fromUnit) && ['l', 'ml'].includes(toUnit)) {
        const fromMl = fromValue * unitConversions.volume[fromUnit];
        result = fromMl / unitConversions.volume[toUnit];
    }
    // نفس الوحدة
    else if (fromUnit === toUnit) {
        result = fromValue;
    }
    
    document.getElementById('quickToValue').value = result.toFixed(3);
}

// إظهار نافذة محول الوحدات
function showUnitConverterModal() {
    const modal = new bootstrap.Modal(document.getElementById('unitConverterModal'));
    modal.show();
}

// تحديث البيانات
function refreshData() {
    displayProducts();
    updateStats();
    populateProductSelects();
    populateIngredientSelect();
    alert('تم تحديث البيانات!');
}

// تهيئة الصفحة
function initializePage() {
    // التحقق من المصادقة
    const user = checkAuth();
    if (user) {
        document.getElementById('sidebarUserName').textContent = user.name;
    }
    
    // استعادة حالة القائمة الجانبية
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        document.getElementById('sidebar').classList.add('collapsed');
        document.getElementById('mainContent').classList.add('expanded');
    }
    
    // عرض البيانات
    displayProducts();
    updateStats();
    populateProductSelects();
    populateIngredientSelect();
    
    // تعيين التواريخ الافتراضية
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('productionDate').value = today;
    document.getElementById('damageDate').value = today;
    document.getElementById('productionDateInput').value = today;
    document.getElementById('damageDateInput').value = today;
    
    // ربط أحداث الفلترة
    document.getElementById('searchInput').addEventListener('input', filterProducts);
    document.getElementById('categoryFilter').addEventListener('change', filterProducts);
    document.getElementById('statusFilter').addEventListener('change', filterProducts);
    
    // ربط أحداث تحويل الوحدات
    document.getElementById('fromValue').addEventListener('input', convertUnits);
    document.getElementById('fromUnit').addEventListener('change', convertUnits);
    document.getElementById('toUnit').addEventListener('change', convertUnits);
    
    // تأثيرات بصرية
    const cards = document.querySelectorAll('.stats-card, .card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});
