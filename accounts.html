<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>شجرة الحسابات المحاسبية</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
        }
        
        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        
        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        
        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }
        
        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }
        
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
        }
        
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        
        .account-level-1 {
            font-weight: bold;
            background-color: #f8f9fa;
        }
        
        .account-level-2 {
            padding-right: 2rem;
        }
        
        .account-level-3 {
            padding-right: 4rem;
        }
        
        .account-level-4 {
            padding-right: 6rem;
        }
        
        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
        
        .action-buttons .btn {
            margin: 0 2px;
        }
    </style>
</head>
<body>
    <!-- شريط التنقل -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard-admin.html">
                <i class="bi bi-shop me-2"></i>نظام محاسبة المخبز
            </a>
            
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="dashboard-admin.html">
                    <i class="bi bi-speedometer2 me-1"></i>لوحة التحكم
                </a>
                <a class="nav-link active" href="accounts.html">
                    <i class="bi bi-diagram-3 me-1"></i>شجرة الحسابات
                </a>
                <a class="nav-link" href="users.html">
                    <i class="bi bi-people me-1"></i>المستخدمين
                </a>
            </div>
            
            <div class="navbar-nav">
                <a class="nav-link" href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right me-1"></i>خروج
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <!-- العنوان والأزرار -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-diagram-3 text-primary me-2"></i>
                شجرة الحسابات المحاسبية
            </h1>
            <div class="btn-group">
                <button type="button" class="btn btn-primary" onclick="showAddAccountModal()">
                    <i class="bi bi-plus-lg me-1"></i>إضافة حساب جديد
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="refreshAccounts()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
            </div>
        </div>
        
        <!-- إحصائيات الحسابات -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #28a745;">
                    <div class="icon text-success">
                        <i class="bi bi-cash-stack"></i>
                    </div>
                    <div class="number text-success" id="assetAccounts">15</div>
                    <div class="label">حسابات الأصول</div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #dc3545;">
                    <div class="icon text-danger">
                        <i class="bi bi-credit-card"></i>
                    </div>
                    <div class="number text-danger" id="liabilityAccounts">8</div>
                    <div class="label">حسابات الخصوم</div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #17a2b8;">
                    <div class="icon text-info">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="number text-info" id="revenueAccounts">12</div>
                    <div class="label">حسابات الإيرادات</div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #ffc107;">
                    <div class="icon text-warning">
                        <i class="bi bi-graph-down"></i>
                    </div>
                    <div class="number text-warning" id="expenseAccounts">18</div>
                    <div class="label">حسابات المصروفات</div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #6f42c1;">
                    <div class="icon text-primary">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="number text-primary" id="equityAccounts">5</div>
                    <div class="label">حقوق الملكية</div>
                </div>
            </div>
            
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #20c997;">
                    <div class="icon text-success">
                        <i class="bi bi-list-ol"></i>
                    </div>
                    <div class="number text-success" id="totalAccounts">58</div>
                    <div class="label">إجمالي الحسابات</div>
                </div>
            </div>
        </div>
        
        <!-- فلاتر البحث -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="searchInput" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث في الحسابات...">
                    </div>
                    <div class="col-md-3">
                        <label for="typeFilter" class="form-label">نوع الحساب</label>
                        <select class="form-select" id="typeFilter">
                            <option value="">جميع الأنواع</option>
                            <option value="asset">أصول</option>
                            <option value="liability">خصوم</option>
                            <option value="equity">حقوق الملكية</option>
                            <option value="revenue">إيرادات</option>
                            <option value="expense">مصروفات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="levelFilter" class="form-label">المستوى</label>
                        <select class="form-select" id="levelFilter">
                            <option value="">جميع المستويات</option>
                            <option value="1">المستوى الأول</option>
                            <option value="2">المستوى الثاني</option>
                            <option value="3">المستوى الثالث</option>
                            <option value="4">المستوى الرابع</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="bi bi-x-lg me-1"></i>مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- جدول الحسابات -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul me-2"></i>قائمة الحسابات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="accountsTable">
                        <thead>
                            <tr>
                                <th>رمز الحساب</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الطبيعة</th>
                                <th>المستوى</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody id="accountsTableBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- نافذة إضافة حساب جديد -->
    <div class="modal fade" id="addAccountModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة حساب جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addAccountForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountCode" class="form-label">رمز الحساب *</label>
                                    <input type="text" class="form-control" id="accountCode" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="accountName" class="form-label">اسم الحساب *</label>
                                    <input type="text" class="form-control" id="accountName" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="accountType" class="form-label">نوع الحساب *</label>
                                    <select class="form-select" id="accountType" required>
                                        <option value="">اختر النوع</option>
                                        <option value="asset">أصول</option>
                                        <option value="liability">خصوم</option>
                                        <option value="equity">حقوق الملكية</option>
                                        <option value="revenue">إيرادات</option>
                                        <option value="expense">مصروفات</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="accountNature" class="form-label">طبيعة الحساب *</label>
                                    <select class="form-select" id="accountNature" required>
                                        <option value="">اختر الطبيعة</option>
                                        <option value="debit">مدين</option>
                                        <option value="credit">دائن</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="parentAccount" class="form-label">الحساب الأب</label>
                                    <select class="form-select" id="parentAccount">
                                        <option value="">لا يوجد (حساب رئيسي)</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="openingBalance" class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" class="form-control" id="openingBalance" step="0.001" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="accountDescription" class="form-label">الوصف</label>
                            <textarea class="form-control" id="accountDescription" rows="3"></textarea>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" checked>
                            <label class="form-check-label" for="isActive">
                                حساب نشط
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveAccount()">حفظ الحساب</button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // بيانات الحسابات التجريبية
        let accounts = [
            {
                id: 1,
                code: '1000',
                name: 'الأصول',
                type: 'asset',
                nature: 'debit',
                level: 1,
                parent_id: null,
                balance: 250000,
                is_active: true,
                is_main: true
            },
            {
                id: 2,
                code: '1100',
                name: 'الأصول المتداولة',
                type: 'asset',
                nature: 'debit',
                level: 2,
                parent_id: 1,
                balance: 180000,
                is_active: true,
                is_main: false
            },
            {
                id: 3,
                code: '1110',
                name: 'النقدية والبنوك',
                type: 'asset',
                nature: 'debit',
                level: 3,
                parent_id: 2,
                balance: 85000,
                is_active: true,
                is_main: false
            },
            {
                id: 4,
                code: '1111',
                name: 'الصندوق الرئيسي',
                type: 'asset',
                nature: 'debit',
                level: 4,
                parent_id: 3,
                balance: 25000,
                is_active: true,
                is_main: false
            },
            {
                id: 5,
                code: '2000',
                name: 'الخصوم',
                type: 'liability',
                nature: 'credit',
                level: 1,
                parent_id: null,
                balance: 120000,
                is_active: true,
                is_main: true
            },
            {
                id: 6,
                code: '4000',
                name: 'الإيرادات',
                type: 'revenue',
                nature: 'credit',
                level: 1,
                parent_id: null,
                balance: 350000,
                is_active: true,
                is_main: true
            },
            {
                id: 7,
                code: '5000',
                name: 'المصروفات',
                type: 'expense',
                nature: 'debit',
                level: 1,
                parent_id: null,
                balance: 180000,
                is_active: true,
                is_main: true
            }
        ];
        
        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }
        
        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }
        
        // عرض الحسابات
        function displayAccounts(accountsToShow = accounts) {
            const tbody = document.getElementById('accountsTableBody');
            tbody.innerHTML = '';
            
            accountsToShow.forEach(account => {
                const row = document.createElement('tr');
                row.className = `account-level-${account.level}`;
                
                const indent = '&nbsp;'.repeat((account.level - 1) * 4);
                const levelIcon = account.level > 1 ? '<i class="bi bi-arrow-return-right text-muted me-2"></i>' : '';
                
                const typeClass = {
                    'asset': 'success',
                    'liability': 'danger',
                    'equity': 'secondary',
                    'revenue': 'info',
                    'expense': 'warning'
                }[account.type];
                
                const typeText = {
                    'asset': 'أصول',
                    'liability': 'خصوم',
                    'equity': 'حقوق الملكية',
                    'revenue': 'إيرادات',
                    'expense': 'مصروفات'
                }[account.type];
                
                row.innerHTML = `
                    <td><strong>${account.code}</strong></td>
                    <td>
                        ${indent}${levelIcon}${account.name}
                        ${account.is_main ? '<span class="badge bg-primary ms-2">رئيسي</span>' : ''}
                    </td>
                    <td><span class="badge bg-${typeClass}">${typeText}</span></td>
                    <td><span class="badge bg-${account.nature === 'debit' ? 'primary' : 'secondary'}">${account.nature === 'debit' ? 'مدين' : 'دائن'}</span></td>
                    <td><span class="badge bg-light text-dark">${account.level}</span></td>
                    <td><strong class="${account.balance >= 0 ? 'text-success' : 'text-danger'}">${account.balance.toLocaleString()} ر.ي</strong></td>
                    <td><span class="badge bg-${account.is_active ? 'success' : 'secondary'}">${account.is_active ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" onclick="viewAccount(${account.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="editAccount(${account.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            ${!account.is_main ? `
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteAccount(${account.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;
                
                tbody.appendChild(row);
            });
        }
        
        // تحديث الإحصائيات
        function updateStats() {
            const stats = {
                asset: accounts.filter(a => a.type === 'asset').length,
                liability: accounts.filter(a => a.type === 'liability').length,
                equity: accounts.filter(a => a.type === 'equity').length,
                revenue: accounts.filter(a => a.type === 'revenue').length,
                expense: accounts.filter(a => a.type === 'expense').length,
                total: accounts.length
            };
            
            document.getElementById('assetAccounts').textContent = stats.asset;
            document.getElementById('liabilityAccounts').textContent = stats.liability;
            document.getElementById('equityAccounts').textContent = stats.equity;
            document.getElementById('revenueAccounts').textContent = stats.revenue;
            document.getElementById('expenseAccounts').textContent = stats.expense;
            document.getElementById('totalAccounts').textContent = stats.total;
        }
        
        // فلترة الحسابات
        function filterAccounts() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            const levelFilter = document.getElementById('levelFilter').value;
            
            let filtered = accounts.filter(account => {
                const matchesSearch = !searchTerm || 
                    account.code.toLowerCase().includes(searchTerm) ||
                    account.name.toLowerCase().includes(searchTerm);
                
                const matchesType = !typeFilter || account.type === typeFilter;
                const matchesLevel = !levelFilter || account.level.toString() === levelFilter;
                
                return matchesSearch && matchesType && matchesLevel;
            });
            
            displayAccounts(filtered);
        }
        
        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('levelFilter').value = '';
            displayAccounts();
        }
        
        // إظهار نافذة إضافة حساب
        function showAddAccountModal() {
            const modal = new bootstrap.Modal(document.getElementById('addAccountModal'));
            modal.show();
        }
        
        // حفظ حساب جديد
        function saveAccount() {
            const form = document.getElementById('addAccountForm');
            const formData = new FormData(form);
            
            const newAccount = {
                id: accounts.length + 1,
                code: document.getElementById('accountCode').value,
                name: document.getElementById('accountName').value,
                type: document.getElementById('accountType').value,
                nature: document.getElementById('accountNature').value,
                level: document.getElementById('parentAccount').value ? 2 : 1,
                parent_id: document.getElementById('parentAccount').value || null,
                balance: parseFloat(document.getElementById('openingBalance').value) || 0,
                is_active: document.getElementById('isActive').checked,
                is_main: false
            };
            
            accounts.push(newAccount);
            displayAccounts();
            updateStats();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('addAccountModal'));
            modal.hide();
            
            // إعادة تعيين النموذج
            form.reset();
            
            alert('تم إضافة الحساب بنجاح!');
        }
        
        // عرض تفاصيل الحساب
        function viewAccount(id) {
            const account = accounts.find(a => a.id === id);
            if (account) {
                alert(`تفاصيل الحساب:\nالرمز: ${account.code}\nالاسم: ${account.name}\nالرصيد: ${account.balance.toLocaleString()} ر.ي`);
            }
        }
        
        // تعديل الحساب
        function editAccount(id) {
            alert('سيتم فتح نافذة التعديل قريباً');
        }
        
        // حذف الحساب
        function deleteAccount(id) {
            if (confirm('هل أنت متأكد من حذف هذا الحساب؟')) {
                accounts = accounts.filter(a => a.id !== id);
                displayAccounts();
                updateStats();
                alert('تم حذف الحساب بنجاح!');
            }
        }
        
        // تحديث الحسابات
        function refreshAccounts() {
            displayAccounts();
            updateStats();
            alert('تم تحديث البيانات!');
        }
        
        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            displayAccounts();
            updateStats();
            
            // ربط أحداث الفلترة
            document.getElementById('searchInput').addEventListener('input', filterAccounts);
            document.getElementById('typeFilter').addEventListener('change', filterAccounts);
            document.getElementById('levelFilter').addEventListener('change', filterAccounts);
            
            // تحديد طبيعة الحساب تلقائياً
            document.getElementById('accountType').addEventListener('change', function() {
                const type = this.value;
                const natureSelect = document.getElementById('accountNature');
                
                if (type === 'asset' || type === 'expense') {
                    natureSelect.value = 'debit';
                } else if (type === 'liability' || type === 'equity' || type === 'revenue') {
                    natureSelect.value = 'credit';
                }
            });
        });
    </script>
</body>
</html>
