<?php
/**
 * إدارة المستخدمين
 * Users Management
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('users');

$page_title = 'إدارة المستخدمين';

// معالجة الحذف
if (isset($_GET['delete']) && isset($_GET['id'])) {
    checkCSRF();
    
    try {
        $userId = intval($_GET['id']);
        
        // منع حذف المستخدم الحالي
        if ($userId == $_SESSION['user_id']) {
            throw new Exception('لا يمكن حذف المستخدم الحالي');
        }
        
        // منع حذف المدير الرئيسي
        $user = queryOne("SELECT role FROM users WHERE id = ?", [$userId]);
        if ($user && $user['role'] === 'admin' && $userId == 1) {
            throw new Exception('لا يمكن حذف المدير الرئيسي');
        }
        
        // الحصول على بيانات المستخدم قبل الحذف
        $userData = queryOne("SELECT * FROM users WHERE id = ?", [$userId]);
        
        if (!$userData) {
            throw new Exception('المستخدم غير موجود');
        }
        
        // حذف المستخدم
        execute("DELETE FROM users WHERE id = ?", [$userId]);
        
        // تسجيل النشاط
        logUserActivity('delete', 'users', $userId, $userData, null);
        
        showMessage('تم حذف المستخدم بنجاح', 'success');
        
    } catch (Exception $e) {
        showMessage($e->getMessage(), 'error');
        logError("خطأ في حذف المستخدم: " . $e->getMessage());
    }
    
    header('Location: index.php');
    exit();
}

// معالجة تفعيل/إلغاء تفعيل المستخدم
if (isset($_GET['toggle']) && isset($_GET['id'])) {
    checkCSRF();
    
    try {
        $userId = intval($_GET['id']);
        
        // منع تعطيل المستخدم الحالي
        if ($userId == $_SESSION['user_id']) {
            throw new Exception('لا يمكن تعطيل المستخدم الحالي');
        }
        
        // منع تعطيل المدير الرئيسي
        if ($userId == 1) {
            throw new Exception('لا يمكن تعطيل المدير الرئيسي');
        }
        
        $user = queryOne("SELECT is_active FROM users WHERE id = ?", [$userId]);
        
        if (!$user) {
            throw new Exception('المستخدم غير موجود');
        }
        
        $newStatus = $user['is_active'] ? 0 : 1;
        
        execute("UPDATE users SET is_active = ? WHERE id = ?", [$newStatus, $userId]);
        
        $action = $newStatus ? 'تم تفعيل المستخدم' : 'تم إلغاء تفعيل المستخدم';
        showMessage($action . ' بنجاح', 'success');
        
        // تسجيل النشاط
        logUserActivity($newStatus ? 'activate' : 'deactivate', 'users', $userId);
        
    } catch (Exception $e) {
        showMessage($e->getMessage(), 'error');
        logError("خطأ في تغيير حالة المستخدم: " . $e->getMessage());
    }
    
    header('Location: index.php');
    exit();
}

// الحصول على جميع المستخدمين
try {
    $users = query(
        "SELECT u.*, creator.full_name as created_by_name
         FROM users u
         LEFT JOIN users creator ON u.created_by = creator.id
         ORDER BY u.created_at DESC"
    );
    
    // إحصائيات المستخدمين
    $stats = [
        'total' => count($users),
        'active' => count(array_filter($users, function($u) { return $u['is_active']; })),
        'inactive' => count(array_filter($users, function($u) { return !$u['is_active']; })),
        'admins' => count(array_filter($users, function($u) { return $u['role'] === 'admin'; })),
        'managers' => count(array_filter($users, function($u) { return $u['role'] === 'manager'; })),
        'accountants' => count(array_filter($users, function($u) { return $u['role'] === 'accountant'; })),
        'cashiers' => count(array_filter($users, function($u) { return $u['role'] === 'cashier'; })),
        'users' => count(array_filter($users, function($u) { return $u['role'] === 'user'; }))
    ];
    
} catch (Exception $e) {
    $users = [];
    $stats = ['total' => 0, 'active' => 0, 'inactive' => 0, 'admins' => 0, 'managers' => 0, 'accountants' => 0, 'cashiers' => 0, 'users' => 0];
    logError("خطأ في تحميل المستخدمين: " . $e->getMessage());
    showMessage('خطأ في تحميل البيانات', 'error');
}

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-people text-primary me-2"></i>
        إدارة المستخدمين
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="create.php" class="btn btn-primary">
                <i class="bi bi-person-plus me-1"></i>
                إضافة مستخدم جديد
            </a>
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mb-4">
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="border-right-color: #17a2b8;">
            <div class="icon text-info">
                <i class="bi bi-people"></i>
            </div>
            <div class="number text-info"><?php echo $stats['total']; ?></div>
            <div class="label">إجمالي المستخدمين</div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="border-right-color: #28a745;">
            <div class="icon text-success">
                <i class="bi bi-person-check"></i>
            </div>
            <div class="number text-success"><?php echo $stats['active']; ?></div>
            <div class="label">نشط</div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="border-right-color: #dc3545;">
            <div class="icon text-danger">
                <i class="bi bi-person-x"></i>
            </div>
            <div class="number text-danger"><?php echo $stats['inactive']; ?></div>
            <div class="label">غير نشط</div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="border-right-color: #6f42c1;">
            <div class="icon text-primary">
                <i class="bi bi-shield-check"></i>
            </div>
            <div class="number text-primary"><?php echo $stats['admins']; ?></div>
            <div class="label">مديرين</div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="border-right-color: #fd7e14;">
            <div class="icon text-warning">
                <i class="bi bi-calculator"></i>
            </div>
            <div class="number text-warning"><?php echo $stats['accountants']; ?></div>
            <div class="label">محاسبين</div>
        </div>
    </div>
    
    <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
        <div class="stats-card" style="border-right-color: #20c997;">
            <div class="icon text-info">
                <i class="bi bi-cash-stack"></i>
            </div>
            <div class="number text-info"><?php echo $stats['cashiers']; ?></div>
            <div class="label">أمناء صندوق</div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <label for="search-input" class="form-label">البحث</label>
                <input type="text" class="form-control" id="search-input" placeholder="البحث في المستخدمين...">
            </div>
            <div class="col-md-3">
                <label for="role-filter" class="form-label">الدور</label>
                <select class="form-select" id="role-filter">
                    <option value="">جميع الأدوار</option>
                    <?php foreach (USER_ROLES as $key => $value): ?>
                        <option value="<?php echo $key; ?>"><?php echo $value; ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="status-filter" class="form-label">الحالة</label>
                <select class="form-select" id="status-filter">
                    <option value="">جميع الحالات</option>
                    <option value="1">نشط</option>
                    <option value="0">غير نشط</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                        <i class="bi bi-x-lg me-1"></i>
                        مسح الفلاتر
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="bi bi-list-ul me-2"></i>
            قائمة المستخدمين
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="users-table">
                <thead>
                    <tr>
                        <th>الصورة</th>
                        <th>اسم المستخدم</th>
                        <th>الاسم الكامل</th>
                        <th>البريد الإلكتروني</th>
                        <th>الهاتف</th>
                        <th>الدور</th>
                        <th>آخر دخول</th>
                        <th>الحالة</th>
                        <th>العمليات</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($users as $user): ?>
                        <tr data-role="<?php echo $user['role']; ?>" 
                            data-status="<?php echo $user['is_active']; ?>"
                            data-search="<?php echo strtolower($user['username'] . ' ' . $user['full_name'] . ' ' . $user['email']); ?>">
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" 
                                         style="width: 40px; height: 40px;">
                                        <i class="bi bi-person"></i>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <strong><?php echo htmlspecialchars($user['username']); ?></strong>
                                <?php if ($user['id'] == $_SESSION['user_id']): ?>
                                    <span class="badge bg-info ms-1">أنت</span>
                                <?php endif; ?>
                                <?php if ($user['id'] == 1): ?>
                                    <span class="badge bg-warning ms-1">رئيسي</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                            <td>
                                <?php if ($user['email']): ?>
                                    <a href="mailto:<?php echo htmlspecialchars($user['email']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($user['email']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['phone']): ?>
                                    <a href="tel:<?php echo htmlspecialchars($user['phone']); ?>" class="text-decoration-none">
                                        <?php echo htmlspecialchars($user['phone']); ?>
                                    </a>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php
                                $roleClass = '';
                                $roleText = '';
                                switch ($user['role']) {
                                    case 'admin':
                                        $roleClass = 'danger';
                                        $roleText = 'مدير النظام';
                                        break;
                                    case 'manager':
                                        $roleClass = 'primary';
                                        $roleText = 'مدير';
                                        break;
                                    case 'accountant':
                                        $roleClass = 'warning';
                                        $roleText = 'محاسب';
                                        break;
                                    case 'cashier':
                                        $roleClass = 'info';
                                        $roleText = 'أمين صندوق';
                                        break;
                                    case 'user':
                                        $roleClass = 'secondary';
                                        $roleText = 'مستخدم';
                                        break;
                                }
                                ?>
                                <span class="badge bg-<?php echo $roleClass; ?>"><?php echo $roleText; ?></span>
                            </td>
                            <td>
                                <?php if ($user['last_login']): ?>
                                    <small class="text-muted">
                                        <?php echo formatDateTime($user['last_login']); ?>
                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">لم يسجل دخول</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($user['is_active']): ?>
                                    <span class="badge bg-success">نشط</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="action-buttons">
                                    <a href="view.php?id=<?php echo $user['id']; ?>" 
                                       class="btn btn-sm btn-outline-info" title="عرض">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                    <a href="edit.php?id=<?php echo $user['id']; ?>" 
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <?php if ($user['id'] != $_SESSION['user_id'] && $user['id'] != 1): ?>
                                        <a href="?toggle=1&id=<?php echo $user['id']; ?>&csrf_token=<?php echo generateCSRFToken(); ?>" 
                                           class="btn btn-sm btn-outline-<?php echo $user['is_active'] ? 'warning' : 'success'; ?>" 
                                           title="<?php echo $user['is_active'] ? 'إلغاء تفعيل' : 'تفعيل'; ?>">
                                            <i class="bi bi-<?php echo $user['is_active'] ? 'person-x' : 'person-check'; ?>"></i>
                                        </a>
                                        <a href="?delete=1&id=<?php echo $user['id']; ?>&csrf_token=<?php echo generateCSRFToken(); ?>" 
                                           class="btn btn-sm btn-outline-danger" title="حذف"
                                           onclick="return confirmDelete('هل أنت متأكد من حذف هذا المستخدم؟')">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    // تهيئة البحث والفلاتر
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('search-input');
        const roleFilter = document.getElementById('role-filter');
        const statusFilter = document.getElementById('status-filter');
        const tableRows = document.querySelectorAll('#users-table tbody tr');
        
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedRole = roleFilter.value;
            const selectedStatus = statusFilter.value;
            
            tableRows.forEach(row => {
                const searchData = row.getAttribute('data-search');
                const rowRole = row.getAttribute('data-role');
                const rowStatus = row.getAttribute('data-status');
                
                let showRow = true;
                
                // فلتر البحث
                if (searchTerm && !searchData.includes(searchTerm)) {
                    showRow = false;
                }
                
                // فلتر الدور
                if (selectedRole && rowRole !== selectedRole) {
                    showRow = false;
                }
                
                // فلتر الحالة
                if (selectedStatus && rowStatus !== selectedStatus) {
                    showRow = false;
                }
                
                row.style.display = showRow ? '' : 'none';
            });
        }
        
        searchInput.addEventListener('input', filterTable);
        roleFilter.addEventListener('change', filterTable);
        statusFilter.addEventListener('change', filterTable);
    });
    
    function clearFilters() {
        document.getElementById('search-input').value = '';
        document.getElementById('role-filter').value = '';
        document.getElementById('status-filter').value = '';
        
        // إظهار جميع الصفوف
        document.querySelectorAll('#users-table tbody tr').forEach(row => {
            row.style.display = '';
        });
    }
";

include __DIR__ . '/../../includes/footer.php';
?>
