<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>القيود المحاسبية الذكية</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .journal-entry {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid #667eea;
        }

        .journal-entry-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .debit-entry {
            color: #dc3545;
            font-weight: bold;
        }

        .credit-entry {
            color: #28a745;
            font-weight: bold;
        }

        .entry-balanced {
            background: #d4edda;
            border-left-color: #28a745;
        }

        .entry-unbalanced {
            background: #f8d7da;
            border-left-color: #dc3545;
        }

        .smart-entry-indicator {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            font-weight: 600;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 2rem;
        }

        .account-selector {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .balance-indicator {
            font-size: 1.2rem;
            font-weight: bold;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            text-align: center;
        }

        .balanced {
            background: #d4edda;
            color: #155724;
        }

        .unbalanced {
            background: #f8d7da;
            color: #721c24;
        }

        @media print {
            .sidebar, .btn, .action-buttons {
                display: none !important;
            }
            .main-content {
                margin-right: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="products.html">
                    <i class="bi bi-basket"></i>
                    <span class="menu-text">المنتجات والوصفات</span>
                </a>
            </li>

            <li>
                <a href="journal-entries.html" class="active">
                    <i class="bi bi-journal-text"></i>
                    <span class="menu-text">القيود المحاسبية</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-journal-text text-primary me-2"></i>
                    القيود المحاسبية الذكية
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showJournalEntryModal()">
                        <i class="bi bi-plus-lg me-1"></i>قيد جديد
                    </button>
                    <button type="button" class="btn btn-success" onclick="showSmartEntryModal()">
                        <i class="bi bi-magic me-1"></i>قيد ذكي
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showReviewModal()">
                        <i class="bi bi-check-circle me-1"></i>مراجعة
                    </button>
                    <button type="button" class="btn btn-info" onclick="showAuditLogModal()">
                        <i class="bi bi-clock-history me-1"></i>سجل التغييرات
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>

            <!-- إحصائيات القيود -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #28a745;">
                        <div class="icon text-success">
                            <i class="bi bi-journal-text"></i>
                        </div>
                        <div class="number text-success" id="totalEntries">45</div>
                        <div class="label">إجمالي القيود</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #17a2b8;">
                        <div class="icon text-info">
                            <i class="bi bi-magic"></i>
                        </div>
                        <div class="number text-info" id="smartEntries">32</div>
                        <div class="label">القيود الذكية</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #ffc107;">
                        <div class="icon text-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="number text-warning" id="pendingReview">8</div>
                        <div class="label">في انتظار المراجعة</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #dc3545;">
                        <div class="icon text-danger">
                            <i class="bi bi-x-circle"></i>
                        </div>
                        <div class="number text-danger" id="unbalancedEntries">2</div>
                        <div class="label">قيود غير متوازنة</div>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="card">
                <div class="card-header p-0">
                    <ul class="nav nav-tabs" id="journalTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="entries-tab" data-bs-toggle="tab" data-bs-target="#entries" type="button" role="tab">
                                <i class="bi bi-journal-text me-2"></i>القيود
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="smart-tab" data-bs-toggle="tab" data-bs-target="#smart" type="button" role="tab">
                                <i class="bi bi-magic me-2"></i>القيود الذكية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="review-tab" data-bs-toggle="tab" data-bs-target="#review" type="button" role="tab">
                                <i class="bi bi-check-circle me-2"></i>المراجعة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="audit-tab" data-bs-toggle="tab" data-bs-target="#audit" type="button" role="tab">
                                <i class="bi bi-clock-history me-2"></i>سجل التغييرات
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="journalTabContent">
                    <!-- تبويب القيود -->
                    <div class="tab-pane fade show active" id="entries" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="entryDateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="entryDateFrom">
                            </div>
                            <div class="col-md-3">
                                <label for="entryDateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="entryDateTo">
                            </div>
                            <div class="col-md-3">
                                <label for="entryTypeFilter" class="form-label">نوع القيد</label>
                                <select class="form-select" id="entryTypeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="manual">يدوي</option>
                                    <option value="smart">ذكي</option>
                                    <option value="auto">تلقائي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="entryStatusFilter" class="form-label">الحالة</label>
                                <select class="form-select" id="entryStatusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="draft">مسودة</option>
                                    <option value="pending">في انتظار المراجعة</option>
                                    <option value="approved">معتمد</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="entrySearchInput" placeholder="البحث في القيود...">
                            </div>
                            <div class="col-md-6">
                                <div class="btn-group w-100">
                                    <button type="button" class="btn btn-outline-primary" onclick="searchEntries()">
                                        <i class="bi bi-search me-1"></i>بحث
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearEntryFilters()">
                                        <i class="bi bi-x-lg me-1"></i>مسح
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- قائمة القيود -->
                        <div id="journalEntriesList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>

                        <!-- ترقيم الصفحات -->
                        <nav aria-label="ترقيم القيود">
                            <ul class="pagination justify-content-center" id="entriesPagination">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </ul>
                        </nav>
                    </div>

                    <!-- تبويب القيود الذكية -->
                    <div class="tab-pane fade" id="smart" role="tabpanel">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>القيود الذكية:</strong> يتم إنشاؤها تلقائياً من العمليات المختلفة مثل المبيعات والمشتريات والإنتاج.
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="bi bi-cart text-primary" style="font-size: 2rem;"></i>
                                        <h6 class="card-title mt-2">قيود المبيعات</h6>
                                        <p class="card-text">إنشاء قيود تلقائية من فواتير المبيعات</p>
                                        <button class="btn btn-primary btn-sm" onclick="generateSalesEntries()">إنشاء</button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="bi bi-bag text-success" style="font-size: 2rem;"></i>
                                        <h6 class="card-title mt-2">قيود المشتريات</h6>
                                        <p class="card-text">إنشاء قيود تلقائية من فواتير المشتريات</p>
                                        <button class="btn btn-success btn-sm" onclick="generatePurchaseEntries()">إنشاء</button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="bi bi-gear text-warning" style="font-size: 2rem;"></i>
                                        <h6 class="card-title mt-2">قيود الإنتاج</h6>
                                        <p class="card-text">إنشاء قيود تلقائية من عمليات الإنتاج</p>
                                        <button class="btn btn-warning btn-sm" onclick="generateProductionEntries()">إنشاء</button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="bi bi-person-badge text-info" style="font-size: 2rem;"></i>
                                        <h6 class="card-title mt-2">قيود الرواتب</h6>
                                        <p class="card-text">إنشاء قيود تلقائية من كشوف الرواتب</p>
                                        <button class="btn btn-info btn-sm" onclick="generatePayrollEntries()">إنشاء</button>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                                        <h6 class="card-title mt-2">قيود التالف</h6>
                                        <p class="card-text">إنشاء قيود تلقائية من المنتجات التالفة</p>
                                        <button class="btn btn-danger btn-sm" onclick="generateDamageEntries()">إنشاء</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب المراجعة -->
                    <div class="tab-pane fade" id="review" role="tabpanel">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> جميع القيود تحتاج إلى مراجعة واعتماد قبل التأثير على الحسابات.
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="reviewerSelect" class="form-label">المراجع</label>
                                <select class="form-select" id="reviewerSelect">
                                    <option value="">اختر المراجع</option>
                                    <option value="1">أحمد المحاسب الرئيسي</option>
                                    <option value="2">فاطمة المدققة</option>
                                    <option value="3">محمد المدير المالي</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="reviewStatusSelect" class="form-label">حالة المراجعة</label>
                                <select class="form-select" id="reviewStatusSelect">
                                    <option value="pending">في انتظار المراجعة</option>
                                    <option value="approved">معتمد</option>
                                    <option value="rejected">مرفوض</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="reviewTable">
                                <thead>
                                    <tr>
                                        <th>رقم القيد</th>
                                        <th>التاريخ</th>
                                        <th>النوع</th>
                                        <th>المبلغ</th>
                                        <th>المنشئ</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="reviewTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب سجل التغييرات -->
                    <div class="tab-pane fade" id="audit" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="auditDateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="auditDateFrom">
                            </div>
                            <div class="col-md-4">
                                <label for="auditDateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="auditDateTo">
                            </div>
                            <div class="col-md-4">
                                <label for="auditActionFilter" class="form-label">نوع العملية</label>
                                <select class="form-select" id="auditActionFilter">
                                    <option value="">جميع العمليات</option>
                                    <option value="create">إنشاء</option>
                                    <option value="edit">تعديل</option>
                                    <option value="delete">حذف</option>
                                    <option value="approve">اعتماد</option>
                                    <option value="reject">رفض</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="auditTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ والوقت</th>
                                        <th>المستخدم</th>
                                        <th>العملية</th>
                                        <th>رقم القيد</th>
                                        <th>التفاصيل</th>
                                        <th>عنوان IP</th>
                                    </tr>
                                </thead>
                                <tbody id="auditTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة قيد جديد -->
    <div class="modal fade" id="journalEntryModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">قيد محاسبي جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="journalEntryForm">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="entryNumber" class="form-label">رقم القيد</label>
                                <input type="text" class="form-control" id="entryNumber" readonly>
                            </div>
                            <div class="col-md-4">
                                <label for="entryDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="entryDate" required>
                            </div>
                            <div class="col-md-4">
                                <label for="entryType" class="form-label">نوع القيد *</label>
                                <select class="form-select" id="entryType" required>
                                    <option value="manual">يدوي</option>
                                    <option value="smart">ذكي</option>
                                    <option value="auto">تلقائي</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="entryDescription" class="form-label">البيان *</label>
                                <textarea class="form-control" id="entryDescription" rows="2" required placeholder="وصف القيد المحاسبي..."></textarea>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">تفاصيل القيد</h6>

                        <!-- إضافة سطر جديد -->
                        <div class="account-selector mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="accountSelect" class="form-label">الحساب</label>
                                    <select class="form-select" id="accountSelect">
                                        <option value="">اختر الحساب</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="debitAmount" class="form-label">مدين</label>
                                    <input type="number" class="form-control" id="debitAmount" step="0.01" onchange="updateBalance()">
                                </div>
                                <div class="col-md-3">
                                    <label for="creditAmount" class="form-label">دائن</label>
                                    <input type="number" class="form-control" id="creditAmount" step="0.01" onchange="updateBalance()">
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-success" onclick="addEntryLine()">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول تفاصيل القيد -->
                        <div class="table-responsive">
                            <table class="table table-striped" id="entryDetailsTable">
                                <thead>
                                    <tr>
                                        <th>الحساب</th>
                                        <th>البيان</th>
                                        <th>مدين</th>
                                        <th>دائن</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="entryDetailsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <td colspan="2"><strong>الإجمالي:</strong></td>
                                        <td><strong id="totalDebit">0.00</strong></td>
                                        <td><strong id="totalCredit">0.00</strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <!-- مؤشر التوازن -->
                        <div class="balance-indicator mb-3" id="balanceIndicator">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            القيد غير متوازن
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <label for="entryReference" class="form-label">المرجع</label>
                                <input type="text" class="form-control" id="entryReference" placeholder="رقم الفاتورة أو المرجع...">
                            </div>
                            <div class="col-md-6">
                                <label for="entryNotes" class="form-label">ملاحظات</label>
                                <input type="text" class="form-control" id="entryNotes" placeholder="ملاحظات إضافية...">
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="saveAsDraft()">حفظ كمسودة</button>
                    <button type="button" class="btn btn-primary" onclick="saveJournalEntry()">حفظ وإرسال للمراجعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة القيد الذكي -->
    <div class="modal fade" id="smartEntryModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إنشاء قيد ذكي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        <strong>القيود الذكية:</strong> يتم إنشاؤها تلقائياً بناءً على نوع العملية المحددة.
                    </div>

                    <form id="smartEntryForm">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="smartEntryType" class="form-label">نوع العملية *</label>
                                <select class="form-select" id="smartEntryType" required onchange="updateSmartEntryFields()">
                                    <option value="">اختر نوع العملية</option>
                                    <option value="sales">مبيعات</option>
                                    <option value="purchase">مشتريات</option>
                                    <option value="production">إنتاج</option>
                                    <option value="payroll">رواتب</option>
                                    <option value="damage">تالف</option>
                                    <option value="expense">مصروف</option>
                                    <option value="revenue">إيراد</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="smartEntryDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="smartEntryDate" required>
                            </div>
                        </div>

                        <div id="smartEntryFields">
                            <!-- سيتم ملؤها بـ JavaScript حسب نوع العملية -->
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label for="smartEntryNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="smartEntryNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="generateSmartEntry()">إنشاء القيد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة المراجعة -->
    <div class="modal fade" id="reviewModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">مراجعة القيد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="reviewEntryDetails">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>

                    <hr>

                    <form id="reviewForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label for="reviewDecision" class="form-label">قرار المراجعة *</label>
                                <select class="form-select" id="reviewDecision" required>
                                    <option value="">اختر القرار</option>
                                    <option value="approved">اعتماد</option>
                                    <option value="rejected">رفض</option>
                                    <option value="pending">إرجاع للتعديل</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="reviewDate" class="form-label">تاريخ المراجعة</label>
                                <input type="date" class="form-control" id="reviewDate" readonly>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label for="reviewComments" class="form-label">تعليقات المراجع</label>
                                <textarea class="form-control" id="reviewComments" rows="3" placeholder="اكتب تعليقاتك هنا..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="submitReview()">تأكيد المراجعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة سجل التغييرات -->
    <div class="modal fade" id="auditLogModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">سجل التغييرات التفصيلي</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="auditUserFilter" class="form-label">المستخدم</label>
                            <select class="form-select" id="auditUserFilter">
                                <option value="">جميع المستخدمين</option>
                                <option value="1">أحمد المحاسب</option>
                                <option value="2">فاطمة المدققة</option>
                                <option value="3">محمد المدير</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="auditModuleFilter" class="form-label">النموذج</label>
                            <select class="form-select" id="auditModuleFilter">
                                <option value="">جميع النماذج</option>
                                <option value="journal">القيود المحاسبية</option>
                                <option value="inventory">المخزون</option>
                                <option value="sales">المبيعات</option>
                                <option value="purchase">المشتريات</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="loadAuditLog()">
                                    <i class="bi bi-search me-1"></i>تحديث السجل
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-striped table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ والوقت</th>
                                    <th>المستخدم</th>
                                    <th>النموذج</th>
                                    <th>العملية</th>
                                    <th>المعرف</th>
                                    <th>القيم القديمة</th>
                                    <th>القيم الجديدة</th>
                                    <th>IP</th>
                                </tr>
                            </thead>
                            <tbody id="auditLogTableBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-primary" onclick="exportAuditLog()">
                        <i class="bi bi-download me-1"></i>تصدير
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Journal JS Files -->
    <script src="journal-functions.js"></script>
    <script src="print-system.js"></script>
    <script src="system-init.js"></script>

    <script>
        // بيانات القيود المحاسبية التجريبية
        let journalEntries = [
            {
                id: 1,
                number: 'JE001',
                date: '2024-01-15',
                type: 'smart',
                description: 'قيد مبيعات يومية',
                reference: 'INV-001',
                status: 'approved',
                createdBy: 'أحمد المحاسب',
                reviewedBy: 'فاطمة المدققة',
                isBalanced: true,
                totalAmount: 15000,
                details: [
                    { accountId: 1, accountName: 'النقدية', debit: 15000, credit: 0 },
                    { accountId: 2, accountName: 'المبيعات', debit: 0, credit: 15000 }
                ]
            },
            {
                id: 2,
                number: 'JE002',
                date: '2024-01-14',
                type: 'auto',
                description: 'قيد تالف منتجات',
                reference: 'DMG-001',
                status: 'pending',
                createdBy: 'النظام',
                reviewedBy: null,
                isBalanced: true,
                totalAmount: 85,
                details: [
                    { accountId: 3, accountName: 'خسائر التالف', debit: 85, credit: 0 },
                    { accountId: 4, accountName: 'المخزون', debit: 0, credit: 85 }
                ]
            }
        ];

        // بيانات سجل التغييرات التجريبية
        let auditLog = [
            {
                id: 1,
                timestamp: '2024-01-15 10:30:25',
                userId: 1,
                userName: 'أحمد المحاسب',
                module: 'journal',
                action: 'create',
                recordId: 'JE001',
                oldValues: null,
                newValues: { description: 'قيد مبيعات يومية', amount: 15000 },
                ipAddress: '*************'
            },
            {
                id: 2,
                timestamp: '2024-01-15 11:15:42',
                userId: 2,
                userName: 'فاطمة المدققة',
                module: 'journal',
                action: 'approve',
                recordId: 'JE001',
                oldValues: { status: 'pending' },
                newValues: { status: 'approved' },
                ipAddress: '*************'
            }
        ];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // طباعة الصفحة
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>
