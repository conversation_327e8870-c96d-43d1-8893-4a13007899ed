<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الصناديق والبنوك</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .balance-positive {
            color: #28a745;
            font-weight: bold;
        }

        .balance-negative {
            color: #dc3545;
            font-weight: bold;
        }

        .balance-zero {
            color: #6c757d;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html" class="active">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-bank text-primary me-2"></i>
                    الصناديق والبنوك
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddCashBankModal()">
                        <i class="bi bi-plus-lg me-1"></i>إضافة صندوق/بنك
                    </button>
                    <button type="button" class="btn btn-success" onclick="showTransferModal()">
                        <i class="bi bi-arrow-left-right me-1"></i>تحويل
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="searchInput" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="searchInput" placeholder="البحث في الصناديق والبنوك...">
                        </div>
                        <div class="col-md-3">
                            <label for="typeFilter" class="form-label">النوع</label>
                            <select class="form-select" id="typeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="cash">صندوق</option>
                                <option value="bank">بنك</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="statusFilter" class="form-label">الحالة</label>
                            <select class="form-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="bi bi-x-lg me-1"></i>مسح الفلاتر
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول الصناديق والبنوك -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>قائمة الصناديق والبنوك
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover" id="cashBanksTable">
                            <thead>
                                <tr>
                                    <th>الرمز</th>
                                    <th>الاسم</th>
                                    <th>النوع</th>
                                    <th>المسؤول</th>
                                    <th>الرصيد الحالي</th>
                                    <th>آخر معاملة</th>
                                    <th>الحالة</th>
                                    <th>العمليات</th>
                                </tr>
                            </thead>
                            <tbody id="cashBanksTableBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة صندوق/بنك جديد -->
    <div class="modal fade" id="addCashBankModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة صندوق/بنك جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addCashBankForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="code" class="form-label">الرمز *</label>
                                    <input type="text" class="form-control" id="code" required>
                                </div>

                                <div class="mb-3">
                                    <label for="name" class="form-label">الاسم *</label>
                                    <input type="text" class="form-control" id="name" required>
                                </div>

                                <div class="mb-3">
                                    <label for="type" class="form-label">النوع *</label>
                                    <select class="form-select" id="type" required onchange="toggleBankFields()">
                                        <option value="">اختر النوع</option>
                                        <option value="cash">صندوق</option>
                                        <option value="bank">بنك</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="responsible" class="form-label">المسؤول</label>
                                    <select class="form-select" id="responsible">
                                        <option value="">اختر المسؤول</option>
                                        <option value="1">أحمد المحاسب</option>
                                        <option value="2">فاطمة أمين الصندوق</option>
                                        <option value="3">محمد المدير</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="openingBalance" class="form-label">الرصيد الافتتاحي</label>
                                    <input type="number" class="form-control" id="openingBalance" step="0.001" value="0">
                                </div>

                                <div class="mb-3" id="bankFields" style="display: none;">
                                    <label for="bankName" class="form-label">اسم البنك</label>
                                    <input type="text" class="form-control" id="bankName">
                                </div>

                                <div class="mb-3" id="accountNumberField" style="display: none;">
                                    <label for="accountNumber" class="form-label">رقم الحساب</label>
                                    <input type="text" class="form-control" id="accountNumber">
                                </div>

                                <div class="mb-3" id="ibanField" style="display: none;">
                                    <label for="iban" class="form-label">رقم IBAN</label>
                                    <input type="text" class="form-control" id="iban">
                                </div>

                                <div class="mb-3">
                                    <label for="currency" class="form-label">العملة</label>
                                    <select class="form-select" id="currency">
                                        <option value="YER">ريال يمني</option>
                                        <option value="SAR">ريال سعودي</option>
                                        <option value="USD">دولار أمريكي</option>
                                        <option value="EUR">يورو</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">الوصف</label>
                            <textarea class="form-control" id="description" rows="3"></textarea>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" checked>
                            <label class="form-check-label" for="isActive">
                                نشط
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveCashBank()">حفظ</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة التحويل -->
    <div class="modal fade" id="transferModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تحويل بين الصناديق/البنوك</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="transferForm">
                        <div class="mb-3">
                            <label for="fromAccount" class="form-label">من *</label>
                            <select class="form-select" id="fromAccount" required onchange="updateFromBalance()">
                                <option value="">اختر الصندوق/البنك</option>
                            </select>
                            <div class="form-text">الرصيد المتاح: <span id="fromBalance">0</span> ر.ي</div>
                        </div>

                        <div class="mb-3">
                            <label for="toAccount" class="form-label">إلى *</label>
                            <select class="form-select" id="toAccount" required>
                                <option value="">اختر الصندوق/البنك</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="transferAmount" class="form-label">المبلغ *</label>
                            <input type="number" class="form-control" id="transferAmount" step="0.001" required>
                        </div>

                        <div class="mb-3">
                            <label for="transferDate" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="transferDate" required>
                        </div>

                        <div class="mb-3">
                            <label for="transferDescription" class="form-label">البيان</label>
                            <textarea class="form-control" id="transferDescription" rows="3" placeholder="وصف التحويل..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="transferReference" class="form-label">رقم المرجع</label>
                            <input type="text" class="form-control" id="transferReference" placeholder="رقم الشيك أو الحوالة...">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="executeTransfer()">تنفيذ التحويل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة كشف الحساب -->
    <div class="modal fade" id="statementModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="statementTitle">كشف حساب</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label for="statementFrom" class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="statementFrom">
                        </div>
                        <div class="col-md-4">
                            <label for="statementTo" class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="statementTo">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-primary" onclick="loadStatement()">عرض الكشف</button>
                            </div>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>التاريخ</th>
                                    <th>البيان</th>
                                    <th>المرجع</th>
                                    <th>مدين</th>
                                    <th>دائن</th>
                                    <th>الرصيد</th>
                                </tr>
                            </thead>
                            <tbody id="statementTableBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" onclick="printStatement()">طباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // بيانات الصناديق والبنوك التجريبية
        let cashBanks = [
            {
                id: 1,
                code: 'CASH001',
                name: 'الصندوق الرئيسي',
                type: 'cash',
                responsible: 'فاطمة أمين الصندوق',
                responsibleId: 2,
                currentBalance: 25000,
                openingBalance: 20000,
                currency: 'YER',
                lastTransaction: '2024-01-15',
                isActive: true,
                description: 'الصندوق الرئيسي للمخبز'
            },
            {
                id: 2,
                code: 'CASH002',
                name: 'صندوق المبيعات',
                type: 'cash',
                responsible: 'أحمد المحاسب',
                responsibleId: 1,
                currentBalance: 15000,
                openingBalance: 10000,
                currency: 'YER',
                lastTransaction: '2024-01-15',
                isActive: true,
                description: 'صندوق خاص بالمبيعات اليومية'
            },
            {
                id: 3,
                code: 'BANK001',
                name: 'البنك الأهلي اليمني',
                type: 'bank',
                responsible: 'محمد المدير',
                responsibleId: 3,
                currentBalance: 85000,
                openingBalance: 80000,
                currency: 'YER',
                bankName: 'البنك الأهلي اليمني',
                accountNumber: '*********',
                iban: '************************',
                lastTransaction: '2024-01-14',
                isActive: true,
                description: 'الحساب الجاري الرئيسي'
            },
            {
                id: 4,
                code: 'BANK002',
                name: 'بنك سبأ',
                type: 'bank',
                responsible: 'محمد المدير',
                responsibleId: 3,
                currentBalance: 45000,
                openingBalance: 50000,
                currency: 'YER',
                bankName: 'بنك سبأ',
                accountNumber: '*********',
                iban: '************************',
                lastTransaction: '2024-01-13',
                isActive: true,
                description: 'حساب احتياطي'
            },
            {
                id: 5,
                code: 'CASH003',
                name: 'صندوق الطوارئ',
                type: 'cash',
                responsible: '',
                responsibleId: null,
                currentBalance: 5000,
                openingBalance: 5000,
                currency: 'YER',
                lastTransaction: '2024-01-10',
                isActive: false,
                description: 'صندوق للطوارئ'
            }
        ];

        // بيانات المعاملات التجريبية
        let transactions = [
            {
                id: 1,
                accountId: 1,
                date: '2024-01-15',
                description: 'مبيعات نقدية',
                reference: 'INV-001',
                debit: 5000,
                credit: 0,
                balance: 25000
            },
            {
                id: 2,
                accountId: 3,
                date: '2024-01-14',
                description: 'إيداع نقدي',
                reference: 'DEP-001',
                debit: 0,
                credit: 10000,
                balance: 85000
            },
            {
                id: 3,
                accountId: 2,
                date: '2024-01-15',
                description: 'مبيعات المساء',
                reference: 'INV-002',
                debit: 3000,
                credit: 0,
                balance: 15000
            }
        ];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // عرض الصناديق والبنوك
        function displayCashBanks(dataToShow = cashBanks) {
            const tbody = document.getElementById('cashBanksTableBody');
            tbody.innerHTML = '';

            dataToShow.forEach(item => {
                const row = document.createElement('tr');

                const typeClass = item.type === 'cash' ? 'success' : 'info';
                const typeText = item.type === 'cash' ? 'صندوق' : 'بنك';
                const typeIcon = item.type === 'cash' ? 'cash-stack' : 'bank';

                const balanceClass = item.currentBalance > 0 ? 'balance-positive' :
                                   item.currentBalance < 0 ? 'balance-negative' : 'balance-zero';

                row.innerHTML = `
                    <td><strong>${item.code}</strong></td>
                    <td>
                        <i class="bi bi-${typeIcon} me-2 text-${typeClass}"></i>
                        ${item.name}
                    </td>
                    <td><span class="badge bg-${typeClass}">${typeText}</span></td>
                    <td>${item.responsible || '<span class="text-muted">غير محدد</span>'}</td>
                    <td><span class="${balanceClass}">${item.currentBalance.toLocaleString()} ${item.currency}</span></td>
                    <td><small class="text-muted">${formatDate(item.lastTransaction)}</small></td>
                    <td><span class="badge bg-${item.isActive ? 'success' : 'secondary'}">${item.isActive ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" onclick="showStatement(${item.id})" title="كشف الحساب">
                                <i class="bi bi-file-text"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="editCashBank(${item.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-${item.isActive ? 'warning' : 'success'}" onclick="toggleStatus(${item.id})" title="${item.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                                <i class="bi bi-${item.isActive ? 'pause' : 'play'}"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteCashBank(${item.id})" title="حذف">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الإحصائيات
        function updateStats() {
            const stats = {
                totalCashBoxes: cashBanks.filter(item => item.type === 'cash').length,
                totalBanks: cashBanks.filter(item => item.type === 'bank').length,
                totalBalance: cashBanks.reduce((sum, item) => sum + item.currentBalance, 0),
                todayTransactions: transactions.filter(t => t.date === new Date().toISOString().split('T')[0]).length
            };

            document.getElementById('totalCashBoxes').textContent = stats.totalCashBoxes;
            document.getElementById('totalBanks').textContent = stats.totalBanks;
            document.getElementById('totalBalance').textContent = stats.totalBalance.toLocaleString();
            document.getElementById('todayTransactions').textContent = stats.todayTransactions;
        }

        // فلترة البيانات
        function filterData() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const typeFilter = document.getElementById('typeFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = cashBanks.filter(item => {
                const matchesSearch = !searchTerm ||
                    item.code.toLowerCase().includes(searchTerm) ||
                    item.name.toLowerCase().includes(searchTerm) ||
                    item.responsible.toLowerCase().includes(searchTerm);

                const matchesType = !typeFilter || item.type === typeFilter;
                const matchesStatus = !statusFilter ||
                    (statusFilter === 'active' && item.isActive) ||
                    (statusFilter === 'inactive' && !item.isActive);

                return matchesSearch && matchesType && matchesStatus;
            });

            displayCashBanks(filtered);
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('typeFilter').value = '';
            document.getElementById('statusFilter').value = '';
            displayCashBanks();
        }

        // إظهار نافذة إضافة صندوق/بنك
        function showAddCashBankModal() {
            document.getElementById('addCashBankForm').reset();
            document.getElementById('isActive').checked = true;
            toggleBankFields(); // إخفاء حقول البنك
            const modal = new bootstrap.Modal(document.getElementById('addCashBankModal'));
            modal.show();
        }

        // تبديل حقول البنك
        function toggleBankFields() {
            const type = document.getElementById('type').value;
            const bankFields = document.getElementById('bankFields');
            const accountNumberField = document.getElementById('accountNumberField');
            const ibanField = document.getElementById('ibanField');

            if (type === 'bank') {
                bankFields.style.display = 'block';
                accountNumberField.style.display = 'block';
                ibanField.style.display = 'block';
            } else {
                bankFields.style.display = 'none';
                accountNumberField.style.display = 'none';
                ibanField.style.display = 'none';
            }
        }

        // حفظ صندوق/بنك جديد
        function saveCashBank() {
            const form = document.getElementById('addCashBankForm');
            const formData = new FormData(form);

            const newItem = {
                id: cashBanks.length + 1,
                code: document.getElementById('code').value,
                name: document.getElementById('name').value,
                type: document.getElementById('type').value,
                responsible: document.getElementById('responsible').selectedOptions[0]?.text || '',
                responsibleId: document.getElementById('responsible').value || null,
                currentBalance: parseFloat(document.getElementById('openingBalance').value) || 0,
                openingBalance: parseFloat(document.getElementById('openingBalance').value) || 0,
                currency: document.getElementById('currency').value,
                lastTransaction: new Date().toISOString().split('T')[0],
                isActive: document.getElementById('isActive').checked,
                description: document.getElementById('description').value
            };

            // إضافة حقول البنك إذا كان النوع بنك
            if (newItem.type === 'bank') {
                newItem.bankName = document.getElementById('bankName').value;
                newItem.accountNumber = document.getElementById('accountNumber').value;
                newItem.iban = document.getElementById('iban').value;
            }

            cashBanks.push(newItem);
            displayCashBanks();
            updateStats();

            const modal = bootstrap.Modal.getInstance(document.getElementById('addCashBankModal'));
            modal.hide();

            alert('تم إضافة الصندوق/البنك بنجاح!');
        }

        // تعديل صندوق/بنك
        function editCashBank(id) {
            const item = cashBanks.find(cb => cb.id === id);
            if (item) {
                alert(`تعديل: ${item.name}\nسيتم فتح نافذة التعديل قريباً`);
            }
        }

        // تبديل حالة التفعيل
        function toggleStatus(id) {
            const item = cashBanks.find(cb => cb.id === id);
            if (item) {
                item.isActive = !item.isActive;
                displayCashBanks();
                updateStats();
                alert(`تم ${item.isActive ? 'تفعيل' : 'إلغاء تفعيل'} ${item.name} بنجاح!`);
            }
        }

        // حذف صندوق/بنك
        function deleteCashBank(id) {
            const item = cashBanks.find(cb => cb.id === id);
            if (item && confirm(`هل أنت متأكد من حذف ${item.name}؟`)) {
                cashBanks = cashBanks.filter(cb => cb.id !== id);
                displayCashBanks();
                updateStats();
                alert('تم حذف الصندوق/البنك بنجاح!');
            }
        }

        // إظهار نافذة التحويل
        function showTransferModal() {
            populateTransferAccounts();
            document.getElementById('transferDate').value = new Date().toISOString().split('T')[0];
            const modal = new bootstrap.Modal(document.getElementById('transferModal'));
            modal.show();
        }

        // ملء قوائم الحسابات في نافذة التحويل
        function populateTransferAccounts() {
            const fromSelect = document.getElementById('fromAccount');
            const toSelect = document.getElementById('toAccount');

            fromSelect.innerHTML = '<option value="">اختر الصندوق/البنك</option>';
            toSelect.innerHTML = '<option value="">اختر الصندوق/البنك</option>';

            cashBanks.filter(item => item.isActive).forEach(item => {
                const option = `<option value="${item.id}">${item.name} (${item.currentBalance.toLocaleString()} ${item.currency})</option>`;
                fromSelect.innerHTML += option;
                toSelect.innerHTML += option;
            });
        }

        // تحديث رصيد الحساب المحول منه
        function updateFromBalance() {
            const fromAccountId = document.getElementById('fromAccount').value;
            const fromBalanceSpan = document.getElementById('fromBalance');

            if (fromAccountId) {
                const account = cashBanks.find(cb => cb.id == fromAccountId);
                if (account) {
                    fromBalanceSpan.textContent = account.currentBalance.toLocaleString();
                }
            } else {
                fromBalanceSpan.textContent = '0';
            }
        }

        // تنفيذ التحويل
        function executeTransfer() {
            const fromAccountId = parseInt(document.getElementById('fromAccount').value);
            const toAccountId = parseInt(document.getElementById('toAccount').value);
            const amount = parseFloat(document.getElementById('transferAmount').value);
            const date = document.getElementById('transferDate').value;
            const description = document.getElementById('transferDescription').value;
            const reference = document.getElementById('transferReference').value;

            // التحقق من صحة البيانات
            if (!fromAccountId || !toAccountId || !amount || !date) {
                alert('يرجى ملء جميع الحقول المطلوبة');
                return;
            }

            if (fromAccountId === toAccountId) {
                alert('لا يمكن التحويل من وإلى نفس الحساب');
                return;
            }

            const fromAccount = cashBanks.find(cb => cb.id === fromAccountId);
            const toAccount = cashBanks.find(cb => cb.id === toAccountId);

            if (fromAccount.currentBalance < amount) {
                alert('الرصيد غير كافي للتحويل');
                return;
            }

            // تنفيذ التحويل
            fromAccount.currentBalance -= amount;
            toAccount.currentBalance += amount;
            fromAccount.lastTransaction = date;
            toAccount.lastTransaction = date;

            // إضافة المعاملات
            const transferId = Date.now();

            transactions.push({
                id: transferId * 2,
                accountId: fromAccountId,
                date: date,
                description: description || `تحويل إلى ${toAccount.name}`,
                reference: reference || `TRF-${transferId}`,
                debit: 0,
                credit: amount,
                balance: fromAccount.currentBalance
            });

            transactions.push({
                id: transferId * 2 + 1,
                accountId: toAccountId,
                date: date,
                description: description || `تحويل من ${fromAccount.name}`,
                reference: reference || `TRF-${transferId}`,
                debit: amount,
                credit: 0,
                balance: toAccount.currentBalance
            });

            displayCashBanks();
            updateStats();

            const modal = bootstrap.Modal.getInstance(document.getElementById('transferModal'));
            modal.hide();

            alert('تم تنفيذ التحويل بنجاح!');
        }

        // عرض كشف الحساب
        function showStatement(accountId) {
            const account = cashBanks.find(cb => cb.id === accountId);
            if (!account) return;

            document.getElementById('statementTitle').textContent = `كشف حساب - ${account.name}`;

            // تعيين التواريخ الافتراضية (آخر شهر)
            const today = new Date();
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

            document.getElementById('statementFrom').value = lastMonth.toISOString().split('T')[0];
            document.getElementById('statementTo').value = today.toISOString().split('T')[0];

            // تحميل الكشف
            loadStatement(accountId);

            const modal = new bootstrap.Modal(document.getElementById('statementModal'));
            modal.show();
        }

        // تحميل كشف الحساب
        function loadStatement(accountId = null) {
            if (!accountId) {
                // الحصول على معرف الحساب من العنوان
                const title = document.getElementById('statementTitle').textContent;
                const accountName = title.replace('كشف حساب - ', '');
                const account = cashBanks.find(cb => cb.name === accountName);
                accountId = account ? account.id : null;
            }

            if (!accountId) return;

            const fromDate = document.getElementById('statementFrom').value;
            const toDate = document.getElementById('statementTo').value;

            // فلترة المعاملات
            const accountTransactions = transactions.filter(t =>
                t.accountId === accountId &&
                t.date >= fromDate &&
                t.date <= toDate
            ).sort((a, b) => new Date(a.date) - new Date(b.date));

            const tbody = document.getElementById('statementTableBody');
            tbody.innerHTML = '';

            let runningBalance = 0;

            accountTransactions.forEach(transaction => {
                runningBalance = transaction.balance;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${formatDate(transaction.date)}</td>
                    <td>${transaction.description}</td>
                    <td>${transaction.reference}</td>
                    <td class="text-end">${transaction.debit > 0 ? transaction.debit.toLocaleString() : '-'}</td>
                    <td class="text-end">${transaction.credit > 0 ? transaction.credit.toLocaleString() : '-'}</td>
                    <td class="text-end"><strong>${runningBalance.toLocaleString()}</strong></td>
                `;
                tbody.appendChild(row);
            });

            if (accountTransactions.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد معاملات في هذه الفترة</td></tr>';
            }
        }

        // طباعة كشف الحساب
        function printStatement() {
            window.print();
        }

        // تحديث البيانات
        function refreshData() {
            displayCashBanks();
            updateStats();
            alert('تم تحديث البيانات!');
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من المصادقة
            const user = checkAuth();
            if (user) {
                document.getElementById('sidebarUserName').textContent = user.name;
            }

            // استعادة حالة القائمة الجانبية
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }

            // عرض البيانات
            displayCashBanks();
            updateStats();

            // ربط أحداث الفلترة
            document.getElementById('searchInput').addEventListener('input', filterData);
            document.getElementById('typeFilter').addEventListener('change', filterData);
            document.getElementById('statusFilter').addEventListener('change', filterData);

            // تأثيرات بصرية
            const cards = document.querySelectorAll('.stats-card, .card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
