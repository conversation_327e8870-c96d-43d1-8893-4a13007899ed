<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام محاسبة المخبز</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            max-width: 400px;
            width: 100%;
            padding: 2rem;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 700;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .form-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 0.5rem;
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .back-link {
            text-align: center;
            margin-top: 1rem;
        }
        
        .back-link a {
            color: #667eea;
            text-decoration: none;
            font-weight: 500;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
        
        .demo-info {
            background: #e3f2fd;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
            border-left: 4px solid #2196f3;
        }
        
        .demo-info h6 {
            color: #1976d2;
            margin-bottom: 0.5rem;
        }
        
        .demo-info small {
            color: #424242;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="bi bi-shield-lock" style="font-size: 3rem; margin-bottom: 1rem;"></i>
                <h2>تسجيل الدخول</h2>
                <p class="mb-0">نظام محاسبة المخبز</p>
            </div>
            
            <div class="login-body">
                <!-- معلومات تجريبية -->
                <div class="demo-info">
                    <h6><i class="bi bi-info-circle me-2"></i>بيانات تجريبية</h6>
                    <small>
                        <strong>المدير:</strong> admin / password<br>
                        <strong>محاسب:</strong> accountant / password<br>
                        <strong>أمين صندوق:</strong> cashier / password
                    </small>
                </div>
                
                <!-- رسائل التنبيه -->
                <div id="alert-container"></div>
                
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="bi bi-person me-2"></i>اسم المستخدم
                        </label>
                        <input type="text" class="form-control" id="username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="bi bi-lock me-2"></i>كلمة المرور
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember">
                        <label class="form-check-label" for="remember">
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-login">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        دخول
                    </button>
                </form>
                
                <div class="back-link">
                    <a href="index.html">
                        <i class="bi bi-arrow-right me-1"></i>
                        العودة للصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // بيانات المستخدمين التجريبية
        const users = {
            'admin': {
                password: 'password',
                name: 'مدير النظام',
                role: 'admin',
                dashboard: 'dashboard-admin.html'
            },
            'accountant': {
                password: 'password',
                name: 'المحاسب الرئيسي',
                role: 'accountant',
                dashboard: 'dashboard-accountant.html'
            },
            'cashier': {
                password: 'password',
                name: 'أمين الصندوق',
                role: 'cashier',
                dashboard: 'dashboard-cashier.html'
            },
            'manager': {
                password: 'password',
                name: 'مدير العمليات',
                role: 'manager',
                dashboard: 'dashboard-manager.html'
            }
        };
        
        // عرض رسالة تنبيه
        function showAlert(message, type = 'info') {
            const alertTypes = {
                success: 'alert-success',
                error: 'alert-danger',
                warning: 'alert-warning',
                info: 'alert-info'
            };
            
            const alertClass = alertTypes[type] || 'alert-info';
            const iconClass = type === 'success' ? 'check-circle' : 
                             type === 'error' ? 'exclamation-triangle' : 
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';
            
            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            
            document.getElementById('alert-container').innerHTML = alertHtml;
            
            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
        
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordField.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
        
        // معالجة تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            // التحقق من البيانات
            if (!username || !password) {
                showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'error');
                return;
            }
            
            // التحقق من صحة البيانات
            if (users[username] && users[username].password === password) {
                const user = users[username];
                
                // حفظ بيانات المستخدم
                const userData = {
                    username: username,
                    name: user.name,
                    role: user.role,
                    loginTime: new Date().toISOString()
                };
                
                localStorage.setItem('currentUser', JSON.stringify(userData));
                
                if (remember) {
                    localStorage.setItem('rememberLogin', 'true');
                } else {
                    sessionStorage.setItem('currentUser', JSON.stringify(userData));
                }
                
                showAlert('تم تسجيل الدخول بنجاح! جاري التحويل...', 'success');
                
                // التحويل إلى لوحة التحكم
                setTimeout(() => {
                    window.location.href = user.dashboard;
                }, 1500);
                
            } else {
                showAlert('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        });
        
        // التحقق من تسجيل الدخول المسبق
        document.addEventListener('DOMContentLoaded', function() {
            const savedUser = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            
            if (savedUser) {
                const userData = JSON.parse(savedUser);
                showAlert(`مرحباً ${userData.name}، أنت مسجل دخول بالفعل`, 'info');
                
                // ملء البيانات تلقائياً
                document.getElementById('username').value = userData.username;
                document.getElementById('remember').checked = localStorage.getItem('rememberLogin') === 'true';
            }
            
            // تأثيرات بصرية
            const card = document.querySelector('.login-card');
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 100);
        });
        
        // ملء سريع للبيانات التجريبية
        function quickFill(username) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = 'password';
        }
        
        // إضافة أزرار الملء السريع
        document.addEventListener('DOMContentLoaded', function() {
            const demoInfo = document.querySelector('.demo-info');
            demoInfo.addEventListener('click', function(e) {
                if (e.target.tagName === 'STRONG') {
                    const username = e.target.textContent.replace(':', '');
                    if (username === 'المدير') quickFill('admin');
                    else if (username === 'محاسب') quickFill('accountant');
                    else if (username === 'أمين صندوق') quickFill('cashier');
                }
            });
        });
    </script>
</body>
</html>
