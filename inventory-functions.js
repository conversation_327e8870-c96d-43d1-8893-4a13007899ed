// وظائف إدارة المخزون

// عرض الأصناف
function displayItems(dataToShow = items) {
    const tbody = document.getElementById('itemsTableBody');
    tbody.innerHTML = '';
    
    dataToShow.forEach(item => {
        const row = document.createElement('tr');
        
        const categoryText = {
            'flour': 'دقيق ومواد أساسية',
            'ingredients': 'مكونات',
            'packaging': 'مواد تعبئة',
            'tools': 'أدوات'
        }[item.category];
        
        const categoryClass = {
            'flour': 'primary',
            'ingredients': 'success',
            'packaging': 'warning',
            'tools': 'info'
        }[item.category];
        
        const unitText = {
            'kg': 'كيلوجرام',
            'g': 'جرام',
            'l': 'لتر',
            'ml': 'مليلتر',
            'piece': 'قطعة',
            'box': 'صندوق',
            'bag': 'كيس'
        }[item.unit];
        
        // حساب إجمالي المخزون
        const totalStock = Object.values(item.warehouseStocks).reduce((sum, stock) => sum + stock, 0);
        
        // تحديد حالة المخزون
        let stockStatus, stockClass;
        if (totalStock === 0) {
            stockStatus = 'منتهي';
            stockClass = 'stock-out';
        } else if (totalStock <= item.minStock) {
            stockStatus = 'منخفض';
            stockClass = 'stock-low';
        } else if (totalStock <= item.reorderPoint) {
            stockStatus = 'متوسط';
            stockClass = 'stock-medium';
        } else {
            stockStatus = 'عالي';
            stockClass = 'stock-high';
        }
        
        row.innerHTML = `
            <td>
                <div class="item-image">${item.name.charAt(0)}</div>
            </td>
            <td>
                <strong>${item.name}</strong>
                <br><small class="text-muted">${item.description}</small>
            </td>
            <td><code>${item.code}</code></td>
            <td><span class="badge bg-${categoryClass}">${categoryText}</span></td>
            <td>${unitText}</td>
            <td><span class="stock-level ${stockClass}">${totalStock.toLocaleString()}</span></td>
            <td>${item.minStock.toLocaleString()}</td>
            <td><span class="badge bg-${stockClass === 'stock-high' ? 'success' : stockClass === 'stock-medium' ? 'warning' : 'danger'}">${stockStatus}</span></td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewItemDetails(${item.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="editItem(${item.id})" title="تعديل">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="addStock(${item.id})" title="إضافة مخزون">
                        <i class="bi bi-plus-circle"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="transferStock(${item.id})" title="تحويل">
                        <i class="bi bi-arrow-left-right"></i>
                    </button>
                </div>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// تحديث الإحصائيات
function updateStats() {
    const stats = {
        totalItems: items.length,
        totalWarehouses: warehouses.length,
        lowStockItems: 0,
        outOfStockItems: 0
    };
    
    items.forEach(item => {
        const totalStock = Object.values(item.warehouseStocks).reduce((sum, stock) => sum + stock, 0);
        if (totalStock === 0) {
            stats.outOfStockItems++;
        } else if (totalStock <= item.minStock) {
            stats.lowStockItems++;
        }
    });
    
    document.getElementById('totalItems').textContent = stats.totalItems;
    document.getElementById('totalWarehouses').textContent = stats.totalWarehouses;
    document.getElementById('lowStockItems').textContent = stats.lowStockItems;
    document.getElementById('outOfStockItems').textContent = stats.outOfStockItems;
}

// فلترة الأصناف
function filterItems() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const stockFilter = document.getElementById('stockFilter').value;
    
    let filtered = items.filter(item => {
        const matchesSearch = !searchTerm || 
            item.name.toLowerCase().includes(searchTerm) ||
            item.code.toLowerCase().includes(searchTerm) ||
            item.description.toLowerCase().includes(searchTerm);
        
        const matchesCategory = !categoryFilter || item.category === categoryFilter;
        
        const totalStock = Object.values(item.warehouseStocks).reduce((sum, stock) => sum + stock, 0);
        let matchesStock = true;
        if (stockFilter) {
            if (stockFilter === 'high' && totalStock <= item.reorderPoint) matchesStock = false;
            if (stockFilter === 'medium' && (totalStock <= item.minStock || totalStock > item.reorderPoint)) matchesStock = false;
            if (stockFilter === 'low' && (totalStock === 0 || totalStock > item.minStock)) matchesStock = false;
            if (stockFilter === 'out' && totalStock > 0) matchesStock = false;
        }
        
        return matchesSearch && matchesCategory && matchesStock;
    });
    
    displayItems(filtered);
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('stockFilter').value = '';
    displayItems();
}

// عرض المخازن
function displayWarehouses() {
    const grid = document.getElementById('warehousesGrid');
    grid.innerHTML = '';
    
    warehouses.forEach(warehouse => {
        const col = document.createElement('div');
        col.className = 'col-md-6 col-lg-4 mb-4';
        
        // حساب عدد الأصناف في المخزن
        const itemsInWarehouse = items.filter(item => item.warehouseStocks[warehouse.id] > 0).length;
        
        col.innerHTML = `
            <div class="card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h6 class="card-title">${warehouse.name}</h6>
                        <span class="badge bg-${warehouse.isActive ? 'success' : 'secondary'}">${warehouse.isActive ? 'نشط' : 'غير نشط'}</span>
                    </div>
                    <p class="card-text">
                        <strong>الكود:</strong> ${warehouse.code}<br>
                        <strong>الموقع:</strong> ${warehouse.location}<br>
                        <strong>المسؤول:</strong> ${warehouse.manager}<br>
                        <strong>عدد الأصناف:</strong> ${itemsInWarehouse}
                    </p>
                    <p class="text-muted small">${warehouse.description}</p>
                </div>
                <div class="card-footer">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-info btn-sm" onclick="viewWarehouseStock(${warehouse.id})" title="عرض المخزون">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-primary btn-sm" onclick="editWarehouse(${warehouse.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-${warehouse.isActive ? 'warning' : 'success'} btn-sm" onclick="toggleWarehouseStatus(${warehouse.id})" title="${warehouse.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                            <i class="bi bi-${warehouse.isActive ? 'pause' : 'play'}"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        grid.appendChild(col);
    });
}

// عرض حركات المخزون
function displayMovements(dataToShow = movements) {
    const tbody = document.getElementById('movementsTableBody');
    tbody.innerHTML = '';
    
    dataToShow.forEach(movement => {
        const row = document.createElement('tr');
        
        const typeText = {
            'in': 'وارد',
            'out': 'صادر',
            'transfer': 'تحويل',
            'adjustment': 'تسوية'
        }[movement.type];
        
        const typeClass = {
            'in': 'movement-in',
            'out': 'movement-out',
            'transfer': 'movement-transfer',
            'adjustment': 'text-warning'
        }[movement.type];
        
        row.innerHTML = `
            <td>${formatDate(movement.date)}</td>
            <td>${movement.itemName}</td>
            <td><span class="${typeClass}">${typeText}</span></td>
            <td>${movement.warehouseName}</td>
            <td>${movement.quantity.toLocaleString()}</td>
            <td>${movement.reference || '-'}</td>
            <td>${movement.user}</td>
            <td>${movement.notes || '-'}</td>
        `;
        
        tbody.appendChild(row);
    });
}

// إظهار نافذة إضافة صنف
function showAddItemModal() {
    document.getElementById('addItemForm').reset();
    document.getElementById('isActive').checked = true;
    populateWarehouseStocks();
    const modal = new bootstrap.Modal(document.getElementById('addItemModal'));
    modal.show();
}

// ملء أرصدة المخازن في نافذة إضافة الصنف
function populateWarehouseStocks() {
    const container = document.getElementById('warehouseStocks');
    container.innerHTML = '';
    
    warehouses.forEach(warehouse => {
        const div = document.createElement('div');
        div.className = 'row mb-2';
        div.innerHTML = `
            <div class="col-md-6">
                <label class="form-label">${warehouse.name}</label>
            </div>
            <div class="col-md-6">
                <input type="number" class="form-control" id="stock_${warehouse.id}" step="0.001" value="0" placeholder="الكمية الافتتاحية">
            </div>
        `;
        container.appendChild(div);
    });
}

// حفظ صنف جديد
function saveItem() {
    const newItem = {
        id: items.length + 1,
        code: document.getElementById('itemCode').value,
        name: document.getElementById('itemName').value,
        category: document.getElementById('itemCategory').value,
        unit: document.getElementById('itemUnit').value,
        description: document.getElementById('itemDescription').value,
        minStock: parseFloat(document.getElementById('minStock').value),
        maxStock: parseFloat(document.getElementById('maxStock').value) || 0,
        reorderPoint: parseFloat(document.getElementById('reorderPoint').value) || 0,
        costPrice: parseFloat(document.getElementById('costPrice').value) || 0,
        sellingPrice: parseFloat(document.getElementById('sellingPrice').value) || 0,
        isActive: document.getElementById('isActive').checked,
        warehouseStocks: {}
    };
    
    // جمع أرصدة المخازن
    warehouses.forEach(warehouse => {
        const stockInput = document.getElementById(`stock_${warehouse.id}`);
        newItem.warehouseStocks[warehouse.id] = parseFloat(stockInput.value) || 0;
    });
    
    items.push(newItem);
    displayItems();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addItemModal'));
    modal.hide();
    
    alert('تم إضافة الصنف بنجاح!');
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// تحديث البيانات
function refreshData() {
    displayItems();
    displayWarehouses();
    displayMovements();
    updateStats();
    alert('تم تحديث البيانات!');
}
