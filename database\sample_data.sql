-- ===================================================
-- البيانات التجريبية لنظام المحاسبة للمخبز
-- Sample Data for Bakery Accounting System
-- ===================================================

USE `bakery_accounting`;

-- ===================================================
-- 1. إعدادات المنشأة
-- ===================================================
INSERT INTO `company_settings` (
  `company_name`, `company_name_en`, `address`, `phone1`, `phone2`, 
  `email`, `currency`, `currency_symbol`, `tax_rate`, `language`
) VALUES (
  'مخبز الأنوار', 'Alanwar Bakery', 'صنعاء - شارع الزبيري', 
  '********', '********', '<EMAIL>', 
  'YER', 'ر.ي', 5.00, 'ar'
);

-- ===================================================
-- 2. المستخدمين
-- ===================================================
INSERT INTO `users` (
  `username`, `password`, `full_name`, `email`, `phone`, `role`, 
  `permissions`, `is_active`
) VALUES 
('admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
 'مدير النظام', '<EMAIL>', '********', 'admin', 
 '{"all": true}', 1),
('accountant', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
 'المحاسب الرئيسي', '<EMAIL>', '********', 'accountant', 
 '{"accounts": true, "reports": true, "vouchers": true}', 1),
('cashier', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 
 'أمين الصندوق', '<EMAIL>', '********', 'cashier', 
 '{"invoices": true, "vouchers": true, "cash_banks": true}', 1);

-- ===================================================
-- 3. شجرة الحسابات الأساسية
-- ===================================================
-- الأصول
INSERT INTO `chart_of_accounts` (
  `account_code`, `account_name`, `account_type`, `account_nature`, 
  `level`, `is_main`, `is_active`, `created_by`
) VALUES 
('1', 'الأصول', 'asset', 'debit', 1, 1, 1, 1),
('11', 'الأصول المتداولة', 'asset', 'debit', 2, 1, 1, 1),
('111', 'النقدية والبنوك', 'asset', 'debit', 3, 1, 1, 1),
('1111', 'الصندوق الرئيسي', 'asset', 'debit', 4, 0, 1, 1),
('1112', 'صندوق المبيعات', 'asset', 'debit', 4, 0, 1, 1),
('1113', 'البنك الأهلي', 'asset', 'debit', 4, 0, 1, 1),
('112', 'العملاء', 'asset', 'debit', 3, 1, 1, 1),
('1121', 'عملاء المبيعات', 'asset', 'debit', 4, 0, 1, 1),
('113', 'المخزون', 'asset', 'debit', 3, 1, 1, 1),
('1131', 'مخزون المواد الخام', 'asset', 'debit', 4, 0, 1, 1),
('1132', 'مخزون المنتجات الجاهزة', 'asset', 'debit', 4, 0, 1, 1),
('12', 'الأصول الثابتة', 'asset', 'debit', 2, 1, 1, 1),
('121', 'المعدات والآلات', 'asset', 'debit', 3, 0, 1, 1),
('122', 'مجمع إهلاك المعدات', 'asset', 'credit', 3, 0, 1, 1);

-- الخصوم
INSERT INTO `chart_of_accounts` (
  `account_code`, `account_name`, `account_type`, `account_nature`, 
  `level`, `is_main`, `is_active`, `created_by`
) VALUES 
('2', 'الخصوم', 'liability', 'credit', 1, 1, 1, 1),
('21', 'الخصوم المتداولة', 'liability', 'credit', 2, 1, 1, 1),
('211', 'الموردين', 'liability', 'credit', 3, 1, 1, 1),
('2111', 'موردو المواد الخام', 'liability', 'credit', 4, 0, 1, 1),
('212', 'الموظفين', 'liability', 'credit', 3, 1, 1, 1),
('2121', 'رواتب الموظفين', 'liability', 'credit', 4, 0, 1, 1),
('213', 'الضرائب المستحقة', 'liability', 'credit', 3, 0, 1, 1);

-- حقوق الملكية
INSERT INTO `chart_of_accounts` (
  `account_code`, `account_name`, `account_type`, `account_nature`, 
  `level`, `is_main`, `is_active`, `created_by`
) VALUES 
('3', 'حقوق الملكية', 'equity', 'credit', 1, 1, 1, 1),
('31', 'رأس المال', 'equity', 'credit', 2, 0, 1, 1),
('32', 'الأرباح المحتجزة', 'equity', 'credit', 2, 0, 1, 1);

-- الإيرادات
INSERT INTO `chart_of_accounts` (
  `account_code`, `account_name`, `account_type`, `account_nature`, 
  `level`, `is_main`, `is_active`, `created_by`
) VALUES 
('4', 'الإيرادات', 'revenue', 'credit', 1, 1, 1, 1),
('41', 'إيرادات المبيعات', 'revenue', 'credit', 2, 1, 1, 1),
('411', 'مبيعات الخبز', 'revenue', 'credit', 3, 0, 1, 1),
('412', 'مبيعات الحلويات', 'revenue', 'credit', 3, 0, 1, 1),
('413', 'مبيعات المعجنات', 'revenue', 'credit', 3, 0, 1, 1);

-- المصروفات
INSERT INTO `chart_of_accounts` (
  `account_code`, `account_name`, `account_type`, `account_nature`, 
  `level`, `is_main`, `is_active`, `created_by`
) VALUES 
('5', 'المصروفات', 'expense', 'debit', 1, 1, 1, 1),
('51', 'تكلفة البضاعة المباعة', 'expense', 'debit', 2, 1, 1, 1),
('511', 'تكلفة المواد الخام', 'expense', 'debit', 3, 0, 1, 1),
('52', 'المصروفات التشغيلية', 'expense', 'debit', 2, 1, 1, 1),
('521', 'رواتب الموظفين', 'expense', 'debit', 3, 0, 1, 1),
('522', 'إيجار المحل', 'expense', 'debit', 3, 0, 1, 1),
('523', 'فواتير الكهرباء', 'expense', 'debit', 3, 0, 1, 1),
('524', 'فواتير المياه', 'expense', 'debit', 3, 0, 1, 1),
('525', 'مصروفات الصيانة', 'expense', 'debit', 3, 0, 1, 1),
('53', 'مصروفات الإهلاك', 'expense', 'debit', 2, 0, 1, 1);

-- ===================================================
-- 4. وحدات القياس
-- ===================================================
INSERT INTO `units` (`unit_name`, `unit_symbol`, `created_by`) VALUES 
('كيلوجرام', 'كجم', 1),
('جرام', 'جم', 1),
('قطعة', 'قطعة', 1),
('صندوق', 'صندوق', 1),
('كيس', 'كيس', 1),
('لتر', 'لتر', 1),
('ملليلتر', 'مل', 1),
('عبوة', 'عبوة', 1);

-- ===================================================
-- 5. الصناديق والبنوك
-- ===================================================
INSERT INTO `cash_banks` (
  `name`, `type`, `account_id`, `responsible_user_id`, 
  `opening_balance`, `current_balance`, `created_by`
) VALUES 
('الصندوق الرئيسي', 'cash', 4, 1, 100000.000, 100000.000, 1),
('صندوق المبيعات', 'cash', 5, 3, 50000.000, 50000.000, 1),
('البنك الأهلي', 'bank', 6, 1, 500000.000, 500000.000, 1);

-- ===================================================
-- 6. فئات الأصناف
-- ===================================================
INSERT INTO `item_categories` (`category_name`, `description`, `created_by`) VALUES 
('المواد الخام', 'المواد الأساسية للإنتاج', 1),
('منتجات الخبز', 'أنواع الخبز المختلفة', 1),
('الحلويات', 'الحلويات والكعك', 1),
('المعجنات', 'المعجنات المختلفة', 1),
('المشروبات', 'المشروبات الساخنة والباردة', 1);

-- ===================================================
-- 7. الأصناف (عينة من المواد الخام والمنتجات)
-- ===================================================
INSERT INTO `items` (
  `item_code`, `item_name`, `item_type`, `category_id`, `main_unit_id`, 
  `purchase_price`, `selling_price`, `min_stock`, `current_stock`, `created_by`
) VALUES 
-- المواد الخام
('RAW001', 'دقيق أبيض', 'raw_material', 1, 1, 800.000, 0.000, 100.000, 500.000, 1),
('RAW002', 'سكر أبيض', 'raw_material', 1, 1, 1200.000, 0.000, 50.000, 200.000, 1),
('RAW003', 'زيت نباتي', 'raw_material', 1, 6, 2500.000, 0.000, 20.000, 100.000, 1),
('RAW004', 'خميرة فورية', 'raw_material', 1, 1, 3000.000, 0.000, 10.000, 50.000, 1),
('RAW005', 'ملح طعام', 'raw_material', 1, 1, 400.000, 0.000, 25.000, 100.000, 1),
-- المنتجات
('PRD001', 'خبز عربي', 'product', 2, 3, 0.000, 50.000, 100.000, 200.000, 1),
('PRD002', 'خبز توست', 'product', 2, 3, 0.000, 150.000, 50.000, 100.000, 1),
('PRD003', 'كعك بالتمر', 'product', 3, 3, 0.000, 200.000, 30.000, 80.000, 1),
('PRD004', 'معجنات بالجبن', 'product', 4, 3, 0.000, 300.000, 20.000, 50.000, 1);
