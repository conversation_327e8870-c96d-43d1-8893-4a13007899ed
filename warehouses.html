<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المخازن</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .warehouse-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .warehouse-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .warehouse-active {
            border-left-color: #28a745;
        }

        .warehouse-inactive {
            border-left-color: #dc3545;
        }

        .warehouse-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        @media print {
            .sidebar, .btn, .action-buttons {
                display: none !important;
            }
            .main-content {
                margin-right: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="warehouses.html" class="active">
                    <i class="bi bi-house-gear"></i>
                    <span class="menu-text">إدارة المخازن</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="products.html">
                    <i class="bi bi-basket"></i>
                    <span class="menu-text">المنتجات والوصفات</span>
                </a>
            </li>

            <li>
                <a href="journal-entries.html">
                    <i class="bi bi-journal-text"></i>
                    <span class="menu-text">القيود المحاسبية</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-house-gear text-primary me-2"></i>
                    إدارة المخازن
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddWarehouseModal()">
                        <i class="bi bi-plus-lg me-1"></i>إضافة مخزن
                    </button>
                    <button type="button" class="btn btn-success" onclick="showWarehouseTransferModal()">
                        <i class="bi bi-arrow-left-right me-1"></i>نقل بين المخازن
                    </button>
                    <button type="button" class="btn btn-info" onclick="showWarehouseReportModal()">
                        <i class="bi bi-graph-up me-1"></i>تقرير المخازن
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>

            <!-- إحصائيات المخازن -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #28a745;">
                        <div class="icon text-success">
                            <i class="bi bi-house-gear"></i>
                        </div>
                        <div class="number text-success" id="totalWarehouses">8</div>
                        <div class="label">إجمالي المخازن</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #17a2b8;">
                        <div class="icon text-info">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="number text-info" id="activeWarehouses">6</div>
                        <div class="label">المخازن النشطة</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #ffc107;">
                        <div class="icon text-warning">
                            <i class="bi bi-box-seam"></i>
                        </div>
                        <div class="number text-warning" id="totalItems">1,245</div>
                        <div class="label">إجمالي الأصناف</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #dc3545;">
                        <div class="icon text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="number text-danger" id="lowStockItems">23</div>
                        <div class="label">أصناف منخفضة</div>
                    </div>
                </div>
            </div>

            <!-- فلاتر البحث -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-search me-2"></i>البحث والفلترة
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="warehouseSearchInput" class="form-label">البحث</label>
                            <input type="text" class="form-control" id="warehouseSearchInput" placeholder="البحث في المخازن...">
                        </div>
                        <div class="col-md-3">
                            <label for="warehouseTypeFilter" class="form-label">نوع المخزن</label>
                            <select class="form-select" id="warehouseTypeFilter">
                                <option value="">جميع الأنواع</option>
                                <option value="main">رئيسي</option>
                                <option value="branch">فرعي</option>
                                <option value="production">إنتاج</option>
                                <option value="damaged">تالف</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="warehouseStatusFilter" class="form-label">الحالة</label>
                            <select class="form-select" id="warehouseStatusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشط</option>
                                <option value="inactive">غير نشط</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-primary" onclick="searchWarehouses()">
                                    <i class="bi bi-search me-1"></i>بحث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- قائمة المخازن -->
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-list me-2"></i>قائمة المخازن
                    </h6>
                </div>
                <div class="card-body">
                    <div id="warehousesList">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة مخزن جديد -->
    <div class="modal fade" id="addWarehouseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مخزن جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addWarehouseForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="warehouseName" class="form-label">اسم المخزن *</label>
                                <input type="text" class="form-control" id="warehouseName" required>
                            </div>
                            <div class="col-md-6">
                                <label for="warehouseCode" class="form-label">كود المخزن *</label>
                                <input type="text" class="form-control" id="warehouseCode" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="warehouseType" class="form-label">نوع المخزن *</label>
                                <select class="form-select" id="warehouseType" required>
                                    <option value="">اختر النوع</option>
                                    <option value="main">مخزن رئيسي</option>
                                    <option value="branch">مخزن فرعي</option>
                                    <option value="production">مخزن إنتاج</option>
                                    <option value="damaged">مخزن تالف</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="warehouseManager" class="form-label">أمين المخزن</label>
                                <select class="form-select" id="warehouseManager">
                                    <option value="">اختر أمين المخزن</option>
                                    <option value="1">أحمد محمد - أمين مخزن</option>
                                    <option value="2">فاطمة علي - أمين مخزن</option>
                                    <option value="3">محمد حسن - أمين مخزن</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-12">
                                <label for="warehouseAddress" class="form-label">العنوان</label>
                                <textarea class="form-control" id="warehouseAddress" rows="2"></textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="warehousePhone" class="form-label">الهاتف</label>
                                <input type="tel" class="form-control" id="warehousePhone">
                            </div>
                            <div class="col-md-6">
                                <label for="warehouseCapacity" class="form-label">السعة التخزينية (متر مكعب)</label>
                                <input type="number" class="form-control" id="warehouseCapacity" step="0.01">
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="warehouseIsActive" checked>
                                    <label class="form-check-label" for="warehouseIsActive">
                                        مخزن نشط
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="warehouseAllowNegative">
                                    <label class="form-check-label" for="warehouseAllowNegative">
                                        السماح بالرصيد السالب
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label for="warehouseNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="warehouseNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveWarehouse()">حفظ المخزن</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة نقل بين المخازن -->
    <div class="modal fade" id="warehouseTransferModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">نقل بين المخازن</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="warehouseTransferForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="fromWarehouse" class="form-label">من مخزن *</label>
                                <select class="form-select" id="fromWarehouse" required>
                                    <option value="">اختر المخزن</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="toWarehouse" class="form-label">إلى مخزن *</label>
                                <select class="form-select" id="toWarehouse" required>
                                    <option value="">اختر المخزن</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="transferItem" class="form-label">الصنف *</label>
                                <select class="form-select" id="transferItem" required>
                                    <option value="">اختر الصنف</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="transferQuantity" class="form-label">الكمية *</label>
                                <input type="number" class="form-control" id="transferQuantity" step="0.001" required>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="transferDate" class="form-label">تاريخ النقل *</label>
                                <input type="date" class="form-control" id="transferDate" required>
                            </div>
                            <div class="col-md-6">
                                <label for="transferReference" class="form-label">رقم المرجع</label>
                                <input type="text" class="form-control" id="transferReference">
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-12">
                                <label for="transferNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="transferNotes" rows="3"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="executeTransfer()">تنفيذ النقل</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Warehouse JS Files -->
    <script src="warehouse-functions.js"></script>
    <script src="print-system.js"></script>
    <script src="system-init.js"></script>

    <script>
        // بيانات المخازن التجريبية
        let warehouses = [
            {
                id: 1,
                name: 'المخزن الرئيسي',
                code: 'WH001',
                type: 'main',
                manager: 'أحمد محمد',
                address: 'صنعاء - شارع الزبيري',
                phone: '+967 1 234567',
                capacity: 500.00,
                isActive: true,
                allowNegative: false,
                notes: 'المخزن الرئيسي للمواد الخام',
                createdDate: '2024-01-01'
            },
            {
                id: 2,
                name: 'مخزن المنتجات الجاهزة',
                code: 'WH002',
                type: 'branch',
                manager: 'فاطمة علي',
                address: 'صنعاء - شارع الستين',
                phone: '+967 1 234568',
                capacity: 300.00,
                isActive: true,
                allowNegative: false,
                notes: 'مخزن المنتجات النهائية',
                createdDate: '2024-01-01'
            },
            {
                id: 3,
                name: 'مخزن الإنتاج',
                code: 'WH003',
                type: 'production',
                manager: 'محمد حسن',
                address: 'صنعاء - المنطقة الصناعية',
                phone: '+967 1 234569',
                capacity: 200.00,
                isActive: true,
                allowNegative: true,
                notes: 'مخزن خط الإنتاج',
                createdDate: '2024-01-01'
            },
            {
                id: 4,
                name: 'مخزن التالف',
                code: 'WH004',
                type: 'damaged',
                manager: 'سارة أحمد',
                address: 'صنعاء - المنطقة الخارجية',
                phone: '+967 1 234570',
                capacity: 50.00,
                isActive: true,
                allowNegative: false,
                notes: 'مخزن المنتجات التالفة',
                createdDate: '2024-01-01'
            }
        ];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // طباعة الصفحة
        function printPage() {
            window.print();
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            displayWarehouses();
            updateWarehouseStats();
        });
    </script>
</body>
</html>