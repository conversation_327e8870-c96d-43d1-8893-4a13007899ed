<?php
/**
 * إضافة مستخدم جديد
 * Create New User
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('users');

$page_title = 'إضافة مستخدم جديد';
$error_message = '';
$success_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    checkCSRF();
    
    try {
        $data = [
            'username' => cleanInput($_POST['username']),
            'password' => $_POST['password'],
            'confirm_password' => $_POST['confirm_password'],
            'full_name' => cleanInput($_POST['full_name']),
            'email' => cleanInput($_POST['email']),
            'phone' => cleanInput($_POST['phone']),
            'role' => cleanInput($_POST['role']),
            'is_active' => isset($_POST['is_active']) ? 1 : 0
        ];
        
        // التحقق من صحة البيانات
        if (empty($data['username'])) {
            throw new Exception('اسم المستخدم مطلوب');
        }
        
        if (strlen($data['username']) < 3) {
            throw new Exception('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
        }
        
        if (!preg_match('/^[a-zA-Z0-9_]+$/', $data['username'])) {
            throw new Exception('اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط');
        }
        
        if (empty($data['password'])) {
            throw new Exception('كلمة المرور مطلوبة');
        }
        
        if (strlen($data['password']) < 6) {
            throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
        }
        
        if ($data['password'] !== $data['confirm_password']) {
            throw new Exception('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
        }
        
        if (empty($data['full_name'])) {
            throw new Exception('الاسم الكامل مطلوب');
        }
        
        if (!empty($data['email']) && !isValidEmail($data['email'])) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        if (!empty($data['phone']) && !isValidPhone($data['phone'])) {
            throw new Exception('رقم الهاتف غير صحيح');
        }
        
        if (empty($data['role'])) {
            throw new Exception('الدور مطلوب');
        }
        
        if (!array_key_exists($data['role'], USER_ROLES)) {
            throw new Exception('الدور المحدد غير صحيح');
        }
        
        // التحقق من عدم تكرار اسم المستخدم
        $existingUser = queryOne(
            "SELECT id FROM users WHERE username = ?",
            [$data['username']]
        );
        
        if ($existingUser) {
            throw new Exception('اسم المستخدم موجود مسبقاً');
        }
        
        // التحقق من عدم تكرار البريد الإلكتروني
        if (!empty($data['email'])) {
            $existingEmail = queryOne(
                "SELECT id FROM users WHERE email = ?",
                [$data['email']]
            );
            
            if ($existingEmail) {
                throw new Exception('البريد الإلكتروني موجود مسبقاً');
            }
        }
        
        // تشفير كلمة المرور
        $hashedPassword = password_hash($data['password'], PASSWORD_DEFAULT);
        
        // إعداد الصلاحيات الافتراضية حسب الدور
        $permissions = [];
        switch ($data['role']) {
            case 'admin':
                $permissions = ['all' => true];
                break;
            case 'manager':
                $permissions = [
                    'company' => true,
                    'accounts' => true,
                    'users' => true,
                    'cash_banks' => true,
                    'employees' => true,
                    'inventory' => true,
                    'invoices' => true,
                    'vouchers' => true,
                    'assets' => true,
                    'reports' => true
                ];
                break;
            case 'accountant':
                $permissions = [
                    'accounts' => true,
                    'cash_banks' => true,
                    'employees' => true,
                    'invoices' => true,
                    'vouchers' => true,
                    'assets' => true,
                    'reports' => true
                ];
                break;
            case 'cashier':
                $permissions = [
                    'cash_banks' => true,
                    'invoices' => true,
                    'vouchers' => true,
                    'reports' => true
                ];
                break;
            case 'user':
                $permissions = [
                    'reports' => true
                ];
                break;
        }
        
        // إدراج المستخدم الجديد
        execute(
            "INSERT INTO users (
                username, password, full_name, email, phone, role, permissions, 
                is_active, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $data['username'], $hashedPassword, $data['full_name'],
                $data['email'], $data['phone'], $data['role'],
                json_encode($permissions, JSON_UNESCAPED_UNICODE),
                $data['is_active'], $_SESSION['user_id']
            ]
        );
        
        $userId = getDB()->lastInsertId();
        
        // تسجيل النشاط
        logUserActivity('create', 'users', $userId, null, array_merge($data, ['permissions' => $permissions]));
        
        $success_message = 'تم إضافة المستخدم بنجاح';
        
        // إعادة توجيه بعد النجاح
        header('Location: index.php?success=1');
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        logError("خطأ في إضافة المستخدم: " . $e->getMessage());
    }
}

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-person-plus text-primary me-2"></i>
        إضافة مستخدم جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-person me-2"></i>
                    بيانات المستخدم الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="user-form">
                    <?php echo csrfField(); ?>
                    
                    <div class="row">
                        <!-- معلومات تسجيل الدخول -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-key me-2"></i>
                                معلومات تسجيل الدخول
                            </h6>
                            
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم *</label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                                <div class="form-text">أحرف وأرقام فقط، 3 أحرف على الأقل</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                                <div class="form-text">6 أحرف على الأقل</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="role" class="form-label">الدور *</label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">اختر الدور</option>
                                    <?php foreach (USER_ROLES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo (isset($_POST['role']) && $_POST['role'] == $key) ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?php echo (!isset($_POST['is_active']) || $_POST['is_active']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        مستخدم نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- المعلومات الشخصية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-person me-2"></i>
                                المعلومات الشخصية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="full_name" class="form-label">الاسم الكامل *</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" 
                                       value="<?php echo htmlspecialchars($_POST['full_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($_POST['email'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="<?php echo htmlspecialchars($_POST['phone'] ?? ''); ?>">
                            </div>
                            
                            <!-- معاينة الصلاحيات -->
                            <div class="alert alert-info" id="permissions-preview" style="display: none;">
                                <h6><i class="bi bi-shield-check me-2"></i>الصلاحيات المتاحة:</h6>
                                <div id="permissions-list"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="bi bi-x-lg me-1"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-1"></i>
                                    حفظ المستخدم
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    // تهيئة النموذج
    document.addEventListener('DOMContentLoaded', function() {
        const roleSelect = document.getElementById('role');
        const permissionsPreview = document.getElementById('permissions-preview');
        const permissionsList = document.getElementById('permissions-list');
        
        // إظهار/إخفاء كلمة المرور
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordField.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
        
        document.getElementById('toggleConfirmPassword').addEventListener('click', function() {
            const passwordField = document.getElementById('confirm_password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.className = 'bi bi-eye-slash';
            } else {
                passwordField.type = 'password';
                icon.className = 'bi bi-eye';
            }
        });
        
        // عرض الصلاحيات حسب الدور
        const rolePermissions = {
            'admin': ['جميع الصلاحيات'],
            'manager': ['إعدادات المنشأة', 'شجرة الحسابات', 'إدارة المستخدمين', 'الصناديق والبنوك', 'الموظفين والرواتب', 'إدارة المخزون', 'الفواتير', 'سندات القبض والصرف', 'الأصول الثابتة', 'التقارير'],
            'accountant': ['شجرة الحسابات', 'الصناديق والبنوك', 'الموظفين والرواتب', 'الفواتير', 'سندات القبض والصرف', 'الأصول الثابتة', 'التقارير'],
            'cashier': ['الصناديق والبنوك', 'الفواتير', 'سندات القبض والصرف', 'التقارير'],
            'user': ['التقارير']
        };
        
        roleSelect.addEventListener('change', function() {
            const selectedRole = this.value;
            
            if (selectedRole && rolePermissions[selectedRole]) {
                const permissions = rolePermissions[selectedRole];
                permissionsList.innerHTML = permissions.map(p => '<span class=\"badge bg-secondary me-1 mb-1\">' + p + '</span>').join('');
                permissionsPreview.style.display = 'block';
            } else {
                permissionsPreview.style.display = 'none';
            }
        });
        
        // التحقق من صحة النموذج
        document.getElementById('user-form').addEventListener('submit', function(e) {
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const confirmPassword = document.getElementById('confirm_password').value;
            const fullName = document.getElementById('full_name').value.trim();
            const role = document.getElementById('role').value;
            
            if (!username || !password || !confirmPassword || !fullName || !role) {
                e.preventDefault();
                showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            if (username.length < 3) {
                e.preventDefault();
                showErrorMessage('اسم المستخدم يجب أن يكون 3 أحرف على الأقل');
                return false;
            }
            
            if (password.length < 6) {
                e.preventDefault();
                showErrorMessage('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
                return false;
            }
            
            if (password !== confirmPassword) {
                e.preventDefault();
                showErrorMessage('كلمة المرور وتأكيد كلمة المرور غير متطابقتين');
                return false;
            }
            
            showLoading(true);
        });
    });
";

include __DIR__ . '/../../includes/footer.php';
?>
