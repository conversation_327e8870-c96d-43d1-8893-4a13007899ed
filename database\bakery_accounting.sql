-- ===================================================
-- قاعدة بيانات نظام المحاسبة للمخبز
-- Bakery Accounting System Database
-- ===================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS `bakery_accounting`
DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `bakery_accounting`;

-- ===================================================
-- 1. جدول بيانات المنشأة
-- ===================================================
CREATE TABLE `company_settings` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `company_name` varchar(255) NOT NULL COMMENT 'اسم المنشأة',
  `company_name_en` varchar(255) DEFAULT NULL COMMENT 'اسم المنشأة بالإنجليزية',
  `address` text COMMENT 'العنوان',
  `phone1` varchar(20) DEFAULT NULL COMMENT 'الهاتف الأول',
  `phone2` varchar(20) DEFAULT NULL COMMENT 'الهاتف الثاني',
  `email` varchar(100) DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `website` varchar(100) DEFAULT NULL COMMENT 'الموقع الإلكتروني',
  `logo` varchar(255) DEFAULT NULL COMMENT 'شعار المنشأة',
  `currency` varchar(10) DEFAULT 'YER' COMMENT 'العملة',
  `currency_symbol` varchar(10) DEFAULT 'ر.ي' COMMENT 'رمز العملة',
  `tax_rate` decimal(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
  `language` varchar(10) DEFAULT 'ar' COMMENT 'اللغة',
  `date_format` varchar(20) DEFAULT 'Y-m-d' COMMENT 'تنسيق التاريخ',
  `print_format` enum('normal','thermal') DEFAULT 'normal' COMMENT 'تنسيق الطباعة',
  `auto_backup` tinyint(1) DEFAULT 1 COMMENT 'النسخ الاحتياطي التلقائي',
  `backup_frequency` enum('daily','weekly','monthly') DEFAULT 'daily' COMMENT 'تكرار النسخ الاحتياطي',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='إعدادات المنشأة';

-- ===================================================
-- 2. جدول المستخدمين
-- ===================================================
CREATE TABLE `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL UNIQUE COMMENT 'اسم المستخدم',
  `password` varchar(255) NOT NULL COMMENT 'كلمة المرور',
  `full_name` varchar(100) NOT NULL COMMENT 'الاسم الكامل',
  `email` varchar(100) DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `role` enum('admin','manager','accountant','cashier','user') DEFAULT 'user' COMMENT 'الدور',
  `permissions` text COMMENT 'الصلاحيات JSON',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `last_login` timestamp NULL DEFAULT NULL COMMENT 'آخر تسجيل دخول',
  `created_by` int(11) DEFAULT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_users_created_by` (`created_by`),
  CONSTRAINT `fk_users_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='المستخدمين';

-- ===================================================
-- 3. جدول سجل العمليات
-- ===================================================
CREATE TABLE `user_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT 'المستخدم',
  `action` varchar(50) NOT NULL COMMENT 'العملية',
  `table_name` varchar(50) DEFAULT NULL COMMENT 'اسم الجدول',
  `record_id` int(11) DEFAULT NULL COMMENT 'معرف السجل',
  `old_data` text COMMENT 'البيانات القديمة JSON',
  `new_data` text COMMENT 'البيانات الجديدة JSON',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'عنوان IP',
  `user_agent` text COMMENT 'معلومات المتصفح',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_user_logs_user` (`user_id`),
  KEY `idx_user_logs_action` (`action`),
  KEY `idx_user_logs_table` (`table_name`),
  KEY `idx_user_logs_date` (`created_at`),
  CONSTRAINT `fk_user_logs_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سجل عمليات المستخدمين';

-- ===================================================
-- 4. جدول شجرة الحسابات
-- ===================================================
CREATE TABLE `chart_of_accounts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `account_code` varchar(20) NOT NULL UNIQUE COMMENT 'رمز الحساب',
  `account_name` varchar(255) NOT NULL COMMENT 'اسم الحساب',
  `account_name_en` varchar(255) DEFAULT NULL COMMENT 'اسم الحساب بالإنجليزية',
  `parent_id` int(11) DEFAULT NULL COMMENT 'الحساب الأب',
  `account_type` enum('asset','liability','equity','revenue','expense') NOT NULL COMMENT 'نوع الحساب',
  `account_nature` enum('debit','credit') NOT NULL COMMENT 'طبيعة الحساب',
  `level` int(2) NOT NULL DEFAULT 1 COMMENT 'مستوى الحساب',
  `is_main` tinyint(1) DEFAULT 0 COMMENT 'حساب رئيسي',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `opening_balance` decimal(15,3) DEFAULT 0.000 COMMENT 'الرصيد الافتتاحي',
  `current_balance` decimal(15,3) DEFAULT 0.000 COMMENT 'الرصيد الحالي',
  `description` text COMMENT 'الوصف',
  `created_by` int(11) DEFAULT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_accounts_parent` (`parent_id`),
  KEY `fk_accounts_created_by` (`created_by`),
  KEY `idx_accounts_code` (`account_code`),
  KEY `idx_accounts_type` (`account_type`),
  CONSTRAINT `fk_accounts_parent` FOREIGN KEY (`parent_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_accounts_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='شجرة الحسابات';

-- ===================================================
-- 5. جدول القيود المحاسبية
-- ===================================================
CREATE TABLE `journal_entries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `entry_number` varchar(20) NOT NULL UNIQUE COMMENT 'رقم القيد',
  `entry_date` date NOT NULL COMMENT 'تاريخ القيد',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `description` text COMMENT 'البيان',
  `total_debit` decimal(15,3) NOT NULL DEFAULT 0.000 COMMENT 'إجمالي المدين',
  `total_credit` decimal(15,3) NOT NULL DEFAULT 0.000 COMMENT 'إجمالي الدائن',
  `is_balanced` tinyint(1) DEFAULT 0 COMMENT 'متوازن',
  `is_posted` tinyint(1) DEFAULT 0 COMMENT 'مرحل',
  `posted_by` int(11) DEFAULT NULL COMMENT 'رحل بواسطة',
  `posted_at` timestamp NULL DEFAULT NULL COMMENT 'تاريخ الترحيل',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_journal_created_by` (`created_by`),
  KEY `fk_journal_posted_by` (`posted_by`),
  KEY `idx_journal_date` (`entry_date`),
  KEY `idx_journal_reference` (`reference_type`, `reference_id`),
  CONSTRAINT `fk_journal_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_journal_posted_by` FOREIGN KEY (`posted_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='القيود المحاسبية';

-- ===================================================
-- 6. جدول تفاصيل القيود المحاسبية
-- ===================================================
CREATE TABLE `journal_entry_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `journal_entry_id` int(11) NOT NULL COMMENT 'القيد المحاسبي',
  `account_id` int(11) NOT NULL COMMENT 'الحساب',
  `debit_amount` decimal(15,3) DEFAULT 0.000 COMMENT 'المبلغ المدين',
  `credit_amount` decimal(15,3) DEFAULT 0.000 COMMENT 'المبلغ الدائن',
  `description` text COMMENT 'البيان',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_journal_details_entry` (`journal_entry_id`),
  KEY `fk_journal_details_account` (`account_id`),
  CONSTRAINT `fk_journal_details_entry` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_journal_details_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تفاصيل القيود المحاسبية';

-- ===================================================
-- 7. جدول الصناديق والبنوك
-- ===================================================
CREATE TABLE `cash_banks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT 'اسم الصندوق/البنك',
  `type` enum('cash','bank') NOT NULL COMMENT 'النوع',
  `account_id` int(11) NOT NULL COMMENT 'الحساب المرتبط',
  `responsible_user_id` int(11) DEFAULT NULL COMMENT 'المستخدم المسؤول',
  `opening_balance` decimal(15,3) DEFAULT 0.000 COMMENT 'الرصيد الافتتاحي',
  `current_balance` decimal(15,3) DEFAULT 0.000 COMMENT 'الرصيد الحالي',
  `bank_name` varchar(100) DEFAULT NULL COMMENT 'اسم البنك',
  `account_number` varchar(50) DEFAULT NULL COMMENT 'رقم الحساب',
  `iban` varchar(50) DEFAULT NULL COMMENT 'رقم الآيبان',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_cash_banks_account` (`account_id`),
  KEY `fk_cash_banks_responsible` (`responsible_user_id`),
  KEY `fk_cash_banks_created_by` (`created_by`),
  CONSTRAINT `fk_cash_banks_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_cash_banks_responsible` FOREIGN KEY (`responsible_user_id`) REFERENCES `users` (`id`),
  CONSTRAINT `fk_cash_banks_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الصناديق والبنوك';

-- ===================================================
-- 8. جدول الموظفين
-- ===================================================
CREATE TABLE `employees` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_code` varchar(20) NOT NULL UNIQUE COMMENT 'رمز الموظف',
  `full_name` varchar(100) NOT NULL COMMENT 'الاسم الكامل',
  `position` varchar(100) DEFAULT NULL COMMENT 'المنصب',
  `department` varchar(100) DEFAULT NULL COMMENT 'القسم',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `email` varchar(100) DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `address` text COMMENT 'العنوان',
  `hire_date` date NOT NULL COMMENT 'تاريخ التعيين',
  `basic_salary` decimal(10,3) DEFAULT 0.000 COMMENT 'الراتب الأساسي',
  `allowances` decimal(10,3) DEFAULT 0.000 COMMENT 'البدلات',
  `account_id` int(11) DEFAULT NULL COMMENT 'الحساب المرتبط',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_employees_account` (`account_id`),
  KEY `fk_employees_created_by` (`created_by`),
  CONSTRAINT `fk_employees_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_employees_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الموظفين';

-- ===================================================
-- 9. جدول رواتب الموظفين
-- ===================================================
CREATE TABLE `employee_salaries` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `employee_id` int(11) NOT NULL COMMENT 'الموظف',
  `salary_month` date NOT NULL COMMENT 'شهر الراتب',
  `basic_salary` decimal(10,3) NOT NULL DEFAULT 0.000 COMMENT 'الراتب الأساسي',
  `allowances` decimal(10,3) DEFAULT 0.000 COMMENT 'البدلات',
  `overtime` decimal(10,3) DEFAULT 0.000 COMMENT 'الإضافي',
  `bonuses` decimal(10,3) DEFAULT 0.000 COMMENT 'المكافآت',
  `deductions` decimal(10,3) DEFAULT 0.000 COMMENT 'الخصومات',
  `advances` decimal(10,3) DEFAULT 0.000 COMMENT 'السلف',
  `net_salary` decimal(10,3) NOT NULL DEFAULT 0.000 COMMENT 'صافي الراتب',
  `is_paid` tinyint(1) DEFAULT 0 COMMENT 'مدفوع',
  `payment_date` date DEFAULT NULL COMMENT 'تاريخ الدفع',
  `payment_method` enum('cash','bank','transfer') DEFAULT 'cash' COMMENT 'طريقة الدفع',
  `cash_bank_id` int(11) DEFAULT NULL COMMENT 'الصندوق/البنك',
  `journal_entry_id` int(11) DEFAULT NULL COMMENT 'القيد المحاسبي',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_employee_month` (`employee_id`, `salary_month`),
  KEY `fk_salaries_employee` (`employee_id`),
  KEY `fk_salaries_cash_bank` (`cash_bank_id`),
  KEY `fk_salaries_journal` (`journal_entry_id`),
  KEY `fk_salaries_created_by` (`created_by`),
  CONSTRAINT `fk_salaries_employee` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`),
  CONSTRAINT `fk_salaries_cash_bank` FOREIGN KEY (`cash_bank_id`) REFERENCES `cash_banks` (`id`),
  CONSTRAINT `fk_salaries_journal` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`),
  CONSTRAINT `fk_salaries_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='رواتب الموظفين';
