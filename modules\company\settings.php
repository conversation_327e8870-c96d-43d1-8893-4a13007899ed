<?php
/**
 * إعدادات المنشأة
 * Company Settings
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('company');

$page_title = 'إعدادات المنشأة';
$error_message = '';
$success_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    checkCSRF();
    
    try {
        $data = [
            'company_name' => cleanInput($_POST['company_name']),
            'company_name_en' => cleanInput($_POST['company_name_en']),
            'address' => cleanInput($_POST['address']),
            'phone1' => cleanInput($_POST['phone1']),
            'phone2' => cleanInput($_POST['phone2']),
            'email' => cleanInput($_POST['email']),
            'website' => cleanInput($_POST['website']),
            'currency' => cleanInput($_POST['currency']),
            'currency_symbol' => cleanInput($_POST['currency_symbol']),
            'tax_rate' => floatval($_POST['tax_rate']),
            'language' => cleanInput($_POST['language']),
            'date_format' => cleanInput($_POST['date_format']),
            'print_format' => cleanInput($_POST['print_format']),
            'auto_backup' => isset($_POST['auto_backup']) ? 1 : 0,
            'backup_frequency' => cleanInput($_POST['backup_frequency'])
        ];
        
        // التحقق من صحة البيانات
        if (empty($data['company_name'])) {
            throw new Exception('اسم المنشأة مطلوب');
        }
        
        if (!empty($data['email']) && !isValidEmail($data['email'])) {
            throw new Exception('البريد الإلكتروني غير صحيح');
        }
        
        // معالجة رفع الشعار
        $logo_path = null;
        if (isset($_FILES['logo']) && $_FILES['logo']['error'] === UPLOAD_ERR_OK) {
            $upload_dir = UPLOADS_PATH . '/logos/';
            
            // إنشاء المجلد إذا لم يكن موجوداً
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            $file_extension = strtolower(pathinfo($_FILES['logo']['name'], PATHINFO_EXTENSION));
            
            if (!in_array($file_extension, ALLOWED_IMAGE_TYPES)) {
                throw new Exception('نوع الملف غير مدعوم. الأنواع المدعومة: ' . implode(', ', ALLOWED_IMAGE_TYPES));
            }
            
            if ($_FILES['logo']['size'] > MAX_FILE_SIZE) {
                throw new Exception('حجم الملف كبير جداً. الحد الأقصى: ' . (MAX_FILE_SIZE / 1024 / 1024) . ' ميجابايت');
            }
            
            $logo_filename = 'logo_' . time() . '.' . $file_extension;
            $logo_path = $upload_dir . $logo_filename;
            
            if (!move_uploaded_file($_FILES['logo']['tmp_name'], $logo_path)) {
                throw new Exception('خطأ في رفع الشعار');
            }
            
            $data['logo'] = 'uploads/logos/' . $logo_filename;
        }
        
        // التحقق من وجود إعدادات سابقة
        $existing_settings = queryOne("SELECT id FROM company_settings WHERE id = 1");
        
        if ($existing_settings) {
            // تحديث الإعدادات الموجودة
            $sql = "UPDATE company_settings SET 
                    company_name = ?, company_name_en = ?, address = ?, phone1 = ?, phone2 = ?, 
                    email = ?, website = ?, currency = ?, currency_symbol = ?, tax_rate = ?, 
                    language = ?, date_format = ?, print_format = ?, auto_backup = ?, backup_frequency = ?";
            
            $params = [
                $data['company_name'], $data['company_name_en'], $data['address'], 
                $data['phone1'], $data['phone2'], $data['email'], $data['website'],
                $data['currency'], $data['currency_symbol'], $data['tax_rate'],
                $data['language'], $data['date_format'], $data['print_format'],
                $data['auto_backup'], $data['backup_frequency']
            ];
            
            if ($logo_path) {
                $sql .= ", logo = ?";
                $params[] = $data['logo'];
            }
            
            $sql .= ", updated_at = NOW() WHERE id = 1";
            
            execute($sql, $params);
            
            // تسجيل النشاط
            logUserActivity('update', 'company_settings', 1, $existing_settings, $data);
            
        } else {
            // إدراج إعدادات جديدة
            $sql = "INSERT INTO company_settings (
                    company_name, company_name_en, address, phone1, phone2, email, website,
                    currency, currency_symbol, tax_rate, language, date_format, print_format,
                    auto_backup, backup_frequency" . ($logo_path ? ", logo" : "") . "
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?" . ($logo_path ? ", ?" : "") . ")";
            
            $params = [
                $data['company_name'], $data['company_name_en'], $data['address'],
                $data['phone1'], $data['phone2'], $data['email'], $data['website'],
                $data['currency'], $data['currency_symbol'], $data['tax_rate'],
                $data['language'], $data['date_format'], $data['print_format'],
                $data['auto_backup'], $data['backup_frequency']
            ];
            
            if ($logo_path) {
                $params[] = $data['logo'];
            }
            
            execute($sql, $params);
            
            // تسجيل النشاط
            logUserActivity('create', 'company_settings', 1, null, $data);
        }
        
        $success_message = 'تم حفظ إعدادات المنشأة بنجاح';
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        logError("خطأ في حفظ إعدادات المنشأة: " . $e->getMessage());
    }
}

// الحصول على الإعدادات الحالية
$settings = queryOne("SELECT * FROM company_settings WHERE id = 1") ?: [];

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-building text-primary me-2"></i>
        إعدادات المنشأة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
<?php endif; ?>

<?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-gear me-2"></i>
                    بيانات المنشأة الأساسية
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" enctype="multipart/form-data" id="company-settings-form">
                    <?php echo csrfField(); ?>
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="company_name" class="form-label">اسم المنشأة *</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="<?php echo htmlspecialchars($settings['company_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="company_name_en" class="form-label">اسم المنشأة بالإنجليزية</label>
                                <input type="text" class="form-control" id="company_name_en" name="company_name_en" 
                                       value="<?php echo htmlspecialchars($settings['company_name_en'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"><?php echo htmlspecialchars($settings['address'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone1" class="form-label">الهاتف الأول</label>
                                        <input type="tel" class="form-control" id="phone1" name="phone1" 
                                               value="<?php echo htmlspecialchars($settings['phone1'] ?? ''); ?>">
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="phone2" class="form-label">الهاتف الثاني</label>
                                        <input type="tel" class="form-control" id="phone2" name="phone2" 
                                               value="<?php echo htmlspecialchars($settings['phone2'] ?? ''); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($settings['email'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="website" class="form-label">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" id="website" name="website" 
                                       value="<?php echo htmlspecialchars($settings['website'] ?? ''); ?>">
                            </div>
                        </div>
                        
                        <!-- الإعدادات المالية والتقنية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-currency-exchange me-2"></i>
                                الإعدادات المالية والتقنية
                            </h6>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency" class="form-label">العملة</label>
                                        <select class="form-select" id="currency" name="currency">
                                            <option value="YER" <?php echo ($settings['currency'] ?? 'YER') === 'YER' ? 'selected' : ''; ?>>ريال يمني</option>
                                            <option value="SAR" <?php echo ($settings['currency'] ?? '') === 'SAR' ? 'selected' : ''; ?>>ريال سعودي</option>
                                            <option value="USD" <?php echo ($settings['currency'] ?? '') === 'USD' ? 'selected' : ''; ?>>دولار أمريكي</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="currency_symbol" class="form-label">رمز العملة</label>
                                        <input type="text" class="form-control" id="currency_symbol" name="currency_symbol" 
                                               value="<?php echo htmlspecialchars($settings['currency_symbol'] ?? 'ر.ي'); ?>">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="tax_rate" class="form-label">نسبة الضريبة (%)</label>
                                <input type="number" class="form-control" id="tax_rate" name="tax_rate" 
                                       min="0" max="100" step="0.01" 
                                       value="<?php echo $settings['tax_rate'] ?? '0.00'; ?>">
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="language" class="form-label">اللغة</label>
                                        <select class="form-select" id="language" name="language">
                                            <option value="ar" <?php echo ($settings['language'] ?? 'ar') === 'ar' ? 'selected' : ''; ?>>العربية</option>
                                            <option value="en" <?php echo ($settings['language'] ?? '') === 'en' ? 'selected' : ''; ?>>English</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="date_format" class="form-label">تنسيق التاريخ</label>
                                        <select class="form-select" id="date_format" name="date_format">
                                            <option value="Y-m-d" <?php echo ($settings['date_format'] ?? 'Y-m-d') === 'Y-m-d' ? 'selected' : ''; ?>>YYYY-MM-DD</option>
                                            <option value="d/m/Y" <?php echo ($settings['date_format'] ?? '') === 'd/m/Y' ? 'selected' : ''; ?>>DD/MM/YYYY</option>
                                            <option value="m/d/Y" <?php echo ($settings['date_format'] ?? '') === 'm/d/Y' ? 'selected' : ''; ?>>MM/DD/YYYY</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="print_format" class="form-label">تنسيق الطباعة</label>
                                <select class="form-select" id="print_format" name="print_format">
                                    <option value="normal" <?php echo ($settings['print_format'] ?? 'normal') === 'normal' ? 'selected' : ''; ?>>عادي</option>
                                    <option value="thermal" <?php echo ($settings['print_format'] ?? '') === 'thermal' ? 'selected' : ''; ?>>حراري</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="logo" class="form-label">شعار المنشأة</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">الأنواع المدعومة: JPG, PNG, GIF. الحد الأقصى: 5 ميجابايت</div>
                                <?php if (!empty($settings['logo'])): ?>
                                    <div class="mt-2">
                                        <img src="<?php echo BASE_URL . '/' . $settings['logo']; ?>" 
                                             alt="شعار المنشأة" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <h6 class="text-primary mb-3 mt-4">
                                <i class="bi bi-shield-check me-2"></i>
                                إعدادات النسخ الاحتياطي
                            </h6>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="auto_backup" name="auto_backup" 
                                           <?php echo ($settings['auto_backup'] ?? 1) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="auto_backup">
                                        تفعيل النسخ الاحتياطي التلقائي
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="backup_frequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                <select class="form-select" id="backup_frequency" name="backup_frequency">
                                    <option value="daily" <?php echo ($settings['backup_frequency'] ?? 'daily') === 'daily' ? 'selected' : ''; ?>>يومي</option>
                                    <option value="weekly" <?php echo ($settings['backup_frequency'] ?? '') === 'weekly' ? 'selected' : ''; ?>>أسبوعي</option>
                                    <option value="monthly" <?php echo ($settings['backup_frequency'] ?? '') === 'monthly' ? 'selected' : ''; ?>>شهري</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="button" class="btn btn-secondary" onclick="location.reload()">
                                    <i class="bi bi-arrow-clockwise me-1"></i>
                                    إلغاء
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-1"></i>
                                    حفظ الإعدادات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    // تهيئة النموذج
    document.getElementById('company-settings-form').addEventListener('submit', function(e) {
        const companyName = document.getElementById('company_name').value.trim();
        
        if (!companyName) {
            e.preventDefault();
            showErrorMessage('اسم المنشأة مطلوب');
            return false;
        }
        
        showLoading(true);
    });
    
    // تفعيل/تعطيل تكرار النسخ الاحتياطي
    document.getElementById('auto_backup').addEventListener('change', function() {
        const backupFrequency = document.getElementById('backup_frequency');
        backupFrequency.disabled = !this.checked;
    });
    
    // تهيئة حالة تكرار النسخ الاحتياطي
    document.addEventListener('DOMContentLoaded', function() {
        const autoBackup = document.getElementById('auto_backup');
        const backupFrequency = document.getElementById('backup_frequency');
        backupFrequency.disabled = !autoBackup.checked;
    });
";

include __DIR__ . '/../../includes/footer.php';
?>
