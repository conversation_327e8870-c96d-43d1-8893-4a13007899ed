<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المنتجات والوصفات</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .product-image {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.5rem;
        }

        .recipe-ingredient {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            border-left: 3px solid #667eea;
        }

        .cost-breakdown {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 10px;
            padding: 1rem;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            font-weight: 600;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 2rem;
        }

        .unit-converter {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1rem;
        }

        .damage-alert {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 10px;
            padding: 1rem;
            color: #721c24;
        }

        .print-section {
            background: white;
            padding: 2rem;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        @media print {
            .sidebar, .btn, .action-buttons {
                display: none !important;
            }
            .main-content {
                margin-right: 0 !important;
            }
            .print-section {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="products.html" class="active">
                    <i class="bi bi-basket"></i>
                    <span class="menu-text">المنتجات والوصفات</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-basket text-primary me-2"></i>
                    المنتجات والوصفات
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddProductModal()">
                        <i class="bi bi-plus-lg me-1"></i>إضافة منتج
                    </button>
                    <button type="button" class="btn btn-success" onclick="showProductionModal()">
                        <i class="bi bi-gear me-1"></i>إنتاج
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showDamageModal()">
                        <i class="bi bi-exclamation-triangle me-1"></i>تالف
                    </button>
                    <button type="button" class="btn btn-info" onclick="showUnitConverterModal()">
                        <i class="bi bi-arrow-repeat me-1"></i>تحويل وحدات
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>

            <!-- إحصائيات المنتجات -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #28a745;">
                        <div class="icon text-success">
                            <i class="bi bi-basket"></i>
                        </div>
                        <div class="number text-success" id="totalProducts">15</div>
                        <div class="label">إجمالي المنتجات</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #17a2b8;">
                        <div class="icon text-info">
                            <i class="bi bi-list-check"></i>
                        </div>
                        <div class="number text-info" id="totalRecipes">12</div>
                        <div class="label">الوصفات النشطة</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #ffc107;">
                        <div class="icon text-warning">
                            <i class="bi bi-currency-exchange"></i>
                        </div>
                        <div class="number text-warning" id="avgProductionCost">2,450</div>
                        <div class="label">متوسط تكلفة الإنتاج (ر.ي)</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #dc3545;">
                        <div class="icon text-danger">
                            <i class="bi bi-exclamation-triangle"></i>
                        </div>
                        <div class="number text-danger" id="damagedProducts">8</div>
                        <div class="label">منتجات تالفة اليوم</div>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="card">
                <div class="card-header p-0">
                    <ul class="nav nav-tabs" id="productTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="products-tab" data-bs-toggle="tab" data-bs-target="#products" type="button" role="tab">
                                <i class="bi bi-basket me-2"></i>المنتجات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="recipes-tab" data-bs-toggle="tab" data-bs-target="#recipes" type="button" role="tab">
                                <i class="bi bi-list-check me-2"></i>الوصفات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="production-tab" data-bs-toggle="tab" data-bs-target="#production" type="button" role="tab">
                                <i class="bi bi-gear me-2"></i>الإنتاج
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="damage-tab" data-bs-toggle="tab" data-bs-target="#damage" type="button" role="tab">
                                <i class="bi bi-exclamation-triangle me-2"></i>التالف
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="units-tab" data-bs-toggle="tab" data-bs-target="#units" type="button" role="tab">
                                <i class="bi bi-arrow-repeat me-2"></i>وحدات القياس
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="productTabContent">
                    <!-- تبويب المنتجات -->
                    <div class="tab-pane fade show active" id="products" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="searchInput" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في المنتجات...">
                            </div>
                            <div class="col-md-3">
                                <label for="categoryFilter" class="form-label">الفئة</label>
                                <select class="form-select" id="categoryFilter">
                                    <option value="">جميع الفئات</option>
                                    <option value="bread">خبز</option>
                                    <option value="pastry">معجنات</option>
                                    <option value="cake">كعك وحلويات</option>
                                    <option value="biscuit">بسكويت</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="statusFilter" class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="bi bi-x-lg me-1"></i>مسح
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- جدول المنتجات -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="productsTable">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>اسم المنتج</th>
                                        <th>الكود</th>
                                        <th>الفئة</th>
                                        <th>سعر البيع</th>
                                        <th>تكلفة الإنتاج</th>
                                        <th>هامش الربح</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="productsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب الوصفات -->
                    <div class="tab-pane fade" id="recipes" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="recipeProductFilter" class="form-label">المنتج</label>
                                <select class="form-select" id="recipeProductFilter" onchange="loadRecipeDetails()">
                                    <option value="">اختر المنتج</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="showRecipeModal()">
                                        <i class="bi bi-plus-lg me-1"></i>إضافة وصفة
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div id="recipeDetails" class="d-none">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="card">
                                        <div class="card-header">
                                            <h6 class="card-title mb-0">مكونات الوصفة</h6>
                                        </div>
                                        <div class="card-body">
                                            <div id="recipeIngredients">
                                                <!-- سيتم ملؤها بـ JavaScript -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-4">
                                    <div class="cost-breakdown">
                                        <h6 class="text-primary mb-3">تحليل التكلفة</h6>
                                        <div class="mb-2">
                                            <small class="text-muted">تكلفة المواد:</small>
                                            <div class="fw-bold" id="materialCost">0 ر.ي</div>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">تكلفة العمالة:</small>
                                            <div class="fw-bold" id="laborCost">0 ر.ي</div>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">تكاليف إضافية:</small>
                                            <div class="fw-bold" id="overheadCost">0 ر.ي</div>
                                        </div>
                                        <hr>
                                        <div class="mb-2">
                                            <small class="text-muted">إجمالي التكلفة:</small>
                                            <div class="fw-bold text-primary" id="totalCost">0 ر.ي</div>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">سعر البيع:</small>
                                            <div class="fw-bold text-success" id="sellingPrice">0 ر.ي</div>
                                        </div>
                                        <div class="mb-2">
                                            <small class="text-muted">هامش الربح:</small>
                                            <div class="fw-bold text-warning" id="profitMargin">0%</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- تبويب الإنتاج -->
                    <div class="tab-pane fade" id="production" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="productionDate" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="productionDate">
                            </div>
                            <div class="col-md-3">
                                <label for="productionShift" class="form-label">الوردية</label>
                                <select class="form-select" id="productionShift">
                                    <option value="morning">الصباحية</option>
                                    <option value="evening">المسائية</option>
                                    <option value="night">الليلية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="loadProductionData()">
                                        <i class="bi bi-search me-1"></i>عرض الإنتاج
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success" onclick="showProductionModal()">
                                        <i class="bi bi-plus-lg me-1"></i>تسجيل إنتاج
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="productionTable">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية المنتجة</th>
                                        <th>الوحدة</th>
                                        <th>تكلفة الإنتاج</th>
                                        <th>الوردية</th>
                                        <th>المسؤول</th>
                                        <th>ملاحظات</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="productionTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب التالف -->
                    <div class="tab-pane fade" id="damage" role="tabpanel">
                        <div class="damage-alert mb-4">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <strong>تنبيه:</strong> تسجيل المنتجات التالفة يؤثر على المخزون والحسابات المحاسبية.
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="damageDate" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="damageDate">
                            </div>
                            <div class="col-md-3">
                                <label for="damageReason" class="form-label">سبب التلف</label>
                                <select class="form-select" id="damageReason">
                                    <option value="">جميع الأسباب</option>
                                    <option value="expired">انتهاء صلاحية</option>
                                    <option value="broken">كسر</option>
                                    <option value="burnt">احتراق</option>
                                    <option value="contaminated">تلوث</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="loadDamageData()">
                                        <i class="bi bi-search me-1"></i>عرض التالف
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-warning" onclick="showDamageModal()">
                                        <i class="bi bi-plus-lg me-1"></i>تسجيل تالف
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="damageTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>سبب التلف</th>
                                        <th>القيمة</th>
                                        <th>المسؤول</th>
                                        <th>رقم القيد</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="damageTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب وحدات القياس -->
                    <div class="tab-pane fade" id="units" role="tabpanel">
                        <div class="unit-converter mb-4">
                            <h6 class="text-warning mb-3">
                                <i class="bi bi-arrow-repeat me-2"></i>
                                محول الوحدات
                            </h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="fromValue" class="form-label">القيمة</label>
                                    <input type="number" class="form-control" id="fromValue" step="0.001" onchange="convertUnits()">
                                </div>
                                <div class="col-md-3">
                                    <label for="fromUnit" class="form-label">من وحدة</label>
                                    <select class="form-select" id="fromUnit" onchange="convertUnits()">
                                        <option value="kg">كيلوجرام</option>
                                        <option value="g">جرام</option>
                                        <option value="l">لتر</option>
                                        <option value="ml">مليلتر</option>
                                        <option value="piece">قطعة</option>
                                        <option value="box">صندوق</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="toUnit" class="form-label">إلى وحدة</label>
                                    <select class="form-select" id="toUnit" onchange="convertUnits()">
                                        <option value="kg">كيلوجرام</option>
                                        <option value="g">جرام</option>
                                        <option value="l">لتر</option>
                                        <option value="ml">مليلتر</option>
                                        <option value="piece">قطعة</option>
                                        <option value="box">صندوق</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="toValue" class="form-label">النتيجة</label>
                                    <input type="text" class="form-control" id="toValue" readonly>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">وحدات الوزن</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الوحدة</th>
                                                        <th>المعادل بالجرام</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr><td>كيلوجرام</td><td>1000 جرام</td></tr>
                                                    <tr><td>جرام</td><td>1 جرام</td></tr>
                                                    <tr><td>رطل</td><td>453.592 جرام</td></tr>
                                                    <tr><td>أونصة</td><td>28.3495 جرام</td></tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">وحدات الحجم</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الوحدة</th>
                                                        <th>المعادل بالمليلتر</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr><td>لتر</td><td>1000 مليلتر</td></tr>
                                                    <tr><td>مليلتر</td><td>1 مليلتر</td></tr>
                                                    <tr><td>كوب</td><td>250 مليلتر</td></tr>
                                                    <tr><td>ملعقة كبيرة</td><td>15 مليلتر</td></tr>
                                                    <tr><td>ملعقة صغيرة</td><td>5 مليلتر</td></tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة منتج جديد -->
    <div class="modal fade" id="addProductModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة منتج جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addProductForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">المعلومات الأساسية</h6>

                                <div class="mb-3">
                                    <label for="productCode" class="form-label">كود المنتج *</label>
                                    <input type="text" class="form-control" id="productCode" required>
                                </div>

                                <div class="mb-3">
                                    <label for="productName" class="form-label">اسم المنتج *</label>
                                    <input type="text" class="form-control" id="productName" required>
                                </div>

                                <div class="mb-3">
                                    <label for="productCategory" class="form-label">الفئة *</label>
                                    <select class="form-select" id="productCategory" required>
                                        <option value="">اختر الفئة</option>
                                        <option value="bread">خبز</option>
                                        <option value="pastry">معجنات</option>
                                        <option value="cake">كعك وحلويات</option>
                                        <option value="biscuit">بسكويت</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="productUnit" class="form-label">وحدة البيع *</label>
                                    <select class="form-select" id="productUnit" required>
                                        <option value="">اختر الوحدة</option>
                                        <option value="piece">قطعة</option>
                                        <option value="kg">كيلوجرام</option>
                                        <option value="g">جرام</option>
                                        <option value="box">صندوق</option>
                                        <option value="dozen">دستة</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="productDescription" class="form-label">الوصف</label>
                                    <textarea class="form-control" id="productDescription" rows="3"></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">الأسعار والتكاليف</h6>

                                <div class="mb-3">
                                    <label for="sellingPriceProduct" class="form-label">سعر البيع *</label>
                                    <input type="number" class="form-control" id="sellingPriceProduct" step="0.01" required>
                                </div>

                                <div class="mb-3">
                                    <label for="productionCost" class="form-label">تكلفة الإنتاج</label>
                                    <input type="number" class="form-control" id="productionCost" step="0.01" readonly>
                                    <div class="form-text">سيتم حسابها تلقائياً من الوصفة</div>
                                </div>

                                <div class="mb-3">
                                    <label for="laborCostProduct" class="form-label">تكلفة العمالة</label>
                                    <input type="number" class="form-control" id="laborCostProduct" step="0.01" value="0">
                                </div>

                                <div class="mb-3">
                                    <label for="overheadCostProduct" class="form-label">التكاليف الإضافية</label>
                                    <input type="number" class="form-control" id="overheadCostProduct" step="0.01" value="0">
                                </div>

                                <div class="mb-3">
                                    <label for="shelfLife" class="form-label">مدة الصلاحية (بالأيام)</label>
                                    <input type="number" class="form-control" id="shelfLife" min="1">
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isActiveProduct" checked>
                                    <label class="form-check-label" for="isActiveProduct">
                                        منتج نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveProduct()">حفظ المنتج</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة وصفة -->
    <div class="modal fade" id="recipeModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إدارة الوصفة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="recipeForm">
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="recipeProduct" class="form-label">المنتج *</label>
                                <select class="form-select" id="recipeProduct" required>
                                    <option value="">اختر المنتج</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="recipeYield" class="form-label">الكمية المنتجة *</label>
                                <input type="number" class="form-control" id="recipeYield" step="0.001" required>
                            </div>
                            <div class="col-md-3">
                                <label for="recipeYieldUnit" class="form-label">وحدة الإنتاج</label>
                                <select class="form-select" id="recipeYieldUnit">
                                    <option value="piece">قطعة</option>
                                    <option value="kg">كيلوجرام</option>
                                    <option value="dozen">دستة</option>
                                </select>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">مكونات الوصفة</h6>

                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="ingredientSelect" class="form-label">المكون</label>
                                <select class="form-select" id="ingredientSelect">
                                    <option value="">اختر المكون</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="ingredientQuantity" class="form-label">الكمية</label>
                                <input type="number" class="form-control" id="ingredientQuantity" step="0.001">
                            </div>
                            <div class="col-md-3">
                                <label for="ingredientUnit" class="form-label">الوحدة</label>
                                <select class="form-select" id="ingredientUnit">
                                    <option value="kg">كيلوجرام</option>
                                    <option value="g">جرام</option>
                                    <option value="l">لتر</option>
                                    <option value="ml">مليلتر</option>
                                    <option value="piece">قطعة</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success" onclick="addIngredient()">
                                        <i class="bi bi-plus-lg"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="ingredientsTable">
                                <thead>
                                    <tr>
                                        <th>المكون</th>
                                        <th>الكمية</th>
                                        <th>الوحدة</th>
                                        <th>سعر الوحدة</th>
                                        <th>التكلفة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="ingredientsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                                <tfoot>
                                    <tr class="table-primary">
                                        <td colspan="4"><strong>إجمالي تكلفة المواد:</strong></td>
                                        <td><strong id="totalMaterialCost">0 ر.ي</strong></td>
                                        <td></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>

                        <div class="row mt-4">
                            <div class="col-md-12">
                                <label for="recipeInstructions" class="form-label">تعليمات التحضير</label>
                                <textarea class="form-control" id="recipeInstructions" rows="4" placeholder="اكتب خطوات تحضير المنتج..."></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveRecipe()">حفظ الوصفة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تسجيل الإنتاج -->
    <div class="modal fade" id="productionModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل إنتاج</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="productionForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productionProductSelect" class="form-label">المنتج *</label>
                                    <select class="form-select" id="productionProductSelect" required onchange="updateProductionCost()">
                                        <option value="">اختر المنتج</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="productionQuantity" class="form-label">الكمية المنتجة *</label>
                                    <input type="number" class="form-control" id="productionQuantity" step="0.001" required onchange="updateProductionCost()">
                                </div>

                                <div class="mb-3">
                                    <label for="productionDateInput" class="form-label">تاريخ الإنتاج *</label>
                                    <input type="date" class="form-control" id="productionDateInput" required>
                                </div>

                                <div class="mb-3">
                                    <label for="productionShiftSelect" class="form-label">الوردية *</label>
                                    <select class="form-select" id="productionShiftSelect" required>
                                        <option value="morning">الصباحية</option>
                                        <option value="evening">المسائية</option>
                                        <option value="night">الليلية</option>
                                    </select>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="productionCostCalculated" class="form-label">تكلفة الإنتاج</label>
                                    <input type="text" class="form-control" id="productionCostCalculated" readonly>
                                </div>

                                <div class="mb-3">
                                    <label for="productionSupervisor" class="form-label">المشرف</label>
                                    <input type="text" class="form-control" id="productionSupervisor">
                                </div>

                                <div class="mb-3">
                                    <label for="productionNotes" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="productionNotes" rows="3"></textarea>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoUpdateInventory" checked>
                                    <label class="form-check-label" for="autoUpdateInventory">
                                        تحديث المخزون تلقائياً
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-success" onclick="saveProduction()">تسجيل الإنتاج</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة تسجيل التالف -->
    <div class="modal fade" id="damageModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تسجيل منتج تالف</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> سيتم إنشاء قيد محاسبي تلقائي لتسجيل خسارة التالف.
                    </div>

                    <form id="damageForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="damageProduct" class="form-label">المنتج *</label>
                                    <select class="form-select" id="damageProduct" required onchange="updateDamageValue()">
                                        <option value="">اختر المنتج</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="damageQuantity" class="form-label">الكمية التالفة *</label>
                                    <input type="number" class="form-control" id="damageQuantity" step="0.001" required onchange="updateDamageValue()">
                                </div>

                                <div class="mb-3">
                                    <label for="damageReasonSelect" class="form-label">سبب التلف *</label>
                                    <select class="form-select" id="damageReasonSelect" required>
                                        <option value="">اختر السبب</option>
                                        <option value="expired">انتهاء صلاحية</option>
                                        <option value="broken">كسر</option>
                                        <option value="burnt">احتراق</option>
                                        <option value="contaminated">تلوث</option>
                                        <option value="other">أخرى</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="damageDateInput" class="form-label">تاريخ التلف *</label>
                                    <input type="date" class="form-control" id="damageDateInput" required>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="damageValue" class="form-label">قيمة التالف</label>
                                    <input type="text" class="form-control" id="damageValue" readonly>
                                </div>

                                <div class="mb-3">
                                    <label for="damageResponsible" class="form-label">المسؤول</label>
                                    <input type="text" class="form-control" id="damageResponsible">
                                </div>

                                <div class="mb-3">
                                    <label for="damageNotesInput" class="form-label">ملاحظات</label>
                                    <textarea class="form-control" id="damageNotesInput" rows="3"></textarea>
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="createJournalEntry" checked>
                                    <label class="form-check-label" for="createJournalEntry">
                                        إنشاء قيد محاسبي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="saveDamage()">تسجيل التالف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة محول الوحدات -->
    <div class="modal fade" id="unitConverterModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">محول الوحدات المتقدم</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="text-primary">تحويل سريع</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="quickFromValue" class="form-label">من</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="quickFromValue" step="0.001">
                                    <select class="form-select" id="quickFromUnit">
                                        <option value="kg">كيلو</option>
                                        <option value="g">جرام</option>
                                        <option value="l">لتر</option>
                                        <option value="ml">مليلتر</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="quickToValue" class="form-label">إلى</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="quickToValue" readonly>
                                    <select class="form-select" id="quickToUnit">
                                        <option value="kg">كيلو</option>
                                        <option value="g">جرام</option>
                                        <option value="l">لتر</option>
                                        <option value="ml">مليلتر</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <button type="button" class="btn btn-primary" onclick="quickConvert()">تحويل</button>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <h6 class="text-primary">جدول التحويلات الشائعة</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>من</th>
                                        <th>إلى</th>
                                        <th>المعادل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr><td>1 كيلو</td><td>جرام</td><td>1000 جرام</td></tr>
                                    <tr><td>1 لتر</td><td>مليلتر</td><td>1000 مليلتر</td></tr>
                                    <tr><td>1 كوب</td><td>مليلتر</td><td>250 مليلتر</td></tr>
                                    <tr><td>1 ملعقة كبيرة</td><td>مليلتر</td><td>15 مليلتر</td></tr>
                                    <tr><td>1 ملعقة صغيرة</td><td>مليلتر</td><td>5 مليلتر</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Products JS Files -->
    <script src="products-functions.js"></script>
    <script src="products-recipes.js"></script>
    <script src="print-system.js"></script>
    <script src="system-init.js"></script>

    <script>
        // بيانات المنتجات التجريبية
        let products = [
            {
                id: 1,
                code: 'PROD001',
                name: 'خبز أبيض',
                category: 'bread',
                unit: 'piece',
                description: 'خبز أبيض طازج يومياً',
                sellingPrice: 5,
                productionCost: 2.5,
                laborCost: 0.5,
                overheadCost: 0.3,
                shelfLife: 1,
                isActive: true
            },
            {
                id: 2,
                code: 'PROD002',
                name: 'خبز أسمر',
                category: 'bread',
                unit: 'piece',
                description: 'خبز أسمر صحي',
                sellingPrice: 6,
                productionCost: 3,
                laborCost: 0.5,
                overheadCost: 0.3,
                shelfLife: 2,
                isActive: true
            },
            {
                id: 3,
                code: 'PROD003',
                name: 'كرواسان',
                category: 'pastry',
                unit: 'piece',
                description: 'كرواسان بالزبدة',
                sellingPrice: 15,
                productionCost: 8,
                laborCost: 2,
                overheadCost: 1,
                shelfLife: 2,
                isActive: true
            },
            {
                id: 4,
                code: 'PROD004',
                name: 'دونات محشي',
                category: 'pastry',
                unit: 'piece',
                description: 'دونات محشي بالكريمة',
                sellingPrice: 20,
                productionCost: 12,
                laborCost: 3,
                overheadCost: 1.5,
                shelfLife: 1,
                isActive: true
            },
            {
                id: 5,
                code: 'PROD005',
                name: 'كعكة شوكولاتة',
                category: 'cake',
                unit: 'piece',
                description: 'كعكة شوكولاتة فاخرة',
                sellingPrice: 150,
                productionCost: 80,
                laborCost: 20,
                overheadCost: 10,
                shelfLife: 3,
                isActive: true
            },
            {
                id: 6,
                code: 'PROD006',
                name: 'بسكويت سادة',
                category: 'biscuit',
                unit: 'kg',
                description: 'بسكويت سادة مقرمش',
                sellingPrice: 45,
                productionCost: 25,
                laborCost: 5,
                overheadCost: 3,
                shelfLife: 30,
                isActive: true
            }
        ];

        // بيانات الوصفات التجريبية
        let recipes = [
            {
                id: 1,
                productId: 1,
                productName: 'خبز أبيض',
                yield: 10,
                yieldUnit: 'piece',
                ingredients: [
                    { itemId: 1, itemName: 'دقيق أبيض', quantity: 1, unit: 'kg', unitPrice: 800, cost: 800 },
                    { itemId: 3, itemName: 'خميرة فورية', quantity: 10, unit: 'g', unitPrice: 5, cost: 50 },
                    { itemId: 5, itemName: 'ملح طعام', quantity: 20, unit: 'g', unitPrice: 0.3, cost: 6 },
                    { itemId: 6, itemName: 'زيت نباتي', quantity: 50, unit: 'ml', unitPrice: 2, cost: 100 }
                ],
                instructions: 'اخلط الدقيق مع الخميرة والملح، أضف الماء والزيت تدريجياً، اعجن جيداً، اتركه ينتفخ، ثم اخبز في فرن ساخن.',
                totalMaterialCost: 956
            },
            {
                id: 2,
                productId: 3,
                productName: 'كرواسان',
                yield: 6,
                yieldUnit: 'piece',
                ingredients: [
                    { itemId: 1, itemName: 'دقيق أبيض', quantity: 500, unit: 'g', unitPrice: 0.8, cost: 400 },
                    { itemId: 9, itemName: 'زبدة طبيعية', quantity: 200, unit: 'g', unitPrice: 8, cost: 1600 },
                    { itemId: 7, itemName: 'بيض طازج', quantity: 2, unit: 'piece', unitPrice: 15, cost: 30 },
                    { itemId: 8, itemName: 'حليب طازج', quantity: 100, unit: 'ml', unitPrice: 1.5, cost: 150 }
                ],
                instructions: 'حضر عجينة الكرواسان، ادهنها بالزبدة، اطويها عدة مرات، قطعها وشكلها، اتركها تختمر، ثم اخبز.',
                totalMaterialCost: 2180
            }
        ];

        // بيانات الإنتاج التجريبية
        let production = [
            {
                id: 1,
                productId: 1,
                productName: 'خبز أبيض',
                quantity: 100,
                unit: 'piece',
                cost: 250,
                date: '2024-01-15',
                shift: 'morning',
                supervisor: 'خالد الخباز',
                notes: 'إنتاج صباحي عادي'
            },
            {
                id: 2,
                productId: 3,
                productName: 'كرواسان',
                quantity: 24,
                unit: 'piece',
                cost: 192,
                date: '2024-01-15',
                shift: 'morning',
                supervisor: 'فاطمة المعجنات',
                notes: 'طلبية خاصة'
            }
        ];

        // بيانات التالف التجريبية
        let damages = [
            {
                id: 1,
                productId: 1,
                productName: 'خبز أبيض',
                quantity: 5,
                reason: 'expired',
                value: 25,
                date: '2024-01-14',
                responsible: 'أحمد البائع',
                journalEntryId: 'JE001',
                notes: 'انتهاء صلاحية نهاية اليوم'
            },
            {
                id: 2,
                productId: 4,
                productName: 'دونات محشي',
                quantity: 3,
                reason: 'broken',
                value: 60,
                date: '2024-01-14',
                responsible: 'سارة المساعدة',
                journalEntryId: 'JE002',
                notes: 'سقوط أثناء النقل'
            }
        ];

        // معاملات تحويل الوحدات
        const unitConversions = {
            weight: {
                kg: 1000,
                g: 1,
                lb: 453.592,
                oz: 28.3495
            },
            volume: {
                l: 1000,
                ml: 1,
                cup: 250,
                tbsp: 15,
                tsp: 5
            }
        };

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // طباعة الصفحة
        function printPage() {
            window.print();
        }
    </script>
</body>
</html>