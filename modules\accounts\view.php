<?php
/**
 * عرض تفاصيل الحساب المحاسبي
 * View Account Details
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('accounts');

$page_title = 'تفاصيل الحساب المحاسبي';
$accountId = intval($_GET['id'] ?? 0);

if (!$accountId) {
    header('Location: index.php');
    exit();
}

try {
    // الحصول على بيانات الحساب
    $account = queryOne(
        "SELECT a.*, p.account_name as parent_name, u.full_name as created_by_name
         FROM chart_of_accounts a
         LEFT JOIN chart_of_accounts p ON a.parent_id = p.id
         LEFT JOIN users u ON a.created_by = u.id
         WHERE a.id = ?",
        [$accountId]
    );
    
    if (!$account) {
        showMessage('الحساب غير موجود', 'error');
        header('Location: index.php');
        exit();
    }
    
    // الحصول على الحسابات الفرعية
    $subAccounts = query(
        "SELECT * FROM chart_of_accounts WHERE parent_id = ? ORDER BY account_code",
        [$accountId]
    );
    
    // الحصول على آخر القيود المحاسبية
    $recentEntries = query(
        "SELECT je.*, jed.debit_amount, jed.credit_amount, jed.description as detail_description
         FROM journal_entries je
         JOIN journal_entry_details jed ON je.id = jed.journal_entry_id
         WHERE jed.account_id = ?
         ORDER BY je.entry_date DESC, je.id DESC
         LIMIT 10",
        [$accountId]
    );
    
    // حساب الرصيد الحالي
    $currentBalance = getAccountBalance($accountId);
    
    // إحصائيات الحساب
    $stats = queryOne(
        "SELECT 
            COUNT(*) as total_entries,
            SUM(debit_amount) as total_debit,
            SUM(credit_amount) as total_credit
         FROM journal_entry_details jed
         JOIN journal_entries je ON jed.journal_entry_id = je.id
         WHERE jed.account_id = ? AND je.is_posted = 1",
        [$accountId]
    );
    
} catch (Exception $e) {
    logError("خطأ في تحميل تفاصيل الحساب: " . $e->getMessage());
    showMessage('خطأ في تحميل البيانات', 'error');
    header('Location: index.php');
    exit();
}

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-eye text-primary me-2"></i>
        تفاصيل الحساب المحاسبي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="edit.php?id=<?php echo $accountId; ?>" class="btn btn-primary">
                <i class="bi bi-pencil me-1"></i>
                تعديل
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<!-- معلومات الحساب الأساسية -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle me-2"></i>
                    معلومات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>رمز الحساب:</strong></td>
                                <td><?php echo htmlspecialchars($account['account_code']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>اسم الحساب:</strong></td>
                                <td><?php echo htmlspecialchars($account['account_name']); ?></td>
                            </tr>
                            <?php if ($account['account_name_en']): ?>
                            <tr>
                                <td><strong>الاسم بالإنجليزية:</strong></td>
                                <td><?php echo htmlspecialchars($account['account_name_en']); ?></td>
                            </tr>
                            <?php endif; ?>
                            <tr>
                                <td><strong>نوع الحساب:</strong></td>
                                <td>
                                    <?php
                                    $typeClass = '';
                                    $typeText = '';
                                    switch ($account['account_type']) {
                                        case 'asset':
                                            $typeClass = 'success';
                                            $typeText = 'أصول';
                                            break;
                                        case 'liability':
                                            $typeClass = 'danger';
                                            $typeText = 'خصوم';
                                            break;
                                        case 'equity':
                                            $typeClass = 'secondary';
                                            $typeText = 'حقوق الملكية';
                                            break;
                                        case 'revenue':
                                            $typeClass = 'info';
                                            $typeText = 'إيرادات';
                                            break;
                                        case 'expense':
                                            $typeClass = 'warning';
                                            $typeText = 'مصروفات';
                                            break;
                                    }
                                    ?>
                                    <span class="badge bg-<?php echo $typeClass; ?>"><?php echo $typeText; ?></span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>طبيعة الحساب:</strong></td>
                                <td>
                                    <span class="badge bg-<?php echo $account['account_nature'] === 'debit' ? 'primary' : 'secondary'; ?>">
                                        <?php echo $account['account_nature'] === 'debit' ? 'مدين' : 'دائن'; ?>
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>المستوى:</strong></td>
                                <td><span class="badge bg-light text-dark"><?php echo $account['level']; ?></span></td>
                            </tr>
                            <tr>
                                <td><strong>الحساب الأب:</strong></td>
                                <td>
                                    <?php if ($account['parent_name']): ?>
                                        <?php echo htmlspecialchars($account['parent_name']); ?>
                                    <?php else: ?>
                                        <span class="text-muted">لا يوجد</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الرصيد الافتتاحي:</strong></td>
                                <td><?php echo formatMoney($account['opening_balance']); ?></td>
                            </tr>
                            <tr>
                                <td><strong>الرصيد الحالي:</strong></td>
                                <td>
                                    <strong class="<?php echo $currentBalance >= 0 ? 'text-success' : 'text-danger'; ?>">
                                        <?php echo formatMoney($currentBalance); ?>
                                    </strong>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>الحالة:</strong></td>
                                <td>
                                    <?php if ($account['is_active']): ?>
                                        <span class="badge bg-success">نشط</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">غير نشط</span>
                                    <?php endif; ?>
                                    
                                    <?php if ($account['is_main']): ?>
                                        <span class="badge bg-primary ms-1">رئيسي</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                <?php if ($account['description']): ?>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>الوصف:</h6>
                        <p class="text-muted"><?php echo nl2br(htmlspecialchars($account['description'])); ?></p>
                    </div>
                </div>
                <?php endif; ?>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <small class="text-muted">
                            أنشأ بواسطة: <?php echo htmlspecialchars($account['created_by_name']); ?> 
                            في <?php echo formatDateTime($account['created_at']); ?>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- إحصائيات الحساب -->
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up me-2"></i>
                    إحصائيات الحساب
                </h5>
            </div>
            <div class="card-body">
                <div class="stats-card mb-3" style="border-right-color: #17a2b8;">
                    <div class="icon text-info">
                        <i class="bi bi-list-ol"></i>
                    </div>
                    <div class="number text-info"><?php echo number_format($stats['total_entries'] ?? 0); ?></div>
                    <div class="label">إجمالي القيود</div>
                </div>
                
                <div class="stats-card mb-3" style="border-right-color: #28a745;">
                    <div class="icon text-success">
                        <i class="bi bi-arrow-up-circle"></i>
                    </div>
                    <div class="number text-success"><?php echo formatMoney($stats['total_debit'] ?? 0); ?></div>
                    <div class="label">إجمالي المدين</div>
                </div>
                
                <div class="stats-card" style="border-right-color: #dc3545;">
                    <div class="icon text-danger">
                        <i class="bi bi-arrow-down-circle"></i>
                    </div>
                    <div class="number text-danger"><?php echo formatMoney($stats['total_credit'] ?? 0); ?></div>
                    <div class="label">إجمالي الدائن</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الحسابات الفرعية -->
<?php if (!empty($subAccounts)): ?>
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-diagram-2 me-2"></i>
                    الحسابات الفرعية (<?php echo count($subAccounts); ?>)
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رمز الحساب</th>
                                <th>اسم الحساب</th>
                                <th>النوع</th>
                                <th>الرصيد الحالي</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subAccounts as $subAccount): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($subAccount['account_code']); ?></td>
                                    <td><?php echo htmlspecialchars($subAccount['account_name']); ?></td>
                                    <td>
                                        <?php
                                        $subTypeClass = '';
                                        $subTypeText = '';
                                        switch ($subAccount['account_type']) {
                                            case 'asset': $subTypeClass = 'success'; $subTypeText = 'أصول'; break;
                                            case 'liability': $subTypeClass = 'danger'; $subTypeText = 'خصوم'; break;
                                            case 'equity': $subTypeClass = 'secondary'; $subTypeText = 'حقوق الملكية'; break;
                                            case 'revenue': $subTypeClass = 'info'; $subTypeText = 'إيرادات'; break;
                                            case 'expense': $subTypeClass = 'warning'; $subTypeText = 'مصروفات'; break;
                                        }
                                        ?>
                                        <span class="badge bg-<?php echo $subTypeClass; ?>"><?php echo $subTypeText; ?></span>
                                    </td>
                                    <td>
                                        <strong class="<?php echo $subAccount['current_balance'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo formatMoney($subAccount['current_balance']); ?>
                                        </strong>
                                    </td>
                                    <td>
                                        <?php if ($subAccount['is_active']): ?>
                                            <span class="badge bg-success">نشط</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">غير نشط</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="view.php?id=<?php echo $subAccount['id']; ?>" 
                                               class="btn btn-sm btn-outline-info" title="عرض">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?php echo $subAccount['id']; ?>" 
                                               class="btn btn-sm btn-outline-primary" title="تعديل">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- آخر القيود المحاسبية -->
<?php if (!empty($recentEntries)): ?>
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-journal-text me-2"></i>
                    آخر القيود المحاسبية
                </h5>
                <a href="../reports/account_statement.php?account_id=<?php echo $accountId; ?>" class="btn btn-sm btn-outline-primary">
                    كشف حساب مفصل
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>رقم القيد</th>
                                <th>التاريخ</th>
                                <th>البيان</th>
                                <th>مدين</th>
                                <th>دائن</th>
                                <th>الرصيد</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $runningBalance = 0;
                            foreach ($recentEntries as $entry): 
                                if ($account['account_nature'] === 'debit') {
                                    $runningBalance += $entry['debit_amount'] - $entry['credit_amount'];
                                } else {
                                    $runningBalance += $entry['credit_amount'] - $entry['debit_amount'];
                                }
                            ?>
                                <tr>
                                    <td>
                                        <a href="../reports/journal_entry.php?id=<?php echo $entry['id']; ?>" 
                                           class="text-decoration-none">
                                            <?php echo htmlspecialchars($entry['entry_number']); ?>
                                        </a>
                                    </td>
                                    <td><?php echo formatDate($entry['entry_date']); ?></td>
                                    <td>
                                        <div>
                                            <strong><?php echo htmlspecialchars($entry['description']); ?></strong>
                                            <?php if ($entry['detail_description']): ?>
                                                <br><small class="text-muted"><?php echo htmlspecialchars($entry['detail_description']); ?></small>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if ($entry['debit_amount'] > 0): ?>
                                            <span class="text-success"><?php echo formatMoney($entry['debit_amount']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if ($entry['credit_amount'] > 0): ?>
                                            <span class="text-danger"><?php echo formatMoney($entry['credit_amount']); ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <strong class="<?php echo $runningBalance >= 0 ? 'text-success' : 'text-danger'; ?>">
                                            <?php echo formatMoney($runningBalance); ?>
                                        </strong>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<?php include __DIR__ . '/../../includes/footer.php'; ?>
