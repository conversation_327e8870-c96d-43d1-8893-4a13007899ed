<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام محاسبة المخبز - AnwarSoft</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .welcome-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .welcome-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 800px;
            width: 100%;
            text-align: center;
        }
        
        .welcome-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 2rem;
        }
        
        .welcome-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
        }
        
        .welcome-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
        }
        
        .welcome-body {
            padding: 3rem 2rem;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 2rem;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            border-color: #667eea;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        
        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .btn-start {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 15px;
            padding: 15px 40px;
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
            margin-top: 2rem;
        }
        
        .btn-start:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }
        
        .system-info {
            background: #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-top: 2rem;
        }
        
        .system-info h5 {
            color: #495057;
            margin-bottom: 1rem;
        }
        
        .system-info ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .system-info li {
            padding: 0.5rem 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .system-info li:last-child {
            border-bottom: none;
        }
        
        .system-info i {
            color: #28a745;
            margin-left: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="welcome-container">
        <div class="welcome-card">
            <div class="welcome-header">
                <i class="bi bi-shop" style="font-size: 4rem; margin-bottom: 1rem;"></i>
                <h1>نظام محاسبة المخبز</h1>
                <p>نظام محاسبي شامل ومتكامل لإدارة المخابز</p>
            </div>
            
            <div class="welcome-body">
                <h3 class="mb-4">مرحباً بك في نظام AnwarSoft للمحاسبة</h3>
                <p class="lead text-muted mb-4">
                    نظام محاسبي احترافي مصمم خصيصاً للمخابز يدعم اللغة العربية بالكامل
                    ويوفر جميع الأدوات اللازمة لإدارة العمليات المالية والمحاسبية
                </p>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="bi bi-calculator feature-icon"></i>
                        <div class="feature-title">المحاسبة الذكية</div>
                        <div class="feature-description">
                            شجرة حسابات شاملة مع قيود محاسبية تلقائية لجميع العمليات
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <i class="bi bi-receipt feature-icon"></i>
                        <div class="feature-title">إدارة الفواتير</div>
                        <div class="feature-description">
                            نظام فواتير متكامل للمبيعات والمشتريات مع دعم الباركود
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <i class="bi bi-box-seam feature-icon"></i>
                        <div class="feature-title">إدارة المخزون</div>
                        <div class="feature-description">
                            تتبع دقيق للمخزون مع وصفات الإنتاج وحساب التكاليف
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <i class="bi bi-people feature-icon"></i>
                        <div class="feature-title">إدارة الموظفين</div>
                        <div class="feature-description">
                            نظام رواتب شامل مع تتبع الحضور والغياب والسلف
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <i class="bi bi-graph-up feature-icon"></i>
                        <div class="feature-title">التقارير المالية</div>
                        <div class="feature-description">
                            تقارير مالية شاملة وميزانيات وقوائم أرباح وخسائر
                        </div>
                    </div>
                    
                    <div class="feature-card">
                        <i class="bi bi-shield-check feature-icon"></i>
                        <div class="feature-title">الأمان والصلاحيات</div>
                        <div class="feature-description">
                            نظام صلاحيات متقدم مع تتبع جميع العمليات والتغييرات
                        </div>
                    </div>
                </div>
                
                <a href="auth/login.php" class="btn-start">
                    <i class="bi bi-box-arrow-in-right me-2"></i>
                    دخول النظام
                </a>
                
                <div class="system-info">
                    <h5><i class="bi bi-info-circle me-2"></i>معلومات النظام</h5>
                    <ul>
                        <li><i class="bi bi-check-circle"></i> يعمل أوفلاين وأونلاين</li>
                        <li><i class="bi bi-check-circle"></i> يدعم الطباعة العادية والحرارية</li>
                        <li><i class="bi bi-check-circle"></i> واجهة عربية RTL بالكامل</li>
                        <li><i class="bi bi-check-circle"></i> متوافق مع الأجهزة المحمولة</li>
                        <li><i class="bi bi-check-circle"></i> نسخ احتياطي تلقائي</li>
                        <li><i class="bi bi-check-circle"></i> معايير محاسبية دقيقة</li>
                    </ul>
                </div>
                
                <div class="mt-4">
                    <small class="text-muted">
                        نظام محاسبة المخبز v1.0.0 | تطوير: AnwarSoft | 
                        <span id="current-date"></span>
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // عرض التاريخ الحالي
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('ar-SA');
        
        // تأثيرات بصرية
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
