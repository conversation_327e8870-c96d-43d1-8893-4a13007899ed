<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>الموظفين والرواتب</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .employee-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 1.1rem;
        }

        .salary-amount {
            font-weight: bold;
            color: #28a745;
        }

        .advance-amount {
            font-weight: bold;
            color: #dc3545;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            font-weight: 600;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 2rem;
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html" class="active">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="products.html">
                    <i class="bi bi-basket"></i>
                    <span class="menu-text">المنتجات والوصفات</span>
                </a>
            </li>

            <li>
                <a href="journal-entries.html">
                    <i class="bi bi-journal-text"></i>
                    <span class="menu-text">القيود المحاسبية</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-person-badge text-primary me-2"></i>
                    الموظفين والرواتب
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="showAddEmployeeModal()">
                        <i class="bi bi-plus-lg me-1"></i>إضافة موظف
                    </button>
                    <button type="button" class="btn btn-success" onclick="showPayrollModal()">
                        <i class="bi bi-calculator me-1"></i>حساب الرواتب
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showAdvanceModal()">
                        <i class="bi bi-cash me-1"></i>سلفة
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="refreshData()">
                        <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                    </button>
                </div>
            </div>

            <!-- إحصائيات الموظفين -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #28a745;">
                        <div class="icon text-success">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="number text-success" id="totalEmployees">12</div>
                        <div class="label">إجمالي الموظفين</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #17a2b8;">
                        <div class="icon text-info">
                            <i class="bi bi-person-check"></i>
                        </div>
                        <div class="number text-info" id="activeEmployees">10</div>
                        <div class="label">الموظفين النشطين</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #ffc107;">
                        <div class="icon text-warning">
                            <i class="bi bi-currency-exchange"></i>
                        </div>
                        <div class="number text-warning" id="totalSalaries">485,000</div>
                        <div class="label">إجمالي الرواتب (ر.ي)</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #dc3545;">
                        <div class="icon text-danger">
                            <i class="bi bi-cash-stack"></i>
                        </div>
                        <div class="number text-danger" id="totalAdvances">45,000</div>
                        <div class="label">إجمالي السلف (ر.ي)</div>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="card">
                <div class="card-header p-0">
                    <ul class="nav nav-tabs" id="employeeTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="employees-tab" data-bs-toggle="tab" data-bs-target="#employees" type="button" role="tab">
                                <i class="bi bi-people me-2"></i>الموظفين
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="payroll-tab" data-bs-toggle="tab" data-bs-target="#payroll" type="button" role="tab">
                                <i class="bi bi-calculator me-2"></i>كشف الرواتب
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="advances-tab" data-bs-toggle="tab" data-bs-target="#advances" type="button" role="tab">
                                <i class="bi bi-cash me-2"></i>السلف
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="attendance-tab" data-bs-toggle="tab" data-bs-target="#attendance" type="button" role="tab">
                                <i class="bi bi-clock me-2"></i>الحضور والانصراف
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="employeeTabContent">
                    <!-- تبويب الموظفين -->
                    <div class="tab-pane fade show active" id="employees" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="searchInput" class="form-label">البحث</label>
                                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الموظفين...">
                            </div>
                            <div class="col-md-3">
                                <label for="departmentFilter" class="form-label">القسم</label>
                                <select class="form-select" id="departmentFilter">
                                    <option value="">جميع الأقسام</option>
                                    <option value="production">الإنتاج</option>
                                    <option value="sales">المبيعات</option>
                                    <option value="admin">الإدارة</option>
                                    <option value="accounting">المحاسبة</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="statusFilter" class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                        <i class="bi bi-x-lg me-1"></i>مسح
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- جدول الموظفين -->
                        <div class="table-responsive">
                            <table class="table table-hover" id="employeesTable">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>الاسم</th>
                                        <th>الوظيفة</th>
                                        <th>القسم</th>
                                        <th>الراتب الأساسي</th>
                                        <th>تاريخ التوظيف</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="employeesTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب كشف الرواتب -->
                    <div class="tab-pane fade" id="payroll" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="payrollMonth" class="form-label">الشهر</label>
                                <select class="form-select" id="payrollMonth">
                                    <option value="1">يناير</option>
                                    <option value="2">فبراير</option>
                                    <option value="3">مارس</option>
                                    <option value="4">أبريل</option>
                                    <option value="5">مايو</option>
                                    <option value="6">يونيو</option>
                                    <option value="7">يوليو</option>
                                    <option value="8">أغسطس</option>
                                    <option value="9">سبتمبر</option>
                                    <option value="10">أكتوبر</option>
                                    <option value="11">نوفمبر</option>
                                    <option value="12">ديسمبر</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="payrollYear" class="form-label">السنة</label>
                                <select class="form-select" id="payrollYear">
                                    <option value="2024">2024</option>
                                    <option value="2023">2023</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="generatePayroll()">
                                        <i class="bi bi-calculator me-1"></i>إنشاء كشف الراتب
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-success" onclick="printPayroll()">
                                        <i class="bi bi-printer me-1"></i>طباعة
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="payrollTable">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>الراتب الأساسي</th>
                                        <th>البدلات</th>
                                        <th>الخصومات</th>
                                        <th>السلف</th>
                                        <th>صافي الراتب</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="payrollTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب السلف -->
                    <div class="tab-pane fade" id="advances" role="tabpanel">
                        <div class="table-responsive">
                            <table class="table table-hover" id="advancesTable">
                                <thead>
                                    <tr>
                                        <th>التاريخ</th>
                                        <th>الموظف</th>
                                        <th>المبلغ</th>
                                        <th>السبب</th>
                                        <th>المتبقي</th>
                                        <th>الحالة</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="advancesTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- تبويب الحضور والانصراف -->
                    <div class="tab-pane fade" id="attendance" role="tabpanel">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="attendanceDate" class="form-label">التاريخ</label>
                                <input type="date" class="form-control" id="attendanceDate">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="loadAttendance()">
                                        <i class="bi bi-search me-1"></i>عرض الحضور
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover" id="attendanceTable">
                                <thead>
                                    <tr>
                                        <th>الموظف</th>
                                        <th>وقت الحضور</th>
                                        <th>وقت الانصراف</th>
                                        <th>ساعات العمل</th>
                                        <th>الحالة</th>
                                        <th>ملاحظات</th>
                                    </tr>
                                </thead>
                                <tbody id="attendanceTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة إضافة موظف جديد -->
    <div class="modal fade" id="addEmployeeModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة موظف جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEmployeeForm">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">المعلومات الشخصية</h6>

                                <div class="mb-3">
                                    <label for="employeeName" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="employeeName" required>
                                </div>

                                <div class="mb-3">
                                    <label for="employeeId" class="form-label">رقم الموظف *</label>
                                    <input type="text" class="form-control" id="employeeId" required>
                                </div>

                                <div class="mb-3">
                                    <label for="nationalId" class="form-label">رقم الهوية</label>
                                    <input type="text" class="form-control" id="nationalId">
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email">
                                </div>

                                <div class="mb-3">
                                    <label for="address" class="form-label">العنوان</label>
                                    <textarea class="form-control" id="address" rows="2"></textarea>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <h6 class="text-primary mb-3">معلومات الوظيفة</h6>

                                <div class="mb-3">
                                    <label for="jobTitle" class="form-label">المسمى الوظيفي *</label>
                                    <input type="text" class="form-control" id="jobTitle" required>
                                </div>

                                <div class="mb-3">
                                    <label for="department" class="form-label">القسم *</label>
                                    <select class="form-select" id="department" required>
                                        <option value="">اختر القسم</option>
                                        <option value="production">الإنتاج</option>
                                        <option value="sales">المبيعات</option>
                                        <option value="admin">الإدارة</option>
                                        <option value="accounting">المحاسبة</option>
                                    </select>
                                </div>

                                <div class="mb-3">
                                    <label for="hireDate" class="form-label">تاريخ التوظيف *</label>
                                    <input type="date" class="form-control" id="hireDate" required>
                                </div>

                                <div class="mb-3">
                                    <label for="basicSalary" class="form-label">الراتب الأساسي *</label>
                                    <input type="number" class="form-control" id="basicSalary" step="0.01" required>
                                </div>

                                <div class="mb-3">
                                    <label for="allowances" class="form-label">البدلات</label>
                                    <input type="number" class="form-control" id="allowances" step="0.01" value="0">
                                </div>

                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isActive" checked>
                                    <label class="form-check-label" for="isActive">
                                        موظف نشط
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveEmployee()">حفظ الموظف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة السلفة -->
    <div class="modal fade" id="advanceModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة سلفة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="advanceForm">
                        <div class="mb-3">
                            <label for="advanceEmployee" class="form-label">الموظف *</label>
                            <select class="form-select" id="advanceEmployee" required>
                                <option value="">اختر الموظف</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label for="advanceAmount" class="form-label">مبلغ السلفة *</label>
                            <input type="number" class="form-control" id="advanceAmount" step="0.01" required>
                        </div>

                        <div class="mb-3">
                            <label for="advanceDate" class="form-label">التاريخ *</label>
                            <input type="date" class="form-control" id="advanceDate" required>
                        </div>

                        <div class="mb-3">
                            <label for="advanceReason" class="form-label">السبب</label>
                            <textarea class="form-control" id="advanceReason" rows="3" placeholder="سبب طلب السلفة..."></textarea>
                        </div>

                        <div class="mb-3">
                            <label for="installments" class="form-label">عدد الأقساط</label>
                            <input type="number" class="form-control" id="installments" min="1" value="1">
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="saveAdvance()">إضافة السلفة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة حساب الرواتب -->
    <div class="modal fade" id="payrollModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">حساب الرواتب الشهرية</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <label for="payrollModalMonth" class="form-label">الشهر</label>
                            <select class="form-select" id="payrollModalMonth">
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="payrollModalYear" class="form-label">السنة</label>
                            <select class="form-select" id="payrollModalYear">
                                <option value="2024">2024</option>
                                <option value="2023">2023</option>
                            </select>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle me-2"></i>
                        سيتم حساب الرواتب لجميع الموظفين النشطين مع خصم السلف المستحقة.
                    </div>

                    <div id="payrollSummary" class="d-none">
                        <h6>ملخص الرواتب:</h6>
                        <ul class="list-group">
                            <li class="list-group-item d-flex justify-content-between">
                                <span>إجمالي الرواتب الأساسية:</span>
                                <strong id="totalBasicSalaries">0 ر.ي</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>إجمالي البدلات:</span>
                                <strong id="totalAllowances">0 ر.ي</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between">
                                <span>إجمالي الخصومات:</span>
                                <strong id="totalDeductions" class="text-danger">0 ر.ي</strong>
                            </li>
                            <li class="list-group-item d-flex justify-content-between bg-light">
                                <span><strong>صافي الرواتب:</strong></span>
                                <strong id="netSalaries" class="text-success">0 ر.ي</strong>
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="calculatePayroll()">حساب الرواتب</button>
                    <button type="button" class="btn btn-success d-none" id="confirmPayrollBtn" onclick="confirmPayroll()">تأكيد الصرف</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // بيانات الموظفين التجريبية
        let employees = [
            {
                id: 1,
                employeeId: 'EMP001',
                name: 'أحمد محمد الصالح',
                nationalId: '123456789',
                phone: '777123456',
                email: '<EMAIL>',
                address: 'شارع الزبيري، صنعاء',
                jobTitle: 'مدير الإنتاج',
                department: 'production',
                hireDate: '2020-01-15',
                basicSalary: 60000,
                allowances: 10000,
                isActive: true,
                avatar: 'أ'
            },
            {
                id: 2,
                employeeId: 'EMP002',
                name: 'فاطمة علي الحميري',
                nationalId: '*********',
                phone: '*********',
                email: '<EMAIL>',
                address: 'حي الحصبة، صنعاء',
                jobTitle: 'محاسبة',
                department: 'accounting',
                hireDate: '2020-03-01',
                basicSalary: 45000,
                allowances: 5000,
                isActive: true,
                avatar: 'ف'
            },
            {
                id: 3,
                employeeId: 'EMP003',
                name: 'محمد سالم الأهدل',
                nationalId: '*********',
                phone: '*********',
                email: '<EMAIL>',
                address: 'شارع الستين، صنعاء',
                jobTitle: 'مشرف مبيعات',
                department: 'sales',
                hireDate: '2021-06-15',
                basicSalary: 40000,
                allowances: 8000,
                isActive: true,
                avatar: 'م'
            },
            {
                id: 4,
                employeeId: 'EMP004',
                name: 'سارة أحمد الشامي',
                nationalId: '*********',
                phone: '*********',
                email: '<EMAIL>',
                address: 'شارع الثورة، صنعاء',
                jobTitle: 'موظفة استقبال',
                department: 'admin',
                hireDate: '2022-01-10',
                basicSalary: 30000,
                allowances: 3000,
                isActive: true,
                avatar: 'س'
            },
            {
                id: 5,
                employeeId: 'EMP005',
                name: 'خالد عبدالله المقطري',
                nationalId: '321654987',
                phone: '777567890',
                email: '<EMAIL>',
                address: 'حي السبعين، صنعاء',
                jobTitle: 'خباز رئيسي',
                department: 'production',
                hireDate: '2019-08-20',
                basicSalary: 35000,
                allowances: 7000,
                isActive: true,
                avatar: 'خ'
            },
            {
                id: 6,
                employeeId: 'EMP006',
                name: 'نادية محمد الزبيري',
                nationalId: '*********',
                phone: '*********',
                email: '<EMAIL>',
                address: 'شارع الجمهورية، صنعاء',
                jobTitle: 'مساعدة محاسبة',
                department: 'accounting',
                hireDate: '2023-02-01',
                basicSalary: 25000,
                allowances: 2000,
                isActive: false,
                avatar: 'ن'
            }
        ];

        // بيانات السلف التجريبية
        let advances = [
            {
                id: 1,
                employeeId: 1,
                employeeName: 'أحمد محمد الصالح',
                amount: 15000,
                date: '2024-01-10',
                reason: 'ظروف طارئة',
                installments: 3,
                remaining: 10000,
                status: 'active'
            },
            {
                id: 2,
                employeeId: 3,
                employeeName: 'محمد سالم الأهدل',
                amount: 20000,
                date: '2024-01-05',
                reason: 'مصاريف طبية',
                installments: 4,
                remaining: 15000,
                status: 'active'
            },
            {
                id: 3,
                employeeId: 2,
                employeeName: 'فاطمة علي الحميري',
                amount: 10000,
                date: '2023-12-20',
                reason: 'مصاريف تعليم',
                installments: 2,
                remaining: 0,
                status: 'completed'
            }
        ];

        // بيانات الحضور التجريبية
        let attendance = [
            {
                employeeId: 1,
                employeeName: 'أحمد محمد الصالح',
                date: '2024-01-15',
                checkIn: '08:00',
                checkOut: '17:00',
                workHours: 9,
                status: 'present',
                notes: ''
            },
            {
                employeeId: 2,
                employeeName: 'فاطمة علي الحميري',
                date: '2024-01-15',
                checkIn: '08:15',
                checkOut: '17:15',
                workHours: 9,
                status: 'present',
                notes: 'تأخير 15 دقيقة'
            },
            {
                employeeId: 3,
                employeeName: 'محمد سالم الأهدل',
                date: '2024-01-15',
                checkIn: '08:30',
                checkOut: '18:00',
                workHours: 9.5,
                status: 'present',
                notes: 'ساعات إضافية'
            }
        ];

        // بيانات كشف الرواتب
        let payrollData = [];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // عرض الموظفين
        function displayEmployees(dataToShow = employees) {
            const tbody = document.getElementById('employeesTableBody');
            tbody.innerHTML = '';

            dataToShow.forEach(employee => {
                const row = document.createElement('tr');

                const departmentText = {
                    'production': 'الإنتاج',
                    'sales': 'المبيعات',
                    'admin': 'الإدارة',
                    'accounting': 'المحاسبة'
                }[employee.department];

                const departmentClass = {
                    'production': 'success',
                    'sales': 'primary',
                    'admin': 'warning',
                    'accounting': 'info'
                }[employee.department];

                row.innerHTML = `
                    <td>
                        <div class="employee-avatar">${employee.avatar}</div>
                    </td>
                    <td>
                        <strong>${employee.name}</strong>
                        <br><small class="text-muted">${employee.employeeId}</small>
                    </td>
                    <td>${employee.jobTitle}</td>
                    <td><span class="badge bg-${departmentClass}">${departmentText}</span></td>
                    <td><span class="salary-amount">${employee.basicSalary.toLocaleString()} ر.ي</span></td>
                    <td><small class="text-muted">${formatDate(employee.hireDate)}</small></td>
                    <td><span class="badge bg-${employee.isActive ? 'success' : 'secondary'}">${employee.isActive ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" onclick="viewEmployee(${employee.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="editEmployee(${employee.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="giveAdvance(${employee.id})" title="سلفة">
                                <i class="bi bi-cash"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-${employee.isActive ? 'warning' : 'success'}" onclick="toggleEmployeeStatus(${employee.id})" title="${employee.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                                <i class="bi bi-${employee.isActive ? 'pause' : 'play'}"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الإحصائيات
        function updateStats() {
            const stats = {
                totalEmployees: employees.length,
                activeEmployees: employees.filter(emp => emp.isActive).length,
                totalSalaries: employees.filter(emp => emp.isActive).reduce((sum, emp) => sum + emp.basicSalary + emp.allowances, 0),
                totalAdvances: advances.filter(adv => adv.status === 'active').reduce((sum, adv) => sum + adv.remaining, 0)
            };

            document.getElementById('totalEmployees').textContent = stats.totalEmployees;
            document.getElementById('activeEmployees').textContent = stats.activeEmployees;
            document.getElementById('totalSalaries').textContent = stats.totalSalaries.toLocaleString();
            document.getElementById('totalAdvances').textContent = stats.totalAdvances.toLocaleString();
        }

        // فلترة الموظفين
        function filterEmployees() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const departmentFilter = document.getElementById('departmentFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = employees.filter(employee => {
                const matchesSearch = !searchTerm ||
                    employee.name.toLowerCase().includes(searchTerm) ||
                    employee.employeeId.toLowerCase().includes(searchTerm) ||
                    employee.jobTitle.toLowerCase().includes(searchTerm);

                const matchesDepartment = !departmentFilter || employee.department === departmentFilter;
                const matchesStatus = !statusFilter ||
                    (statusFilter === 'active' && employee.isActive) ||
                    (statusFilter === 'inactive' && !employee.isActive);

                return matchesSearch && matchesDepartment && matchesStatus;
            });

            displayEmployees(filtered);
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('departmentFilter').value = '';
            document.getElementById('statusFilter').value = '';
            displayEmployees();
        }

        // إظهار نافذة إضافة موظف
        function showAddEmployeeModal() {
            document.getElementById('addEmployeeForm').reset();
            document.getElementById('isActive').checked = true;
            document.getElementById('hireDate').value = new Date().toISOString().split('T')[0];
            const modal = new bootstrap.Modal(document.getElementById('addEmployeeModal'));
            modal.show();
        }

        // حفظ موظف جديد
        function saveEmployee() {
            const newEmployee = {
                id: employees.length + 1,
                employeeId: document.getElementById('employeeId').value,
                name: document.getElementById('employeeName').value,
                nationalId: document.getElementById('nationalId').value,
                phone: document.getElementById('phone').value,
                email: document.getElementById('email').value,
                address: document.getElementById('address').value,
                jobTitle: document.getElementById('jobTitle').value,
                department: document.getElementById('department').value,
                hireDate: document.getElementById('hireDate').value,
                basicSalary: parseFloat(document.getElementById('basicSalary').value),
                allowances: parseFloat(document.getElementById('allowances').value) || 0,
                isActive: document.getElementById('isActive').checked,
                avatar: document.getElementById('employeeName').value.charAt(0)
            };

            employees.push(newEmployee);
            displayEmployees();
            updateStats();

            const modal = bootstrap.Modal.getInstance(document.getElementById('addEmployeeModal'));
            modal.hide();

            alert('تم إضافة الموظف بنجاح!');
        }

        // عرض تفاصيل الموظف
        function viewEmployee(id) {
            const employee = employees.find(emp => emp.id === id);
            if (employee) {
                alert(`تفاصيل الموظف:\nالاسم: ${employee.name}\nالوظيفة: ${employee.jobTitle}\nالراتب: ${employee.basicSalary.toLocaleString()} ر.ي\nتاريخ التوظيف: ${formatDate(employee.hireDate)}`);
            }
        }

        // تعديل الموظف
        function editEmployee(id) {
            alert('سيتم فتح نافذة التعديل قريباً');
        }

        // تبديل حالة الموظف
        function toggleEmployeeStatus(id) {
            const employee = employees.find(emp => emp.id === id);
            if (employee) {
                employee.isActive = !employee.isActive;
                displayEmployees();
                updateStats();
                alert(`تم ${employee.isActive ? 'تفعيل' : 'إلغاء تفعيل'} الموظف بنجاح!`);
            }
        }

        // إعطاء سلفة للموظف
        function giveAdvance(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (employee) {
                document.getElementById('advanceEmployee').value = employeeId;
                document.getElementById('advanceDate').value = new Date().toISOString().split('T')[0];
                showAdvanceModal();
            }
        }

        // إظهار نافذة السلفة
        function showAdvanceModal() {
            populateEmployeeSelect();
            const modal = new bootstrap.Modal(document.getElementById('advanceModal'));
            modal.show();
        }

        // ملء قائمة الموظفين في نافذة السلفة
        function populateEmployeeSelect() {
            const select = document.getElementById('advanceEmployee');
            select.innerHTML = '<option value="">اختر الموظف</option>';

            employees.filter(emp => emp.isActive).forEach(employee => {
                const option = document.createElement('option');
                option.value = employee.id;
                option.textContent = employee.name;
                select.appendChild(option);
            });
        }

        // حفظ السلفة
        function saveAdvance() {
            const employeeId = parseInt(document.getElementById('advanceEmployee').value);
            const employee = employees.find(emp => emp.id === employeeId);

            if (!employee) {
                alert('يرجى اختيار الموظف');
                return;
            }

            const newAdvance = {
                id: advances.length + 1,
                employeeId: employeeId,
                employeeName: employee.name,
                amount: parseFloat(document.getElementById('advanceAmount').value),
                date: document.getElementById('advanceDate').value,
                reason: document.getElementById('advanceReason').value,
                installments: parseInt(document.getElementById('installments').value),
                remaining: parseFloat(document.getElementById('advanceAmount').value),
                status: 'active'
            };

            advances.push(newAdvance);
            displayAdvances();
            updateStats();

            const modal = bootstrap.Modal.getInstance(document.getElementById('advanceModal'));
            modal.hide();

            document.getElementById('advanceForm').reset();
            alert('تم إضافة السلفة بنجاح!');
        }

        // عرض السلف
        function displayAdvances() {
            const tbody = document.getElementById('advancesTableBody');
            tbody.innerHTML = '';

            advances.forEach(advance => {
                const row = document.createElement('tr');

                row.innerHTML = `
                    <td>${formatDate(advance.date)}</td>
                    <td>${advance.employeeName}</td>
                    <td><span class="advance-amount">${advance.amount.toLocaleString()} ر.ي</span></td>
                    <td>${advance.reason || '-'}</td>
                    <td><span class="advance-amount">${advance.remaining.toLocaleString()} ر.ي</span></td>
                    <td><span class="badge bg-${advance.status === 'active' ? 'warning' : 'success'}">${advance.status === 'active' ? 'نشط' : 'مكتمل'}</span></td>
                    <td>
                        <div class="action-buttons">
                            ${advance.status === 'active' ? `
                                <button class="btn btn-sm btn-outline-success" onclick="payAdvanceInstallment(${advance.id})" title="دفع قسط">
                                    <i class="bi bi-cash-coin"></i>
                                </button>
                            ` : ''}
                            <button class="btn btn-sm btn-outline-info" onclick="viewAdvanceDetails(${advance.id})" title="تفاصيل">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // دفع قسط من السلفة
        function payAdvanceInstallment(advanceId) {
            const advance = advances.find(adv => adv.id === advanceId);
            if (advance && advance.status === 'active') {
                const installmentAmount = advance.amount / advance.installments;
                advance.remaining = Math.max(0, advance.remaining - installmentAmount);

                if (advance.remaining === 0) {
                    advance.status = 'completed';
                }

                displayAdvances();
                updateStats();
                alert(`تم دفع قسط بمبلغ ${installmentAmount.toLocaleString()} ر.ي`);
            }
        }

        // عرض تفاصيل السلفة
        function viewAdvanceDetails(advanceId) {
            const advance = advances.find(adv => adv.id === advanceId);
            if (advance) {
                const installmentAmount = advance.amount / advance.installments;
                const paidAmount = advance.amount - advance.remaining;
                alert(`تفاصيل السلفة:\nالموظف: ${advance.employeeName}\nالمبلغ الكلي: ${advance.amount.toLocaleString()} ر.ي\nالمدفوع: ${paidAmount.toLocaleString()} ر.ي\nالمتبقي: ${advance.remaining.toLocaleString()} ر.ي\nقيمة القسط: ${installmentAmount.toLocaleString()} ر.ي`);
            }
        }

        // إظهار نافذة حساب الرواتب
        function showPayrollModal() {
            const currentDate = new Date();
            document.getElementById('payrollModalMonth').value = currentDate.getMonth() + 1;
            document.getElementById('payrollModalYear').value = currentDate.getFullYear();

            const modal = new bootstrap.Modal(document.getElementById('payrollModal'));
            modal.show();
        }

        // حساب الرواتب
        function calculatePayroll() {
            const month = document.getElementById('payrollModalMonth').value;
            const year = document.getElementById('payrollModalYear').value;

            let totalBasic = 0;
            let totalAllowances = 0;
            let totalDeductions = 0;

            const activeEmployees = employees.filter(emp => emp.isActive);

            activeEmployees.forEach(employee => {
                // حساب خصم السلف
                const employeeAdvances = advances.filter(adv =>
                    adv.employeeId === employee.id && adv.status === 'active'
                );

                let advanceDeduction = 0;
                employeeAdvances.forEach(advance => {
                    const installmentAmount = advance.amount / advance.installments;
                    advanceDeduction += installmentAmount;
                });

                totalBasic += employee.basicSalary;
                totalAllowances += employee.allowances;
                totalDeductions += advanceDeduction;
            });

            const netSalaries = totalBasic + totalAllowances - totalDeductions;

            document.getElementById('totalBasicSalaries').textContent = totalBasic.toLocaleString() + ' ر.ي';
            document.getElementById('totalAllowances').textContent = totalAllowances.toLocaleString() + ' ر.ي';
            document.getElementById('totalDeductions').textContent = totalDeductions.toLocaleString() + ' ر.ي';
            document.getElementById('netSalaries').textContent = netSalaries.toLocaleString() + ' ر.ي';

            document.getElementById('payrollSummary').classList.remove('d-none');
            document.getElementById('confirmPayrollBtn').classList.remove('d-none');
        }

        // تأكيد صرف الرواتب
        function confirmPayroll() {
            if (confirm('هل أنت متأكد من تأكيد صرف الرواتب؟')) {
                // هنا يمكن إضافة منطق حفظ كشف الرواتب
                alert('تم تأكيد صرف الرواتب بنجاح!');

                const modal = bootstrap.Modal.getInstance(document.getElementById('payrollModal'));
                modal.hide();
            }
        }

        // إنشاء كشف الراتب
        function generatePayroll() {
            const month = document.getElementById('payrollMonth').value;
            const year = document.getElementById('payrollYear').value;

            const tbody = document.getElementById('payrollTableBody');
            tbody.innerHTML = '';

            const activeEmployees = employees.filter(emp => emp.isActive);

            activeEmployees.forEach(employee => {
                // حساب خصم السلف
                const employeeAdvances = advances.filter(adv =>
                    adv.employeeId === employee.id && adv.status === 'active'
                );

                let advanceDeduction = 0;
                employeeAdvances.forEach(advance => {
                    const installmentAmount = advance.amount / advance.installments;
                    advanceDeduction += installmentAmount;
                });

                const netSalary = employee.basicSalary + employee.allowances - advanceDeduction;

                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${employee.name}</td>
                    <td>${employee.basicSalary.toLocaleString()} ر.ي</td>
                    <td>${employee.allowances.toLocaleString()} ر.ي</td>
                    <td>0 ر.ي</td>
                    <td class="text-danger">${advanceDeduction.toLocaleString()} ر.ي</td>
                    <td class="text-success"><strong>${netSalary.toLocaleString()} ر.ي</strong></td>
                    <td><span class="badge bg-warning">في الانتظار</span></td>
                `;
                tbody.appendChild(row);
            });
        }

        // طباعة كشف الرواتب
        function printPayroll() {
            window.print();
        }

        // تحميل بيانات الحضور
        function loadAttendance() {
            const date = document.getElementById('attendanceDate').value;
            const tbody = document.getElementById('attendanceTableBody');
            tbody.innerHTML = '';

            // فلترة بيانات الحضور حسب التاريخ
            const dayAttendance = attendance.filter(att => att.date === date);

            if (dayAttendance.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد بيانات حضور لهذا التاريخ</td></tr>';
                return;
            }

            dayAttendance.forEach(att => {
                const row = document.createElement('tr');

                const statusClass = {
                    'present': 'success',
                    'late': 'warning',
                    'absent': 'danger'
                }[att.status];

                const statusText = {
                    'present': 'حاضر',
                    'late': 'متأخر',
                    'absent': 'غائب'
                }[att.status];

                row.innerHTML = `
                    <td>${att.employeeName}</td>
                    <td>${att.checkIn || '-'}</td>
                    <td>${att.checkOut || '-'}</td>
                    <td>${att.workHours || 0} ساعة</td>
                    <td><span class="badge bg-${statusClass}">${statusText}</span></td>
                    <td>${att.notes || '-'}</td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث البيانات
        function refreshData() {
            displayEmployees();
            displayAdvances();
            updateStats();
            alert('تم تحديث البيانات!');
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-SA');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من المصادقة
            const user = checkAuth();
            if (user) {
                document.getElementById('sidebarUserName').textContent = user.name;
            }

            // استعادة حالة القائمة الجانبية
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }

            // عرض البيانات
            displayEmployees();
            displayAdvances();
            updateStats();

            // تعيين التاريخ الحالي لحقل الحضور
            document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];

            // ربط أحداث الفلترة
            document.getElementById('searchInput').addEventListener('input', filterEmployees);
            document.getElementById('departmentFilter').addEventListener('change', filterEmployees);
            document.getElementById('statusFilter').addEventListener('change', filterEmployees);

            // تأثيرات بصرية
            const cards = document.querySelectorAll('.stats-card, .card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>