<?php
/**
 * الإعدادات العامة للنظام
 * General System Configuration
 */

// بدء الجلسة
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// تضمين ملف قاعدة البيانات
require_once __DIR__ . '/database.php';
require_once __DIR__ . '/constants.php';

// إعدادات النظام العامة
define('SYSTEM_NAME', 'نظام محاسبة المخبز');
define('SYSTEM_NAME_EN', 'Bakery Accounting System');
define('SYSTEM_VERSION', '1.0.0');
define('SYSTEM_AUTHOR', 'AnwarSoft');

// إعدادات المسارات
define('BASE_PATH', dirname(__DIR__));
define('BASE_URL', 'http://localhost/anwarsoft');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_PATH', BASE_PATH . '/uploads');
define('UPLOADS_URL', BASE_URL . '/uploads');

// إعدادات الأمان
define('HASH_ALGO', 'sha256');
define('SESSION_TIMEOUT', 3600); // ساعة واحدة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 دقيقة

// إعدادات التاريخ والوقت
date_default_timezone_set('Asia/Aden');
define('DATE_FORMAT', 'Y-m-d');
define('DATETIME_FORMAT', 'Y-m-d H:i:s');
define('DISPLAY_DATE_FORMAT', 'd/m/Y');
define('DISPLAY_DATETIME_FORMAT', 'd/m/Y H:i');

// إعدادات العملة
define('CURRENCY_CODE', 'YER');
define('CURRENCY_SYMBOL', 'ر.ي');
define('CURRENCY_DECIMALS', 3);

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('RTL_LANGUAGES', ['ar', 'he', 'fa']);

// إعدادات الملفات
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5 ميجابايت
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif']);
define('ALLOWED_DOCUMENT_TYPES', ['pdf', 'doc', 'docx', 'xls', 'xlsx']);

/**
 * دالة للحصول على إعدادات المنشأة
 */
function getCompanySettings() {
    static $settings = null;
    
    if ($settings === null) {
        try {
            $settings = queryOne("SELECT * FROM company_settings WHERE id = 1");
            if (!$settings) {
                // إعدادات افتراضية
                $settings = [
                    'company_name' => 'مخبز الأنوار',
                    'currency' => 'YER',
                    'currency_symbol' => 'ر.ي',
                    'tax_rate' => 0.00,
                    'language' => 'ar',
                    'date_format' => 'Y-m-d',
                    'print_format' => 'normal'
                ];
            }
        } catch (Exception $e) {
            // في حالة عدم وجود قاعدة البيانات
            $settings = [
                'company_name' => 'مخبز الأنوار',
                'currency' => 'YER',
                'currency_symbol' => 'ر.ي',
                'tax_rate' => 0.00,
                'language' => 'ar',
                'date_format' => 'Y-m-d',
                'print_format' => 'normal'
            ];
        }
    }
    
    return $settings;
}

/**
 * دالة لتنسيق المبالغ المالية
 */
function formatMoney($amount, $showSymbol = true) {
    $settings = getCompanySettings();
    $formatted = number_format($amount, CURRENCY_DECIMALS, '.', ',');
    
    if ($showSymbol) {
        return $formatted . ' ' . $settings['currency_symbol'];
    }
    
    return $formatted;
}

/**
 * دالة لتنسيق التاريخ
 */
function formatDate($date, $format = null) {
    if (empty($date) || $date == '0000-00-00') {
        return '';
    }
    
    if ($format === null) {
        $format = DISPLAY_DATE_FORMAT;
    }
    
    return date($format, strtotime($date));
}

/**
 * دالة لتنسيق التاريخ والوقت
 */
function formatDateTime($datetime, $format = null) {
    if (empty($datetime) || $datetime == '0000-00-00 00:00:00') {
        return '';
    }
    
    if ($format === null) {
        $format = DISPLAY_DATETIME_FORMAT;
    }
    
    return date($format, strtotime($datetime));
}

/**
 * دالة للحصول على التاريخ الحالي
 */
function getCurrentDate() {
    return date(DATE_FORMAT);
}

/**
 * دالة للحصول على التاريخ والوقت الحالي
 */
function getCurrentDateTime() {
    return date(DATETIME_FORMAT);
}

/**
 * دالة لتنظيف البيانات المدخلة
 */
function cleanInput($data) {
    if (is_array($data)) {
        return array_map('cleanInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    
    return $data;
}

/**
 * دالة للتحقق من صحة البريد الإلكتروني
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * دالة للتحقق من صحة رقم الهاتف
 */
function isValidPhone($phone) {
    return preg_match('/^[0-9+\-\s()]+$/', $phone);
}

/**
 * دالة لإنشاء رمز عشوائي
 */
function generateRandomCode($length = 8) {
    $characters = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $code = '';
    
    for ($i = 0; $i < $length; $i++) {
        $code .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $code;
}

/**
 * دالة لتسجيل الأخطاء
 */
function logError($message, $file = null, $line = null) {
    $logFile = BASE_PATH . '/logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    
    if ($file && $line) {
        $logMessage .= " في الملف: $file السطر: $line";
    }
    
    $logMessage .= PHP_EOL;
    
    // إنشاء مجلد السجلات إذا لم يكن موجوداً
    $logDir = dirname($logFile);
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND | LOCK_EX);
}

/**
 * دالة لعرض الرسائل
 */
function showMessage($message, $type = 'info') {
    $_SESSION['message'] = [
        'text' => $message,
        'type' => $type
    ];
}

/**
 * دالة لعرض الرسائل المحفوظة
 */
function displayMessage() {
    if (isset($_SESSION['message'])) {
        $message = $_SESSION['message'];
        unset($_SESSION['message']);
        
        $alertClass = 'alert-info';
        switch ($message['type']) {
            case 'success':
                $alertClass = 'alert-success';
                break;
            case 'error':
                $alertClass = 'alert-danger';
                break;
            case 'warning':
                $alertClass = 'alert-warning';
                break;
        }
        
        echo "<div class='alert $alertClass alert-dismissible fade show' role='alert'>";
        echo htmlspecialchars($message['text']);
        echo "<button type='button' class='btn-close' data-bs-dismiss='alert'></button>";
        echo "</div>";
    }
}
?>
