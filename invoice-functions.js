// وظائف إدارة الفواتير

// عرض فواتير المبيعات
function displaySalesInvoices(dataToShow = salesInvoices) {
    const container = document.getElementById('salesInvoicesList');
    container.innerHTML = '';

    dataToShow.forEach(invoice => {
        const invoiceDiv = document.createElement('div');
        invoiceDiv.className = 'invoice-card invoice-sales';

        const statusText = {
            'draft': 'مسودة',
            'completed': 'مكتملة',
            'pending': 'معلقة',
            'cancelled': 'ملغية'
        }[invoice.status];

        const statusClass = {
            'draft': 'secondary',
            'completed': 'success',
            'pending': 'warning',
            'cancelled': 'danger'
        }[invoice.status];

        const paymentText = {
            'cash': 'نقدي',
            'credit': 'آجل',
            'bank': 'بنكي'
        }[invoice.paymentMethod];

        invoiceDiv.innerHTML = `
            <div class="invoice-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1"><strong>${invoice.number}</strong></h6>
                        <small class="text-muted">${formatDate(invoice.date)} | ${paymentText}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${statusClass}">${statusText}</span>
                        <div class="mt-1">
                            <strong class="text-success">${invoice.total.toLocaleString()} ر.ي</strong>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <strong>العميل:</strong> ${invoice.customerName || 'عميل نقدي'}
                <br><strong>عدد الأصناف:</strong> ${invoice.items.length}
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        المجموع الفرعي: ${invoice.subtotal.toLocaleString()} ر.ي
                        ${invoice.discount > 0 ? ` | خصم: ${invoice.discount.toLocaleString()} ر.ي` : ''}
                    </small>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewInvoiceDetails('sales', ${invoice.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="printInvoice('sales', ${invoice.id})" title="طباعة">
                        <i class="bi bi-printer"></i>
                    </button>
                    ${invoice.status === 'draft' ? `
                        <button class="btn btn-sm btn-outline-warning" onclick="editInvoice('sales', ${invoice.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline-danger" onclick="createReturn('sales', ${invoice.id})" title="مرتجع">
                        <i class="bi bi-arrow-return-left"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(invoiceDiv);
    });
}

// عرض فواتير المشتريات
function displayPurchaseInvoices(dataToShow = purchaseInvoices) {
    const container = document.getElementById('purchaseInvoicesList');
    container.innerHTML = '';

    dataToShow.forEach(invoice => {
        const invoiceDiv = document.createElement('div');
        invoiceDiv.className = 'invoice-card invoice-purchase';

        const statusText = {
            'draft': 'مسودة',
            'completed': 'مكتملة',
            'pending': 'معلقة',
            'cancelled': 'ملغية'
        }[invoice.status];

        const statusClass = {
            'draft': 'secondary',
            'completed': 'success',
            'pending': 'warning',
            'cancelled': 'danger'
        }[invoice.status];

        invoiceDiv.innerHTML = `
            <div class="invoice-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1"><strong>${invoice.number}</strong></h6>
                        <small class="text-muted">${formatDate(invoice.date)}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${statusClass}">${statusText}</span>
                        <div class="mt-1">
                            <strong class="text-danger">${invoice.total.toLocaleString()} ر.ي</strong>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <strong>المورد:</strong> ${invoice.supplierName}
                <br><strong>فاتورة المورد:</strong> ${invoice.supplierInvoiceNumber || 'غير محدد'}
                <br><strong>عدد الأصناف:</strong> ${invoice.items.length}
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        المجموع الفرعي: ${invoice.subtotal.toLocaleString()} ر.ي
                        ${invoice.discount > 0 ? ` | خصم: ${invoice.discount.toLocaleString()} ر.ي` : ''}
                    </small>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewInvoiceDetails('purchase', ${invoice.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="printInvoice('purchase', ${invoice.id})" title="طباعة">
                        <i class="bi bi-printer"></i>
                    </button>
                    ${invoice.status === 'draft' ? `
                        <button class="btn btn-sm btn-outline-warning" onclick="editInvoice('purchase', ${invoice.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline-danger" onclick="createReturn('purchase', ${invoice.id})" title="مرتجع">
                        <i class="bi bi-arrow-return-left"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(invoiceDiv);
    });
}

// عرض فواتير المرتجعات
function displayReturnInvoices(dataToShow = returnInvoices) {
    const container = document.getElementById('returnInvoicesList');
    container.innerHTML = '';

    dataToShow.forEach(returnInv => {
        const invoiceDiv = document.createElement('div');
        invoiceDiv.className = 'invoice-card invoice-return';

        const typeText = {
            'sales_return': 'مرتجع مبيعات',
            'purchase_return': 'مرتجع مشتريات'
        }[returnInv.type];

        const reasonText = {
            'damaged': 'تالف',
            'expired': 'منتهي الصلاحية',
            'wrong_item': 'صنف خاطئ',
            'customer_request': 'طلب العميل',
            'quality_issue': 'مشكلة في الجودة'
        }[returnInv.reason];

        invoiceDiv.innerHTML = `
            <div class="invoice-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1"><strong>${returnInv.number}</strong></h6>
                        <small class="text-muted">${formatDate(returnInv.date)} | ${typeText}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-warning">مرتجع</span>
                        <div class="mt-1">
                            <strong class="text-warning">${returnInv.total.toLocaleString()} ر.ي</strong>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <strong>الفاتورة الأصلية:</strong> ${returnInv.originalInvoiceNumber}
                <br><strong>السبب:</strong> ${reasonText}
                <br><strong>عدد الأصناف:</strong> ${returnInv.items.length}
            </div>

            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">إجمالي المرتجع</small>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewReturnDetails(${returnInv.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="printReturn(${returnInv.id})" title="طباعة">
                        <i class="bi bi-printer"></i>
                    </button>
                </div>
            </div>
        `;

        container.appendChild(invoiceDiv);
    });
}

// تحديث إحصائيات الفواتير
function updateInvoiceStats() {
    const totalSales = salesInvoices.filter(inv => inv.status === 'completed').length;
    const totalPurchases = purchaseInvoices.filter(inv => inv.status === 'completed').length;
    const totalReturns = returnInvoices.length;
    const totalSalesAmount = salesInvoices
        .filter(inv => inv.status === 'completed')
        .reduce((sum, inv) => sum + inv.total, 0);

    document.getElementById('totalSalesInvoices').textContent = totalSales;
    document.getElementById('totalPurchaseInvoices').textContent = totalPurchases;
    document.getElementById('totalReturnInvoices').textContent = totalReturns;
    document.getElementById('totalSalesAmount').textContent = totalSalesAmount.toLocaleString();
}

// إظهار نافذة فاتورة مبيعات
function showSalesInvoiceModal() {
    document.getElementById('salesInvoiceForm').reset();
    document.getElementById('salesInvoiceNumber').value = generateInvoiceNumber('sales');
    document.getElementById('salesInvoiceDate').value = new Date().toISOString().split('T')[0];
    populateItemSelects();
    clearSalesItems();
    const modal = new bootstrap.Modal(document.getElementById('salesInvoiceModal'));
    modal.show();
}

// إظهار نافذة فاتورة مشتريات
function showPurchaseInvoiceModal() {
    document.getElementById('purchaseInvoiceForm').reset();
    document.getElementById('purchaseInvoiceNumber').value = generateInvoiceNumber('purchase');
    document.getElementById('purchaseInvoiceDate').value = new Date().toISOString().split('T')[0];
    populateItemSelects();
    clearPurchaseItems();
    const modal = new bootstrap.Modal(document.getElementById('purchaseInvoiceModal'));
    modal.show();
}

// إظهار نافذة فاتورة مرتجع
function showReturnInvoiceModal() {
    document.getElementById('returnInvoiceForm').reset();
    document.getElementById('returnInvoiceNumber').value = generateInvoiceNumber('return');
    document.getElementById('returnInvoiceDate').value = new Date().toISOString().split('T')[0];
    populateItemSelects();
    clearReturnItems();
    const modal = new bootstrap.Modal(document.getElementById('returnInvoiceModal'));
    modal.show();
}

// ملء قوائم الأصناف
function populateItemSelects() {
    const selects = ['salesItemSelect', 'purchaseItemSelect', 'returnItemSelect'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '<option value="">اختر الصنف</option>';

            // إضافة المنتجات
            if (typeof products !== 'undefined') {
                products.forEach(product => {
                    const option = document.createElement('option');
                    option.value = product.id;
                    option.textContent = product.name;
                    option.dataset.price = product.sellingPrice || product.costPrice || 0;
                    option.dataset.unit = product.unit;
                    select.appendChild(option);
                });
            }

            // إضافة المواد الخام
            if (typeof items !== 'undefined') {
                items.forEach(item => {
                    const option = document.createElement('option');
                    option.value = `item_${item.id}`;
                    option.textContent = item.name;
                    option.dataset.price = item.sellingPrice || item.costPrice || 0;
                    option.dataset.unit = item.unit;
                    select.appendChild(option);
                });
            }
        }
    });
}

// حساب إجمالي صنف المبيعات
function calculateSalesItemTotal() {
    const quantity = parseFloat(document.getElementById('salesItemQuantity').value) || 0;
    const price = parseFloat(document.getElementById('salesItemPrice').value) || 0;
    const total = quantity * price;
    document.getElementById('salesItemTotal').value = total.toLocaleString();
}

// حساب إجمالي صنف المشتريات
function calculatePurchaseItemTotal() {
    const quantity = parseFloat(document.getElementById('purchaseItemQuantity').value) || 0;
    const price = parseFloat(document.getElementById('purchaseItemPrice').value) || 0;
    const total = quantity * price;
    document.getElementById('purchaseItemTotal').value = total.toLocaleString();
}

// إضافة صنف للمبيعات
function addSalesItem() {
    const select = document.getElementById('salesItemSelect');
    const quantity = parseFloat(document.getElementById('salesItemQuantity').value) || 0;
    const price = parseFloat(document.getElementById('salesItemPrice').value) || 0;

    if (!select.value || quantity <= 0 || price <= 0) {
        alert('يرجى ملء جميع البيانات بشكل صحيح');
        return;
    }

    const selectedOption = select.options[select.selectedIndex];
    const itemName = selectedOption.textContent;
    const unit = selectedOption.dataset.unit;
    const total = quantity * price;

    const tbody = document.getElementById('salesItemsTableBody');
    const row = document.createElement('tr');

    row.innerHTML = `
        <td>${itemName}</td>
        <td>${quantity.toLocaleString()}</td>
        <td>${unit}</td>
        <td>${price.toLocaleString()}</td>
        <td>${total.toLocaleString()}</td>
        <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removeSalesItem(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;

    row.dataset.itemId = select.value;
    row.dataset.quantity = quantity;
    row.dataset.price = price;
    row.dataset.total = total;

    tbody.appendChild(row);

    // مسح الحقول
    document.getElementById('salesItemQuantity').value = '';
    document.getElementById('salesItemPrice').value = '';
    document.getElementById('salesItemTotal').value = '';
    select.value = '';

    calculateSalesTotals();
}

// إزالة صنف من المبيعات
function removeSalesItem(button) {
    button.closest('tr').remove();
    calculateSalesTotals();
}

// حساب إجماليات المبيعات
function calculateSalesTotals() {
    const rows = document.querySelectorAll('#salesItemsTableBody tr');
    let subtotal = 0;

    rows.forEach(row => {
        subtotal += parseFloat(row.dataset.total) || 0;
    });

    const discount = parseFloat(document.getElementById('salesDiscount').value) || 0;
    const taxRate = parseFloat(document.getElementById('salesTax').value) || 0;

    const afterDiscount = subtotal - discount;
    const taxAmount = (afterDiscount * taxRate) / 100;
    const grandTotal = afterDiscount + taxAmount;

    document.getElementById('salesSubtotal').textContent = subtotal.toLocaleString();
    document.getElementById('salesAfterDiscount').textContent = afterDiscount.toLocaleString();
    document.getElementById('salesTaxAmount').textContent = taxAmount.toLocaleString();
    document.getElementById('salesGrandTotal').textContent = grandTotal.toLocaleString();
}

// مسح أصناف المبيعات
function clearSalesItems() {
    document.getElementById('salesItemsTableBody').innerHTML = '';
    calculateSalesTotals();
}

// إنشاء رقم فاتورة
function generateInvoiceNumber(type) {
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');

    let prefix, count;
    switch (type) {
        case 'sales':
            prefix = 'INV-S';
            count = salesInvoices.length + 1;
            break;
        case 'purchase':
            prefix = 'INV-P';
            count = purchaseInvoices.length + 1;
            break;
        case 'return':
            prefix = 'RET';
            count = returnInvoices.length + 1;
            break;
        default:
            prefix = 'INV';
            count = 1;
    }

    return `${prefix}-${year}${month}${String(count).padStart(3, '0')}`;
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// وظائف إضافية
function viewInvoiceDetails(type, invoiceId) {
    alert(`عرض تفاصيل فاتورة ${type} رقم ${invoiceId}`);
}

function editInvoice(type, invoiceId) {
    alert(`تعديل فاتورة ${type} رقم ${invoiceId}`);
}

function createReturn(type, invoiceId) {
    alert(`إنشاء مرتجع لفاتورة ${type} رقم ${invoiceId}`);
}

function viewReturnDetails(returnId) {
    alert(`عرض تفاصيل المرتجع رقم ${returnId}`);
}

function printReturn(returnId) {
    alert(`طباعة المرتجع رقم ${returnId}`);
}

// فلترة الفواتير
function filterSalesInvoices() {
    // تنفيذ فلترة فواتير المبيعات
    displaySalesInvoices();
}

function filterPurchaseInvoices() {
    // تنفيذ فلترة فواتير المشتريات
    displayPurchaseInvoices();
}

// إضافة صنف للمشتريات
function addPurchaseItem() {
    const select = document.getElementById('purchaseItemSelect');
    const quantity = parseFloat(document.getElementById('purchaseItemQuantity').value) || 0;
    const price = parseFloat(document.getElementById('purchaseItemPrice').value) || 0;

    if (!select.value || quantity <= 0 || price <= 0) {
        alert('يرجى ملء جميع البيانات بشكل صحيح');
        return;
    }

    const selectedOption = select.options[select.selectedIndex];
    const itemName = selectedOption.textContent;
    const unit = selectedOption.dataset.unit;
    const total = quantity * price;

    const tbody = document.getElementById('purchaseItemsTableBody');
    const row = document.createElement('tr');

    row.innerHTML = `
        <td>${itemName}</td>
        <td>${quantity.toLocaleString()}</td>
        <td>${unit}</td>
        <td>${price.toLocaleString()}</td>
        <td>${total.toLocaleString()}</td>
        <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removePurchaseItem(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;

    row.dataset.itemId = select.value;
    row.dataset.quantity = quantity;
    row.dataset.price = price;
    row.dataset.total = total;

    tbody.appendChild(row);

    // مسح الحقول
    document.getElementById('purchaseItemQuantity').value = '';
    document.getElementById('purchaseItemPrice').value = '';
    document.getElementById('purchaseItemTotal').value = '';
    select.value = '';

    calculatePurchaseTotals();
}

// إزالة صنف من المشتريات
function removePurchaseItem(button) {
    button.closest('tr').remove();
    calculatePurchaseTotals();
}

// حساب إجماليات المشتريات
function calculatePurchaseTotals() {
    const rows = document.querySelectorAll('#purchaseItemsTableBody tr');
    let subtotal = 0;

    rows.forEach(row => {
        subtotal += parseFloat(row.dataset.total) || 0;
    });

    const discount = parseFloat(document.getElementById('purchaseDiscount').value) || 0;
    const taxRate = parseFloat(document.getElementById('purchaseTax').value) || 0;

    const afterDiscount = subtotal - discount;
    const taxAmount = (afterDiscount * taxRate) / 100;
    const grandTotal = afterDiscount + taxAmount;

    document.getElementById('purchaseSubtotal').textContent = subtotal.toLocaleString();
    document.getElementById('purchaseAfterDiscount').textContent = afterDiscount.toLocaleString();
    document.getElementById('purchaseTaxAmount').textContent = taxAmount.toLocaleString();
    document.getElementById('purchaseGrandTotal').textContent = grandTotal.toLocaleString();
}

// مسح أصناف المشتريات
function clearPurchaseItems() {
    document.getElementById('purchaseItemsTableBody').innerHTML = '';
    calculatePurchaseTotals();
}

// إضافة صنف للمرتجع
function addReturnItem() {
    const select = document.getElementById('returnItemSelect');
    const quantity = parseFloat(document.getElementById('returnItemQuantity').value) || 0;
    const price = parseFloat(document.getElementById('returnItemPrice').value) || 0;

    if (!select.value || quantity <= 0 || price <= 0) {
        alert('يرجى ملء جميع البيانات بشكل صحيح');
        return;
    }

    const selectedOption = select.options[select.selectedIndex];
    const itemName = selectedOption.textContent;
    const total = quantity * price;

    const tbody = document.getElementById('returnItemsTableBody');
    const row = document.createElement('tr');

    row.innerHTML = `
        <td>${itemName}</td>
        <td>${quantity.toLocaleString()}</td>
        <td>${price.toLocaleString()}</td>
        <td>${total.toLocaleString()}</td>
        <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removeReturnItem(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;

    row.dataset.itemId = select.value;
    row.dataset.quantity = quantity;
    row.dataset.price = price;
    row.dataset.total = total;

    tbody.appendChild(row);

    // مسح الحقول
    document.getElementById('returnItemQuantity').value = '';
    document.getElementById('returnItemPrice').value = '';
    select.value = '';

    calculateReturnTotal();
}

// إزالة صنف من المرتجع
function removeReturnItem(button) {
    button.closest('tr').remove();
    calculateReturnTotal();
}

// حساب إجمالي المرتجع
function calculateReturnTotal() {
    const rows = document.querySelectorAll('#returnItemsTableBody tr');
    let total = 0;

    rows.forEach(row => {
        total += parseFloat(row.dataset.total) || 0;
    });

    document.getElementById('returnGrandTotal').textContent = total.toLocaleString();
}

// مسح أصناف المرتجع
function clearReturnItems() {
    document.getElementById('returnItemsTableBody').innerHTML = '';
    calculateReturnTotal();
}

// حفظ فاتورة مبيعات
function saveSalesInvoice() {
    const rows = document.querySelectorAll('#salesItemsTableBody tr');
    if (rows.length === 0) {
        alert('يرجى إضافة أصناف للفاتورة');
        return;
    }

    const items = [];
    rows.forEach(row => {
        items.push({
            itemId: row.dataset.itemId,
            itemName: row.cells[0].textContent,
            quantity: parseFloat(row.dataset.quantity),
            price: parseFloat(row.dataset.price),
            total: parseFloat(row.dataset.total)
        });
    });

    const subtotal = parseFloat(document.getElementById('salesSubtotal').textContent.replace(/,/g, ''));
    const discount = parseFloat(document.getElementById('salesDiscount').value) || 0;
    const tax = parseFloat(document.getElementById('salesTaxAmount').textContent.replace(/,/g, ''));
    const total = parseFloat(document.getElementById('salesGrandTotal').textContent.replace(/,/g, ''));

    const newInvoice = {
        id: salesInvoices.length + 1,
        number: document.getElementById('salesInvoiceNumber').value,
        date: document.getElementById('salesInvoiceDate').value,
        customerName: document.getElementById('salesCustomerName').value || 'عميل نقدي',
        paymentMethod: document.getElementById('salesPaymentMethod').value,
        status: 'completed',
        subtotal: subtotal,
        discount: discount,
        tax: tax,
        total: total,
        items: items,
        notes: document.getElementById('salesNotes').value
    };

    salesInvoices.push(newInvoice);

    // إنشاء قيد محاسبي تلقائي
    createSalesJournalEntry(newInvoice);

    // تحديث المخزون
    updateInventoryFromSales(newInvoice);

    displaySalesInvoices();
    updateInvoiceStats();

    const modal = bootstrap.Modal.getInstance(document.getElementById('salesInvoiceModal'));
    modal.hide();

    // طباعة الفاتورة
    printInvoice('sales', newInvoice.id);

    alert('تم حفظ فاتورة المبيعات بنجاح!');
}

// حفظ فاتورة مشتريات
function savePurchaseInvoice() {
    const rows = document.querySelectorAll('#purchaseItemsTableBody tr');
    if (rows.length === 0) {
        alert('يرجى إضافة أصناف للفاتورة');
        return;
    }

    const items = [];
    rows.forEach(row => {
        items.push({
            itemId: row.dataset.itemId,
            itemName: row.cells[0].textContent,
            quantity: parseFloat(row.dataset.quantity),
            price: parseFloat(row.dataset.price),
            total: parseFloat(row.dataset.total)
        });
    });

    const subtotal = parseFloat(document.getElementById('purchaseSubtotal').textContent.replace(/,/g, ''));
    const discount = parseFloat(document.getElementById('purchaseDiscount').value) || 0;
    const tax = parseFloat(document.getElementById('purchaseTaxAmount').textContent.replace(/,/g, ''));
    const total = parseFloat(document.getElementById('purchaseGrandTotal').textContent.replace(/,/g, ''));

    const newInvoice = {
        id: purchaseInvoices.length + 1,
        number: document.getElementById('purchaseInvoiceNumber').value,
        date: document.getElementById('purchaseInvoiceDate').value,
        supplierName: document.getElementById('supplierName').value,
        supplierInvoiceNumber: document.getElementById('supplierInvoiceNumber').value,
        status: 'completed',
        subtotal: subtotal,
        discount: discount,
        tax: tax,
        total: total,
        items: items,
        notes: document.getElementById('purchaseNotes').value
    };

    purchaseInvoices.push(newInvoice);

    // إنشاء قيد محاسبي تلقائي
    createPurchaseJournalEntry(newInvoice);

    // تحديث المخزون
    updateInventoryFromPurchase(newInvoice);

    displayPurchaseInvoices();
    updateInvoiceStats();

    const modal = bootstrap.Modal.getInstance(document.getElementById('purchaseInvoiceModal'));
    modal.hide();

    // طباعة الفاتورة
    printInvoice('purchase', newInvoice.id);

    alert('تم حفظ فاتورة المشتريات بنجاح!');
}

// حفظ فاتورة مرتجع
function saveReturnInvoice() {
    const rows = document.querySelectorAll('#returnItemsTableBody tr');
    if (rows.length === 0) {
        alert('يرجى إضافة أصناف للمرتجع');
        return;
    }

    const items = [];
    rows.forEach(row => {
        items.push({
            itemId: row.dataset.itemId,
            itemName: row.cells[0].textContent,
            quantity: parseFloat(row.dataset.quantity),
            price: parseFloat(row.dataset.price),
            total: parseFloat(row.dataset.total)
        });
    });

    const total = parseFloat(document.getElementById('returnGrandTotal').textContent.replace(/,/g, ''));

    const newReturn = {
        id: returnInvoices.length + 1,
        number: document.getElementById('returnInvoiceNumber').value,
        date: document.getElementById('returnInvoiceDate').value,
        type: document.getElementById('returnType').value,
        originalInvoiceNumber: document.getElementById('originalInvoiceNumber').value,
        reason: document.getElementById('returnReason').value,
        total: total,
        items: items,
        notes: document.getElementById('returnNotes').value
    };

    returnInvoices.push(newReturn);

    // إنشاء قيد محاسبي تلقائي
    createReturnJournalEntry(newReturn);

    // تحديث المخزون
    updateInventoryFromReturn(newReturn);

    displayReturnInvoices();
    updateInvoiceStats();

    const modal = bootstrap.Modal.getInstance(document.getElementById('returnInvoiceModal'));
    modal.hide();

    alert('تم حفظ فاتورة المرتجع بنجاح!');
}

// إنشاء قيد محاسبي للمبيعات
function createSalesJournalEntry(invoice) {
    if (typeof journalEntries === 'undefined') return;

    const paymentAccount = invoice.paymentMethod === 'cash' ? 'النقدية' :
                          invoice.paymentMethod === 'credit' ? 'العملاء' : 'البنك';

    const newEntry = {
        id: journalEntries.length + 1,
        number: `JE-S-${invoice.number}`,
        date: invoice.date,
        type: 'auto',
        description: `قيد مبيعات - فاتورة ${invoice.number}`,
        reference: invoice.number,
        status: 'approved',
        createdBy: 'النظام',
        reviewedBy: null,
        isBalanced: true,
        totalAmount: invoice.total,
        details: [
            { accountId: 1, accountName: paymentAccount, debit: invoice.total, credit: 0 },
            { accountId: 2, accountName: 'المبيعات', debit: 0, credit: invoice.total }
        ]
    };

    journalEntries.push(newEntry);
}

// إنشاء قيد محاسبي للمشتريات
function createPurchaseJournalEntry(invoice) {
    if (typeof journalEntries === 'undefined') return;

    const newEntry = {
        id: journalEntries.length + 1,
        number: `JE-P-${invoice.number}`,
        date: invoice.date,
        type: 'auto',
        description: `قيد مشتريات - فاتورة ${invoice.number}`,
        reference: invoice.number,
        status: 'approved',
        createdBy: 'النظام',
        reviewedBy: null,
        isBalanced: true,
        totalAmount: invoice.total,
        details: [
            { accountId: 4, accountName: 'المخزون', debit: invoice.total, credit: 0 },
            { accountId: 5, accountName: 'الموردون', debit: 0, credit: invoice.total }
        ]
    };

    journalEntries.push(newEntry);
}

// إنشاء قيد محاسبي للمرتجعات
function createReturnJournalEntry(returnInv) {
    if (typeof journalEntries === 'undefined') return;

    const isReturn = returnInv.type === 'sales_return';
    const description = isReturn ? 'قيد مرتجع مبيعات' : 'قيد مرتجع مشتريات';

    const newEntry = {
        id: journalEntries.length + 1,
        number: `JE-R-${returnInv.number}`,
        date: returnInv.date,
        type: 'auto',
        description: `${description} - ${returnInv.number}`,
        reference: returnInv.number,
        status: 'approved',
        createdBy: 'النظام',
        reviewedBy: null,
        isBalanced: true,
        totalAmount: returnInv.total,
        details: isReturn ? [
            { accountId: 2, accountName: 'المبيعات', debit: returnInv.total, credit: 0 },
            { accountId: 1, accountName: 'النقدية', debit: 0, credit: returnInv.total }
        ] : [
            { accountId: 5, accountName: 'الموردون', debit: returnInv.total, credit: 0 },
            { accountId: 4, accountName: 'المخزون', debit: 0, credit: returnInv.total }
        ]
    };

    journalEntries.push(newEntry);
}

// تحديث المخزون من المبيعات
function updateInventoryFromSales(invoice) {
    // تنفيذ تحديث المخزون
    console.log('تحديث المخزون من المبيعات:', invoice);
}

// تحديث المخزون من المشتريات
function updateInventoryFromPurchase(invoice) {
    // تنفيذ تحديث المخزون
    console.log('تحديث المخزون من المشتريات:', invoice);
}

// تحديث المخزون من المرتجعات
function updateInventoryFromReturn(returnInv) {
    // تنفيذ تحديث المخزون
    console.log('تحديث المخزون من المرتجعات:', returnInv);
}
