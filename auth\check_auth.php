<?php
/**
 * فحص المصادقة والصلاحيات
 * Authentication and Authorization Check
 */

require_once __DIR__ . '/../config/config.php';

/**
 * فحص تسجيل الدخول
 */
function checkLogin() {
    if (!isset($_SESSION['user_id']) || !isset($_SESSION['username'])) {
        header('Location: ' . BASE_URL . '/auth/login.php');
        exit();
    }
    
    // فحص انتهاء الجلسة
    if (isset($_SESSION['last_activity']) && 
        (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT)) {
        session_destroy();
        header('Location: ' . BASE_URL . '/auth/login.php?timeout=1');
        exit();
    }
    
    $_SESSION['last_activity'] = time();
}

/**
 * فحص الصلاحيات
 */
function checkPermission($permission) {
    checkLogin();
    
    // المدير له جميع الصلاحيات
    if ($_SESSION['role'] === 'admin') {
        return true;
    }
    
    // فحص الصلاحيات المحددة
    $permissions = json_decode($_SESSION['permissions'] ?? '{}', true);
    
    if (isset($permissions['all']) && $permissions['all'] === true) {
        return true;
    }
    
    if (isset($permissions[$permission]) && $permissions[$permission] === true) {
        return true;
    }
    
    // إعادة توجيه في حالة عدم وجود صلاحية
    header('Location: ' . BASE_URL . '/index.php?error=permission');
    exit();
}

/**
 * الحصول على بيانات المستخدم الحالي
 */
function getCurrentUser() {
    checkLogin();
    
    static $user = null;
    
    if ($user === null) {
        try {
            $user = queryOne(
                "SELECT id, username, full_name, email, phone, role, permissions, 
                        is_active, last_login, created_at 
                 FROM users 
                 WHERE id = ? AND is_active = 1",
                [$_SESSION['user_id']]
            );
            
            if (!$user) {
                session_destroy();
                header('Location: ' . BASE_URL . '/auth/login.php?error=user_not_found');
                exit();
            }
        } catch (Exception $e) {
            logError("خطأ في الحصول على بيانات المستخدم: " . $e->getMessage());
            session_destroy();
            header('Location: ' . BASE_URL . '/auth/login.php?error=database');
            exit();
        }
    }
    
    return $user;
}

/**
 * فحص صلاحية محددة دون إعادة توجيه
 */
function hasPermission($permission) {
    if (!isset($_SESSION['user_id'])) {
        return false;
    }
    
    // المدير له جميع الصلاحيات
    if ($_SESSION['role'] === 'admin') {
        return true;
    }
    
    $permissions = json_decode($_SESSION['permissions'] ?? '{}', true);
    
    if (isset($permissions['all']) && $permissions['all'] === true) {
        return true;
    }
    
    return isset($permissions[$permission]) && $permissions[$permission] === true;
}

/**
 * تسجيل عملية المستخدم
 */
function logUserActivity($action, $table_name = null, $record_id = null, $old_data = null, $new_data = null) {
    if (!isset($_SESSION['user_id'])) {
        return;
    }
    
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        execute(
            "INSERT INTO user_logs (user_id, action, table_name, record_id, old_data, new_data, ip_address, user_agent) 
             VALUES (?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $_SESSION['user_id'],
                $action,
                $table_name,
                $record_id,
                $old_data ? json_encode($old_data, JSON_UNESCAPED_UNICODE) : null,
                $new_data ? json_encode($new_data, JSON_UNESCAPED_UNICODE) : null,
                $ip_address,
                $user_agent
            ]
        );
    } catch (Exception $e) {
        logError("خطأ في تسجيل نشاط المستخدم: " . $e->getMessage());
    }
}

/**
 * تحديث وقت آخر تسجيل دخول
 */
function updateLastLogin($user_id) {
    try {
        execute(
            "UPDATE users SET last_login = NOW() WHERE id = ?",
            [$user_id]
        );
    } catch (Exception $e) {
        logError("خطأ في تحديث وقت آخر تسجيل دخول: " . $e->getMessage());
    }
}

/**
 * فحص محاولات تسجيل الدخول
 */
function checkLoginAttempts($username) {
    try {
        $attempts = queryOne(
            "SELECT COUNT(*) as count 
             FROM user_logs 
             WHERE action = 'failed_login' 
             AND new_data LIKE ? 
             AND created_at > DATE_SUB(NOW(), INTERVAL ? SECOND)",
            ['%"username":"' . $username . '"%', LOGIN_LOCKOUT_TIME]
        );
        
        return $attempts['count'] ?? 0;
    } catch (Exception $e) {
        logError("خطأ في فحص محاولات تسجيل الدخول: " . $e->getMessage());
        return 0;
    }
}

/**
 * تسجيل محاولة دخول فاشلة
 */
function logFailedLogin($username, $reason = 'invalid_credentials') {
    try {
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? '';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
        
        execute(
            "INSERT INTO user_logs (user_id, action, new_data, ip_address, user_agent) 
             VALUES (0, 'failed_login', ?, ?, ?)",
            [
                json_encode(['username' => $username, 'reason' => $reason], JSON_UNESCAPED_UNICODE),
                $ip_address,
                $user_agent
            ]
        );
    } catch (Exception $e) {
        logError("خطأ في تسجيل محاولة الدخول الفاشلة: " . $e->getMessage());
    }
}

/**
 * إنشاء رمز CSRF
 */
function generateCSRFToken() {
    if (!isset($_SESSION['csrf_token'])) {
        $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
    }
    return $_SESSION['csrf_token'];
}

/**
 * فحص رمز CSRF
 */
function verifyCSRFToken($token) {
    return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
}

/**
 * إنشاء حقل CSRF مخفي
 */
function csrfField() {
    $token = generateCSRFToken();
    return "<input type='hidden' name='csrf_token' value='$token'>";
}

/**
 * فحص رمز CSRF من POST
 */
function checkCSRF() {
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $token = $_POST['csrf_token'] ?? '';
        if (!verifyCSRFToken($token)) {
            die('خطأ في التحقق من الأمان');
        }
    }
}
?>
