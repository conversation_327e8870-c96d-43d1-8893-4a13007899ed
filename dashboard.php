<?php
/**
 * لوحة التحكم الرئيسية
 * Main Dashboard
 */

require_once __DIR__ . '/config/config.php';
require_once __DIR__ . '/auth/check_auth.php';
require_once __DIR__ . '/includes/functions.php';

checkLogin();

$page_title = 'لوحة التحكم';
$current_user = getCurrentUser();
$company_settings = getCompanySettings();

// إحصائيات سريعة
try {
    // إجمالي المبيعات اليوم
    $today_sales = queryOne(
        "SELECT COALESCE(SUM(total_amount), 0) as total 
         FROM invoices 
         WHERE invoice_type = 'sales' 
         AND DATE(created_at) = CURDATE() 
         AND is_posted = 1"
    )['total'] ?? 0;
    
    // إجمالي المشتريات اليوم
    $today_purchases = queryOne(
        "SELECT COALESCE(SUM(total_amount), 0) as total 
         FROM invoices 
         WHERE invoice_type = 'purchase' 
         AND DATE(created_at) = CURDATE() 
         AND is_posted = 1"
    )['total'] ?? 0;
    
    // رصيد الصناديق
    $cash_balance = queryOne(
        "SELECT COALESCE(SUM(current_balance), 0) as total 
         FROM cash_banks 
         WHERE type = 'cash' 
         AND is_active = 1"
    )['total'] ?? 0;
    
    // رصيد البنوك
    $bank_balance = queryOne(
        "SELECT COALESCE(SUM(current_balance), 0) as total 
         FROM cash_banks 
         WHERE type = 'bank' 
         AND is_active = 1"
    )['total'] ?? 0;
    
    // عدد العملاء
    $customers_count = queryOne(
        "SELECT COUNT(*) as count 
         FROM customers 
         WHERE is_active = 1"
    )['count'] ?? 0;
    
    // عدد الموردين
    $suppliers_count = queryOne(
        "SELECT COUNT(*) as count 
         FROM suppliers 
         WHERE is_active = 1"
    )['count'] ?? 0;
    
    // عدد الأصناف
    $items_count = queryOne(
        "SELECT COUNT(*) as count 
         FROM items 
         WHERE is_active = 1"
    )['count'] ?? 0;
    
    // عدد الموظفين
    $employees_count = queryOne(
        "SELECT COUNT(*) as count 
         FROM employees 
         WHERE is_active = 1"
    )['count'] ?? 0;
    
    // آخر الفواتير
    $recent_invoices = query(
        "SELECT i.*, 
                CASE 
                    WHEN i.customer_id IS NOT NULL THEN c.customer_name
                    WHEN i.supplier_id IS NOT NULL THEN s.supplier_name
                    ELSE 'غير محدد'
                END as party_name
         FROM invoices i
         LEFT JOIN customers c ON i.customer_id = c.id
         LEFT JOIN suppliers s ON i.supplier_id = s.id
         ORDER BY i.created_at DESC
         LIMIT 5"
    );
    
    // الأصناف منخفضة المخزون
    $low_stock_items = query(
        "SELECT i.item_name, i.current_stock, i.min_stock, u.unit_symbol
         FROM items i
         LEFT JOIN units u ON i.main_unit_id = u.id
         WHERE i.is_active = 1 
         AND i.current_stock <= i.min_stock 
         AND i.min_stock > 0
         ORDER BY (i.current_stock / i.min_stock) ASC
         LIMIT 10"
    );
    
} catch (Exception $e) {
    logError("خطأ في تحميل بيانات لوحة التحكم: " . $e->getMessage());
    $today_sales = $today_purchases = $cash_balance = $bank_balance = 0;
    $customers_count = $suppliers_count = $items_count = $employees_count = 0;
    $recent_invoices = $low_stock_items = [];
}

include __DIR__ . '/includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-speedometer2 text-primary me-2"></i>
        لوحة التحكم
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">
                <i class="bi bi-arrow-clockwise me-1"></i>
                تحديث
            </button>
        </div>
    </div>
</div>

<!-- رسالة ترحيب -->
<div class="alert alert-info border-0 mb-4" style="background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);">
    <div class="d-flex align-items-center">
        <i class="bi bi-person-circle fs-2 text-primary me-3"></i>
        <div>
            <h5 class="alert-heading mb-1">مرحباً بك، <?php echo htmlspecialchars($current_user['full_name']); ?>!</h5>
            <p class="mb-0">اليوم هو <?php echo date('l، d F Y'); ?> - نتمنى لك يوماً مثمراً في العمل</p>
        </div>
    </div>
</div>

<!-- الإحصائيات السريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="border-right-color: #28a745;">
            <div class="icon text-success">
                <i class="bi bi-graph-up-arrow"></i>
            </div>
            <div class="number text-success"><?php echo formatMoney($today_sales); ?></div>
            <div class="label">مبيعات اليوم</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="border-right-color: #dc3545;">
            <div class="icon text-danger">
                <i class="bi bi-graph-down-arrow"></i>
            </div>
            <div class="number text-danger"><?php echo formatMoney($today_purchases); ?></div>
            <div class="label">مشتريات اليوم</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="border-right-color: #ffc107;">
            <div class="icon text-warning">
                <i class="bi bi-cash-stack"></i>
            </div>
            <div class="number text-warning"><?php echo formatMoney($cash_balance); ?></div>
            <div class="label">رصيد الصناديق</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="stats-card" style="border-right-color: #17a2b8;">
            <div class="icon text-info">
                <i class="bi bi-bank"></i>
            </div>
            <div class="number text-info"><?php echo formatMoney($bank_balance); ?></div>
            <div class="label">رصيد البنوك</div>
        </div>
    </div>
</div>

<!-- إحصائيات إضافية -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="feature-card">
            <div class="icon">
                <i class="bi bi-people"></i>
            </div>
            <div class="title"><?php echo number_format($customers_count); ?></div>
            <div class="description">العملاء</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="feature-card">
            <div class="icon">
                <i class="bi bi-truck"></i>
            </div>
            <div class="title"><?php echo number_format($suppliers_count); ?></div>
            <div class="description">الموردين</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="feature-card">
            <div class="icon">
                <i class="bi bi-box-seam"></i>
            </div>
            <div class="title"><?php echo number_format($items_count); ?></div>
            <div class="description">الأصناف</div>
        </div>
    </div>
    
    <div class="col-xl-3 col-md-6 mb-3">
        <div class="feature-card">
            <div class="icon">
                <i class="bi bi-person-badge"></i>
            </div>
            <div class="title"><?php echo number_format($employees_count); ?></div>
            <div class="description">الموظفين</div>
        </div>
    </div>
</div>

<div class="row">
    <!-- آخر الفواتير -->
    <div class="col-lg-8 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-receipt me-2"></i>
                    آخر الفواتير
                </h5>
                <a href="<?php echo BASE_URL; ?>/modules/invoices/index.php" class="btn btn-sm btn-outline-primary">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($recent_invoices)): ?>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>رقم الفاتورة</th>
                                    <th>النوع</th>
                                    <th>الطرف</th>
                                    <th>المبلغ</th>
                                    <th>التاريخ</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($recent_invoices as $invoice): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($invoice['invoice_number']); ?></strong>
                                        </td>
                                        <td>
                                            <?php
                                            $type_class = '';
                                            $type_text = '';
                                            switch ($invoice['invoice_type']) {
                                                case 'sales':
                                                    $type_class = 'success';
                                                    $type_text = 'مبيعات';
                                                    break;
                                                case 'purchase':
                                                    $type_class = 'primary';
                                                    $type_text = 'مشتريات';
                                                    break;
                                                case 'sales_return':
                                                    $type_class = 'warning';
                                                    $type_text = 'مرتجع مبيعات';
                                                    break;
                                                case 'purchase_return':
                                                    $type_class = 'info';
                                                    $type_text = 'مرتجع مشتريات';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $type_class; ?>"><?php echo $type_text; ?></span>
                                        </td>
                                        <td><?php echo htmlspecialchars($invoice['party_name']); ?></td>
                                        <td><?php echo formatMoney($invoice['total_amount']); ?></td>
                                        <td><?php echo formatDate($invoice['invoice_date']); ?></td>
                                        <td>
                                            <?php
                                            $status_class = '';
                                            $status_text = '';
                                            switch ($invoice['payment_status']) {
                                                case 'paid':
                                                    $status_class = 'success';
                                                    $status_text = 'مدفوع';
                                                    break;
                                                case 'partial':
                                                    $status_class = 'warning';
                                                    $status_text = 'جزئي';
                                                    break;
                                                case 'pending':
                                                    $status_class = 'danger';
                                                    $status_text = 'معلق';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge bg-<?php echo $status_class; ?>"><?php echo $status_text; ?></span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-receipt fs-1 text-muted"></i>
                        <p class="text-muted mt-2">لا توجد فواتير حتى الآن</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- الأصناف منخفضة المخزون -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-exclamation-triangle text-warning me-2"></i>
                    تنبيهات المخزون
                </h5>
                <a href="<?php echo BASE_URL; ?>/modules/inventory/index.php" class="btn btn-sm btn-outline-warning">
                    عرض الكل
                </a>
            </div>
            <div class="card-body">
                <?php if (!empty($low_stock_items)): ?>
                    <div class="list-group list-group-flush">
                        <?php foreach ($low_stock_items as $item): ?>
                            <div class="list-group-item d-flex justify-content-between align-items-center px-0">
                                <div>
                                    <h6 class="mb-1"><?php echo htmlspecialchars($item['item_name']); ?></h6>
                                    <small class="text-muted">
                                        الحد الأدنى: <?php echo number_format($item['min_stock'], 3); ?> <?php echo htmlspecialchars($item['unit_symbol']); ?>
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-danger">
                                        <?php echo number_format($item['current_stock'], 3); ?> <?php echo htmlspecialchars($item['unit_symbol']); ?>
                                    </span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-4">
                        <i class="bi bi-check-circle fs-1 text-success"></i>
                        <p class="text-muted mt-2">جميع الأصناف في المستوى الطبيعي</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- روابط سريعة -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning me-2"></i>
                    روابط سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <?php if (hasPermission('invoices')): ?>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?php echo BASE_URL; ?>/modules/invoices/create.php?type=sales" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-plus-circle fs-2 mb-2"></i>
                            <span>فاتورة مبيعات جديدة</span>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?php echo BASE_URL; ?>/modules/invoices/create.php?type=purchase" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-plus-circle fs-2 mb-2"></i>
                            <span>فاتورة مشتريات جديدة</span>
                        </a>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (hasPermission('vouchers')): ?>
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?php echo BASE_URL; ?>/modules/vouchers/create.php?type=receipt" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-arrow-down-circle fs-2 mb-2"></i>
                            <span>سند قبض جديد</span>
                        </a>
                    </div>
                    
                    <div class="col-lg-3 col-md-6 mb-3">
                        <a href="<?php echo BASE_URL; ?>/modules/vouchers/create.php?type=payment" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                            <i class="bi bi-arrow-up-circle fs-2 mb-2"></i>
                            <span>سند صرف جديد</span>
                        </a>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    // تحديث الوقت كل دقيقة
    setInterval(function() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('ar-SA');
        document.title = 'لوحة التحكم - ' + timeString + ' - " . htmlspecialchars($company_settings['company_name']) . "';
    }, 60000);
    
    // تأثيرات بصرية للبطاقات
    document.addEventListener('DOMContentLoaded', function() {
        const cards = document.querySelectorAll('.stats-card, .feature-card');
        
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });
";

include __DIR__ . '/includes/footer.php';
?>
