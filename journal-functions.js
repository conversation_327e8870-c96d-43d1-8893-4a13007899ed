// وظائف القيود المحاسبية الذكية

// بيانات الحسابات المحاسبية
let accounts = [
    { id: 1, code: '1001', name: 'النقدية', type: 'asset' },
    { id: 2, code: '4001', name: 'المبيعات', type: 'revenue' },
    { id: 3, code: '5001', name: 'خسائر التالف', type: 'expense' },
    { id: 4, code: '1301', name: 'المخزون', type: 'asset' },
    { id: 5, code: '2001', name: 'الموردون', type: 'liability' },
    { id: 6, code: '5002', name: 'تكلفة البضاعة المباعة', type: 'expense' },
    { id: 7, code: '1002', name: 'البنك', type: 'asset' },
    { id: 8, code: '2002', name: 'العملاء', type: 'asset' },
    { id: 9, code: '5003', name: 'مصاريف الرواتب', type: 'expense' },
    { id: 10, code: '2003', name: 'رواتب مستحقة', type: 'liability' }
];

// عرض القيود المحاسبية
function displayJournalEntries(dataToShow = journalEntries) {
    const container = document.getElementById('journalEntriesList');
    container.innerHTML = '';
    
    dataToShow.forEach(entry => {
        const entryDiv = document.createElement('div');
        entryDiv.className = `journal-entry ${entry.isBalanced ? 'entry-balanced' : 'entry-unbalanced'}`;
        
        const statusText = {
            'draft': 'مسودة',
            'pending': 'في انتظار المراجعة',
            'approved': 'معتمد',
            'rejected': 'مرفوض'
        }[entry.status];
        
        const statusClass = {
            'draft': 'secondary',
            'pending': 'warning',
            'approved': 'success',
            'rejected': 'danger'
        }[entry.status];
        
        const typeText = {
            'manual': 'يدوي',
            'smart': 'ذكي',
            'auto': 'تلقائي'
        }[entry.type];
        
        entryDiv.innerHTML = `
            <div class="journal-entry-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <strong>${entry.number}</strong>
                            ${entry.type === 'smart' ? '<span class="smart-entry-indicator ms-2">ذكي</span>' : ''}
                        </h6>
                        <small class="text-muted">${formatDate(entry.date)} | ${typeText}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${statusClass}">${statusText}</span>
                        <div class="mt-1">
                            <small class="text-muted">${entry.totalAmount.toLocaleString()} ر.ي</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <strong>البيان:</strong> ${entry.description}
                ${entry.reference ? `<br><small class="text-muted">المرجع: ${entry.reference}</small>` : ''}
            </div>
            
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>الحساب</th>
                            <th>مدين</th>
                            <th>دائن</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${entry.details.map(detail => `
                            <tr>
                                <td>${detail.accountName}</td>
                                <td class="${detail.debit > 0 ? 'debit-entry' : ''}">${detail.debit > 0 ? detail.debit.toLocaleString() : '-'}</td>
                                <td class="${detail.credit > 0 ? 'credit-entry' : ''}">${detail.credit > 0 ? detail.credit.toLocaleString() : '-'}</td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        منشئ: ${entry.createdBy}
                        ${entry.reviewedBy ? ` | مراجع: ${entry.reviewedBy}` : ''}
                    </small>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewEntryDetails(${entry.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    ${entry.status === 'draft' ? `
                        <button class="btn btn-sm btn-outline-primary" onclick="editEntry(${entry.id})" title="تعديل">
                            <i class="bi bi-pencil"></i>
                        </button>
                    ` : ''}
                    ${entry.status === 'pending' ? `
                        <button class="btn btn-sm btn-outline-success" onclick="reviewEntry(${entry.id})" title="مراجعة">
                            <i class="bi bi-check-circle"></i>
                        </button>
                    ` : ''}
                    <button class="btn btn-sm btn-outline-secondary" onclick="printEntry(${entry.id})" title="طباعة">
                        <i class="bi bi-printer"></i>
                    </button>
                </div>
            </div>
        `;
        
        container.appendChild(entryDiv);
    });
}

// تحديث الإحصائيات
function updateStats() {
    const stats = {
        totalEntries: journalEntries.length,
        smartEntries: journalEntries.filter(e => e.type === 'smart').length,
        pendingReview: journalEntries.filter(e => e.status === 'pending').length,
        unbalancedEntries: journalEntries.filter(e => !e.isBalanced).length
    };
    
    document.getElementById('totalEntries').textContent = stats.totalEntries;
    document.getElementById('smartEntries').textContent = stats.smartEntries;
    document.getElementById('pendingReview').textContent = stats.pendingReview;
    document.getElementById('unbalancedEntries').textContent = stats.unbalancedEntries;
}

// إظهار نافذة قيد جديد
function showJournalEntryModal() {
    document.getElementById('journalEntryForm').reset();
    document.getElementById('entryNumber').value = generateEntryNumber();
    document.getElementById('entryDate').value = new Date().toISOString().split('T')[0];
    populateAccountSelect();
    clearEntryDetails();
    const modal = new bootstrap.Modal(document.getElementById('journalEntryModal'));
    modal.show();
}

// إظهار نافذة القيد الذكي
function showSmartEntryModal() {
    document.getElementById('smartEntryForm').reset();
    document.getElementById('smartEntryDate').value = new Date().toISOString().split('T')[0];
    const modal = new bootstrap.Modal(document.getElementById('smartEntryModal'));
    modal.show();
}

// إظهار نافذة المراجعة
function showReviewModal() {
    const modal = new bootstrap.Modal(document.getElementById('reviewModal'));
    modal.show();
}

// إظهار نافذة سجل التغييرات
function showAuditLogModal() {
    loadAuditLog();
    const modal = new bootstrap.Modal(document.getElementById('auditLogModal'));
    modal.show();
}

// ملء قائمة الحسابات
function populateAccountSelect() {
    const select = document.getElementById('accountSelect');
    select.innerHTML = '<option value="">اختر الحساب</option>';
    
    accounts.forEach(account => {
        const option = document.createElement('option');
        option.value = account.id;
        option.textContent = `${account.code} - ${account.name}`;
        option.dataset.accountName = account.name;
        select.appendChild(option);
    });
}

// إضافة سطر للقيد
function addEntryLine() {
    const accountSelect = document.getElementById('accountSelect');
    const debitAmount = parseFloat(document.getElementById('debitAmount').value) || 0;
    const creditAmount = parseFloat(document.getElementById('creditAmount').value) || 0;
    
    if (!accountSelect.value || (debitAmount === 0 && creditAmount === 0)) {
        alert('يرجى اختيار الحساب وإدخال المبلغ');
        return;
    }
    
    if (debitAmount > 0 && creditAmount > 0) {
        alert('لا يمكن أن يكون الحساب مدين ودائن في نفس الوقت');
        return;
    }
    
    const selectedOption = accountSelect.options[accountSelect.selectedIndex];
    const accountName = selectedOption.dataset.accountName;
    
    const tbody = document.getElementById('entryDetailsTableBody');
    const row = document.createElement('tr');
    
    row.innerHTML = `
        <td>${accountName}</td>
        <td>${document.getElementById('entryDescription').value || 'بيان القيد'}</td>
        <td class="${debitAmount > 0 ? 'debit-entry' : ''}">${debitAmount > 0 ? debitAmount.toLocaleString() : '-'}</td>
        <td class="${creditAmount > 0 ? 'credit-entry' : ''}">${creditAmount > 0 ? creditAmount.toLocaleString() : '-'}</td>
        <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removeEntryLine(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;
    
    row.dataset.accountId = accountSelect.value;
    row.dataset.debit = debitAmount;
    row.dataset.credit = creditAmount;
    
    tbody.appendChild(row);
    
    // مسح الحقول
    document.getElementById('debitAmount').value = '';
    document.getElementById('creditAmount').value = '';
    accountSelect.value = '';
    
    updateBalance();
}

// حذف سطر من القيد
function removeEntryLine(button) {
    button.closest('tr').remove();
    updateBalance();
}

// تحديث التوازن
function updateBalance() {
    const rows = document.querySelectorAll('#entryDetailsTableBody tr');
    let totalDebit = 0;
    let totalCredit = 0;
    
    rows.forEach(row => {
        totalDebit += parseFloat(row.dataset.debit) || 0;
        totalCredit += parseFloat(row.dataset.credit) || 0;
    });
    
    document.getElementById('totalDebit').textContent = totalDebit.toLocaleString();
    document.getElementById('totalCredit').textContent = totalCredit.toLocaleString();
    
    const balanceIndicator = document.getElementById('balanceIndicator');
    const isBalanced = Math.abs(totalDebit - totalCredit) < 0.01;
    
    if (isBalanced && totalDebit > 0) {
        balanceIndicator.className = 'balance-indicator mb-3 balanced';
        balanceIndicator.innerHTML = '<i class="bi bi-check-circle me-2"></i>القيد متوازن';
    } else {
        balanceIndicator.className = 'balance-indicator mb-3 unbalanced';
        balanceIndicator.innerHTML = '<i class="bi bi-exclamation-triangle me-2"></i>القيد غير متوازن';
    }
}

// مسح تفاصيل القيد
function clearEntryDetails() {
    document.getElementById('entryDetailsTableBody').innerHTML = '';
    updateBalance();
}

// حفظ القيد
function saveJournalEntry() {
    const rows = document.querySelectorAll('#entryDetailsTableBody tr');
    if (rows.length === 0) {
        alert('يرجى إضافة تفاصيل للقيد');
        return;
    }
    
    const totalDebit = parseFloat(document.getElementById('totalDebit').textContent.replace(/,/g, ''));
    const totalCredit = parseFloat(document.getElementById('totalCredit').textContent.replace(/,/g, ''));
    
    if (Math.abs(totalDebit - totalCredit) >= 0.01) {
        alert('القيد غير متوازن. يجب أن يكون إجمالي المدين مساوياً لإجمالي الدائن');
        return;
    }
    
    const details = [];
    rows.forEach(row => {
        details.push({
            accountId: parseInt(row.dataset.accountId),
            accountName: row.cells[0].textContent,
            debit: parseFloat(row.dataset.debit) || 0,
            credit: parseFloat(row.dataset.credit) || 0
        });
    });
    
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    
    const newEntry = {
        id: journalEntries.length + 1,
        number: document.getElementById('entryNumber').value,
        date: document.getElementById('entryDate').value,
        type: document.getElementById('entryType').value,
        description: document.getElementById('entryDescription').value,
        reference: document.getElementById('entryReference').value,
        status: 'pending',
        createdBy: user.name,
        reviewedBy: null,
        isBalanced: true,
        totalAmount: totalDebit,
        details: details
    };
    
    journalEntries.push(newEntry);
    
    // إضافة سجل تدقيق
    addAuditLog('create', 'journal', newEntry.number, null, newEntry);
    
    displayJournalEntries();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('journalEntryModal'));
    modal.hide();
    
    alert('تم حفظ القيد وإرساله للمراجعة بنجاح!');
}

// حفظ كمسودة
function saveAsDraft() {
    // نفس منطق الحفظ ولكن بحالة مسودة
    const newEntry = {
        // ... نفس البيانات
        status: 'draft'
    };
    
    alert('تم حفظ القيد كمسودة!');
}

// تحديث حقول القيد الذكي
function updateSmartEntryFields() {
    const type = document.getElementById('smartEntryType').value;
    const container = document.getElementById('smartEntryFields');
    
    let fieldsHTML = '';
    
    switch (type) {
        case 'sales':
            fieldsHTML = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">العميل</label>
                        <input type="text" class="form-control" id="customerName" placeholder="اسم العميل">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">المبلغ *</label>
                        <input type="number" class="form-control" id="salesAmount" step="0.01" required>
                    </div>
                </div>
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="paymentMethod">
                            <option value="cash">نقدي</option>
                            <option value="bank">بنكي</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">رقم الفاتورة</label>
                        <input type="text" class="form-control" id="invoiceNumber">
                    </div>
                </div>
            `;
            break;
            
        case 'purchase':
            fieldsHTML = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">المورد</label>
                        <input type="text" class="form-control" id="supplierName" placeholder="اسم المورد">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">المبلغ *</label>
                        <input type="number" class="form-control" id="purchaseAmount" step="0.01" required>
                    </div>
                </div>
            `;
            break;
            
        case 'damage':
            fieldsHTML = `
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">المنتج التالف</label>
                        <input type="text" class="form-control" id="damagedProduct" placeholder="اسم المنتج">
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">قيمة التالف *</label>
                        <input type="number" class="form-control" id="damageValue" step="0.01" required>
                    </div>
                </div>
            `;
            break;
    }
    
    container.innerHTML = fieldsHTML;
}

// إنشاء قيد ذكي
function generateSmartEntry() {
    const type = document.getElementById('smartEntryType').value;
    const date = document.getElementById('smartEntryDate').value;
    const notes = document.getElementById('smartEntryNotes').value;
    
    if (!type || !date) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    let entryDetails = [];
    let description = '';
    let amount = 0;
    
    switch (type) {
        case 'sales':
            amount = parseFloat(document.getElementById('salesAmount').value);
            const paymentMethod = document.getElementById('paymentMethod').value;
            description = `قيد مبيعات - ${document.getElementById('customerName').value || 'عميل'}`;
            
            if (paymentMethod === 'cash') {
                entryDetails = [
                    { accountId: 1, accountName: 'النقدية', debit: amount, credit: 0 },
                    { accountId: 2, accountName: 'المبيعات', debit: 0, credit: amount }
                ];
            } else if (paymentMethod === 'credit') {
                entryDetails = [
                    { accountId: 8, accountName: 'العملاء', debit: amount, credit: 0 },
                    { accountId: 2, accountName: 'المبيعات', debit: 0, credit: amount }
                ];
            }
            break;
            
        case 'damage':
            amount = parseFloat(document.getElementById('damageValue').value);
            description = `قيد تالف منتجات - ${document.getElementById('damagedProduct').value || 'منتج'}`;
            entryDetails = [
                { accountId: 3, accountName: 'خسائر التالف', debit: amount, credit: 0 },
                { accountId: 4, accountName: 'المخزون', debit: 0, credit: amount }
            ];
            break;
    }
    
    if (entryDetails.length === 0) {
        alert('حدث خطأ في إنشاء القيد');
        return;
    }
    
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    
    const newEntry = {
        id: journalEntries.length + 1,
        number: generateEntryNumber(),
        date: date,
        type: 'smart',
        description: description,
        reference: document.getElementById('invoiceNumber')?.value || '',
        status: 'pending',
        createdBy: user.name,
        reviewedBy: null,
        isBalanced: true,
        totalAmount: amount,
        details: entryDetails
    };
    
    journalEntries.push(newEntry);
    
    // إضافة سجل تدقيق
    addAuditLog('create', 'journal', newEntry.number, null, newEntry);
    
    displayJournalEntries();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('smartEntryModal'));
    modal.hide();
    
    alert('تم إنشاء القيد الذكي بنجاح!');
}

// إنشاء رقم قيد جديد
function generateEntryNumber() {
    const year = new Date().getFullYear();
    const nextNumber = journalEntries.length + 1;
    return `JE${year}${String(nextNumber).padStart(4, '0')}`;
}

// إضافة سجل تدقيق
function addAuditLog(action, module, recordId, oldValues, newValues) {
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    
    auditLog.push({
        id: auditLog.length + 1,
        timestamp: new Date().toLocaleString('ar-SA'),
        userId: user.id,
        userName: user.name,
        module: module,
        action: action,
        recordId: recordId,
        oldValues: oldValues,
        newValues: newValues,
        ipAddress: '*************' // محاكاة
    });
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// البحث في القيود
function searchEntries() {
    const searchTerm = document.getElementById('entrySearchInput').value.toLowerCase();
    const dateFrom = document.getElementById('entryDateFrom').value;
    const dateTo = document.getElementById('entryDateTo').value;
    const typeFilter = document.getElementById('entryTypeFilter').value;
    const statusFilter = document.getElementById('entryStatusFilter').value;
    
    let filtered = journalEntries.filter(entry => {
        const matchesSearch = !searchTerm || 
            entry.description.toLowerCase().includes(searchTerm) ||
            entry.number.toLowerCase().includes(searchTerm) ||
            entry.reference.toLowerCase().includes(searchTerm);
        
        const matchesDate = (!dateFrom || entry.date >= dateFrom) && 
                           (!dateTo || entry.date <= dateTo);
        
        const matchesType = !typeFilter || entry.type === typeFilter;
        const matchesStatus = !statusFilter || entry.status === statusFilter;
        
        return matchesSearch && matchesDate && matchesType && matchesStatus;
    });
    
    displayJournalEntries(filtered);
}

// مسح فلاتر البحث
function clearEntryFilters() {
    document.getElementById('entrySearchInput').value = '';
    document.getElementById('entryDateFrom').value = '';
    document.getElementById('entryDateTo').value = '';
    document.getElementById('entryTypeFilter').value = '';
    document.getElementById('entryStatusFilter').value = '';
    displayJournalEntries();
}

// وظائف إضافية
function viewEntryDetails(entryId) {
    const entry = journalEntries.find(e => e.id === entryId);
    if (entry) {
        alert(`تفاصيل القيد ${entry.number}:
البيان: ${entry.description}
التاريخ: ${formatDate(entry.date)}
المبلغ: ${entry.totalAmount.toLocaleString()} ر.ي
الحالة: ${entry.status}
المنشئ: ${entry.createdBy}`);
    }
}

function editEntry(entryId) {
    alert('سيتم فتح نافذة التعديل قريباً');
}

function reviewEntry(entryId) {
    const entry = journalEntries.find(e => e.id === entryId);
    if (entry) {
        // ملء تفاصيل المراجعة
        document.getElementById('reviewDate').value = new Date().toISOString().split('T')[0];
        showReviewModal();
    }
}

function printEntry(entryId) {
    alert('سيتم فتح نافذة الطباعة قريباً');
}

function submitReview() {
    alert('تم تأكيد المراجعة!');
}

function loadAuditLog() {
    const tbody = document.getElementById('auditLogTableBody');
    tbody.innerHTML = '';
    
    auditLog.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${log.timestamp}</td>
            <td>${log.userName}</td>
            <td>${log.module}</td>
            <td>${log.action}</td>
            <td>${log.recordId}</td>
            <td>${log.oldValues ? JSON.stringify(log.oldValues) : '-'}</td>
            <td>${log.newValues ? JSON.stringify(log.newValues) : '-'}</td>
            <td>${log.ipAddress}</td>
        `;
        tbody.appendChild(row);
    });
}

function exportAuditLog() {
    alert('سيتم تصدير سجل التغييرات قريباً');
}

// وظائف إنشاء القيود التلقائية
function generateSalesEntries() {
    alert('سيتم إنشاء قيود المبيعات تلقائياً');
}

function generatePurchaseEntries() {
    alert('سيتم إنشاء قيود المشتريات تلقائياً');
}

function generateProductionEntries() {
    alert('سيتم إنشاء قيود الإنتاج تلقائياً');
}

function generatePayrollEntries() {
    alert('سيتم إنشاء قيود الرواتب تلقائياً');
}

function generateDamageEntries() {
    alert('سيتم إنشاء قيود التالف تلقائياً');
}
