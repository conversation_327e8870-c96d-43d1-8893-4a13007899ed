<?php
/**
 * إضافة حساب محاسبي جديد
 * Create New Account
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('accounts');

$page_title = 'إضافة حساب محاسبي جديد';
$error_message = '';
$success_message = '';

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    checkCSRF();
    
    try {
        $data = [
            'account_code' => cleanInput($_POST['account_code']),
            'account_name' => cleanInput($_POST['account_name']),
            'account_name_en' => cleanInput($_POST['account_name_en']),
            'parent_id' => !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null,
            'account_type' => cleanInput($_POST['account_type']),
            'account_nature' => cleanInput($_POST['account_nature']),
            'is_main' => isset($_POST['is_main']) ? 1 : 0,
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'opening_balance' => floatval($_POST['opening_balance']),
            'description' => cleanInput($_POST['description'])
        ];
        
        // التحقق من صحة البيانات
        if (empty($data['account_code'])) {
            throw new Exception('رمز الحساب مطلوب');
        }
        
        if (empty($data['account_name'])) {
            throw new Exception('اسم الحساب مطلوب');
        }
        
        if (empty($data['account_type'])) {
            throw new Exception('نوع الحساب مطلوب');
        }
        
        if (empty($data['account_nature'])) {
            throw new Exception('طبيعة الحساب مطلوبة');
        }
        
        // التحقق من عدم تكرار رمز الحساب
        $existingAccount = queryOne(
            "SELECT id FROM chart_of_accounts WHERE account_code = ?",
            [$data['account_code']]
        );
        
        if ($existingAccount) {
            throw new Exception('رمز الحساب موجود مسبقاً');
        }
        
        // تحديد المستوى
        $level = 1;
        if ($data['parent_id']) {
            $parentAccount = queryOne(
                "SELECT level FROM chart_of_accounts WHERE id = ?",
                [$data['parent_id']]
            );
            
            if (!$parentAccount) {
                throw new Exception('الحساب الأب غير موجود');
            }
            
            $level = $parentAccount['level'] + 1;
        }
        
        $data['level'] = $level;
        $data['current_balance'] = $data['opening_balance'];
        
        // إدراج الحساب الجديد
        $accountId = execute(
            "INSERT INTO chart_of_accounts (
                account_code, account_name, account_name_en, parent_id, account_type, 
                account_nature, level, is_main, is_active, opening_balance, current_balance, 
                description, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",
            [
                $data['account_code'], $data['account_name'], $data['account_name_en'],
                $data['parent_id'], $data['account_type'], $data['account_nature'],
                $data['level'], $data['is_main'], $data['is_active'],
                $data['opening_balance'], $data['current_balance'], $data['description'],
                $_SESSION['user_id']
            ]
        );
        
        $accountId = getDB()->lastInsertId();
        
        // إنشاء قيد افتتاحي إذا كان هناك رصيد
        if ($data['opening_balance'] != 0) {
            $entryData = [
                'entry_date' => getCurrentDate(),
                'reference_type' => 'opening_balance',
                'reference_id' => $accountId,
                'description' => 'رصيد افتتاحي للحساب: ' . $data['account_name'],
                'details' => []
            ];
            
            if ($data['account_nature'] === 'debit') {
                // حساب مدين
                $entryData['details'][] = [
                    'account_id' => $accountId,
                    'debit_amount' => abs($data['opening_balance']),
                    'credit_amount' => 0,
                    'description' => 'رصيد افتتاحي'
                ];
                
                // الطرف المقابل (حساب رأس المال أو الأرباح المحتجزة)
                $equityAccount = queryOne(
                    "SELECT id FROM chart_of_accounts WHERE account_code = ? LIMIT 1",
                    [DEFAULT_ACCOUNTS['RETAINED_EARNINGS']]
                );
                
                if ($equityAccount) {
                    $entryData['details'][] = [
                        'account_id' => $equityAccount['id'],
                        'debit_amount' => 0,
                        'credit_amount' => abs($data['opening_balance']),
                        'description' => 'مقابل رصيد افتتاحي'
                    ];
                }
            } else {
                // حساب دائن
                $entryData['details'][] = [
                    'account_id' => $accountId,
                    'debit_amount' => 0,
                    'credit_amount' => abs($data['opening_balance']),
                    'description' => 'رصيد افتتاحي'
                ];
                
                // الطرف المقابل
                $equityAccount = queryOne(
                    "SELECT id FROM chart_of_accounts WHERE account_code = ? LIMIT 1",
                    [DEFAULT_ACCOUNTS['RETAINED_EARNINGS']]
                );
                
                if ($equityAccount) {
                    $entryData['details'][] = [
                        'account_id' => $equityAccount['id'],
                        'debit_amount' => abs($data['opening_balance']),
                        'credit_amount' => 0,
                        'description' => 'مقابل رصيد افتتاحي'
                    ];
                }
            }
            
            $entryData['total_debit'] = abs($data['opening_balance']);
            $entryData['total_credit'] = abs($data['opening_balance']);
            
            createJournalEntry($entryData);
        }
        
        // تسجيل النشاط
        logUserActivity('create', 'chart_of_accounts', $accountId, null, $data);
        
        $success_message = 'تم إضافة الحساب بنجاح';
        
        // إعادة توجيه بعد النجاح
        header('Location: index.php?success=1');
        exit();
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        logError("خطأ في إضافة الحساب: " . $e->getMessage());
    }
}

// الحصول على الحسابات الرئيسية للاختيار كحساب أب
$parentAccounts = getAccounts();

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-plus-circle text-primary me-2"></i>
        إضافة حساب محاسبي جديد
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
<?php endif; ?>

<?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-plus me-2"></i>
                    بيانات الحساب الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="account-form">
                    <?php echo csrfField(); ?>
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="account_code" class="form-label">رمز الحساب *</label>
                                <input type="text" class="form-control" id="account_code" name="account_code" 
                                       value="<?php echo htmlspecialchars($_POST['account_code'] ?? ''); ?>" required>
                                <div class="form-text">رمز فريد للحساب (مثال: 1111)</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_name" class="form-label">اسم الحساب *</label>
                                <input type="text" class="form-control" id="account_name" name="account_name" 
                                       value="<?php echo htmlspecialchars($_POST['account_name'] ?? ''); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_name_en" class="form-label">اسم الحساب بالإنجليزية</label>
                                <input type="text" class="form-control" id="account_name_en" name="account_name_en" 
                                       value="<?php echo htmlspecialchars($_POST['account_name_en'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">الحساب الأب</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">اختر الحساب الأب (اختياري)</option>
                                    <?php foreach ($parentAccounts as $account): ?>
                                        <option value="<?php echo $account['id']; ?>" 
                                                <?php echo (isset($_POST['parent_id']) && $_POST['parent_id'] == $account['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($account['account_code'] . ' - ' . $account['account_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="description" class="form-label">الوصف</label>
                                <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($_POST['description'] ?? ''); ?></textarea>
                            </div>
                        </div>
                        
                        <!-- الإعدادات المحاسبية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-calculator me-2"></i>
                                الإعدادات المحاسبية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="account_type" class="form-label">نوع الحساب *</label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <?php foreach (ACCOUNT_TYPES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo (isset($_POST['account_type']) && $_POST['account_type'] == $key) ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_nature" class="form-label">طبيعة الحساب *</label>
                                <select class="form-select" id="account_nature" name="account_nature" required>
                                    <option value="">اختر طبيعة الحساب</option>
                                    <?php foreach (ACCOUNT_NATURES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo (isset($_POST['account_nature']) && $_POST['account_nature'] == $key) ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <strong>مدين:</strong> الأصول والمصروفات<br>
                                        <strong>دائن:</strong> الخصوم وحقوق الملكية والإيرادات
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="opening_balance" class="form-label">الرصيد الافتتاحي</label>
                                <input type="number" class="form-control" id="opening_balance" name="opening_balance" 
                                       step="0.001" value="<?php echo $_POST['opening_balance'] ?? '0.000'; ?>">
                                <div class="form-text">سيتم إنشاء قيد محاسبي تلقائياً للرصيد الافتتاحي</div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_main" name="is_main" 
                                           <?php echo (isset($_POST['is_main']) && $_POST['is_main']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_main">
                                        حساب رئيسي
                                    </label>
                                    <div class="form-text">الحسابات الرئيسية لا يمكن حذفها</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?php echo (!isset($_POST['is_active']) || $_POST['is_active']) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        حساب نشط
                                    </label>
                                </div>
                            </div>
                            
                            <!-- معاينة المستوى -->
                            <div class="alert alert-info" id="level-preview" style="display: none;">
                                <i class="bi bi-info-circle me-2"></i>
                                <span id="level-text"></span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="index.php" class="btn btn-secondary">
                                    <i class="bi bi-x-lg me-1"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-1"></i>
                                    حفظ الحساب
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    // تهيئة النموذج
    document.addEventListener('DOMContentLoaded', function() {
        const accountTypeSelect = document.getElementById('account_type');
        const accountNatureSelect = document.getElementById('account_nature');
        const parentSelect = document.getElementById('parent_id');
        const levelPreview = document.getElementById('level-preview');
        const levelText = document.getElementById('level-text');
        
        // تحديد طبيعة الحساب تلقائياً حسب النوع
        accountTypeSelect.addEventListener('change', function() {
            const type = this.value;
            
            if (type === 'asset' || type === 'expense') {
                accountNatureSelect.value = 'debit';
            } else if (type === 'liability' || type === 'equity' || type === 'revenue') {
                accountNatureSelect.value = 'credit';
            }
        });
        
        // عرض معاينة المستوى
        parentSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            
            if (this.value) {
                const parentText = selectedOption.text;
                levelText.textContent = 'سيكون هذا حساب فرعي تحت: ' + parentText;
                levelPreview.style.display = 'block';
            } else {
                levelPreview.style.display = 'none';
            }
        });
        
        // التحقق من صحة النموذج
        document.getElementById('account-form').addEventListener('submit', function(e) {
            const accountCode = document.getElementById('account_code').value.trim();
            const accountName = document.getElementById('account_name').value.trim();
            const accountType = document.getElementById('account_type').value;
            const accountNature = document.getElementById('account_nature').value;
            
            if (!accountCode || !accountName || !accountType || !accountNature) {
                e.preventDefault();
                showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            showLoading(true);
        });
    });
";

include __DIR__ . '/../../includes/footer.php';
?>
