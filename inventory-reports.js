// وظائف التقارير والتهيئة

// إنشاء تقرير الأصناف الأكثر حركة
function generateTopMovingItems() {
    const container = document.getElementById('topMovingItems');
    container.innerHTML = '';
    
    // حساب حركة كل صنف
    const itemMovements = {};
    movements.forEach(movement => {
        if (!itemMovements[movement.itemId]) {
            itemMovements[movement.itemId] = {
                itemName: movement.itemName,
                totalMovement: 0
            };
        }
        itemMovements[movement.itemId].totalMovement += movement.quantity;
    });
    
    // ترتيب الأصناف حسب الحركة
    const sortedItems = Object.values(itemMovements)
        .sort((a, b) => b.totalMovement - a.totalMovement)
        .slice(0, 5);
    
    sortedItems.forEach((item, index) => {
        const listItem = document.createElement('div');
        listItem.className = 'list-group-item d-flex justify-content-between align-items-center';
        listItem.innerHTML = `
            <div>
                <strong>${item.itemName}</strong>
                <br><small class="text-muted">المرتبة ${index + 1}</small>
            </div>
            <span class="badge bg-primary rounded-pill">${item.totalMovement.toLocaleString()}</span>
        `;
        container.appendChild(listItem);
    });
}

// إنشاء تقرير قيمة المخزون
function generateInventoryValueReport() {
    const tbody = document.getElementById('inventoryValueTableBody');
    tbody.innerHTML = '';
    
    const categoryStats = {};
    
    items.forEach(item => {
        if (!categoryStats[item.category]) {
            categoryStats[item.category] = {
                itemCount: 0,
                totalQuantity: 0,
                totalValue: 0,
                avgPrice: 0
            };
        }
        
        const totalStock = Object.values(item.warehouseStocks).reduce((sum, stock) => sum + stock, 0);
        const itemValue = totalStock * item.costPrice;
        
        categoryStats[item.category].itemCount++;
        categoryStats[item.category].totalQuantity += totalStock;
        categoryStats[item.category].totalValue += itemValue;
    });
    
    // حساب متوسط السعر
    Object.keys(categoryStats).forEach(category => {
        const stats = categoryStats[category];
        stats.avgPrice = stats.totalQuantity > 0 ? stats.totalValue / stats.totalQuantity : 0;
    });
    
    const categoryNames = {
        'flour': 'دقيق ومواد أساسية',
        'ingredients': 'مكونات',
        'packaging': 'مواد تعبئة',
        'tools': 'أدوات'
    };
    
    Object.keys(categoryStats).forEach(category => {
        const stats = categoryStats[category];
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${categoryNames[category] || category}</td>
            <td>${stats.itemCount}</td>
            <td>${stats.totalQuantity.toLocaleString()}</td>
            <td>${stats.avgPrice.toLocaleString()} ر.ي</td>
            <td><strong>${stats.totalValue.toLocaleString()} ر.ي</strong></td>
        `;
        
        tbody.appendChild(row);
    });
    
    // إضافة صف الإجمالي
    const totalRow = document.createElement('tr');
    totalRow.className = 'table-primary';
    
    const grandTotals = Object.values(categoryStats).reduce((totals, stats) => {
        totals.itemCount += stats.itemCount;
        totals.totalQuantity += stats.totalQuantity;
        totals.totalValue += stats.totalValue;
        return totals;
    }, { itemCount: 0, totalQuantity: 0, totalValue: 0 });
    
    const avgPrice = grandTotals.totalQuantity > 0 ? grandTotals.totalValue / grandTotals.totalQuantity : 0;
    
    totalRow.innerHTML = `
        <td><strong>الإجمالي</strong></td>
        <td><strong>${grandTotals.itemCount}</strong></td>
        <td><strong>${grandTotals.totalQuantity.toLocaleString()}</strong></td>
        <td><strong>${avgPrice.toLocaleString()} ر.ي</strong></td>
        <td><strong>${grandTotals.totalValue.toLocaleString()} ر.ي</strong></td>
    `;
    
    tbody.appendChild(totalRow);
}

// إنشاء مخطط الفئات (محاكاة)
function generateCategoryChart() {
    const canvas = document.getElementById('categoryChart');
    const ctx = canvas.getContext('2d');
    
    // مسح الرسم السابق
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    // حساب إحصائيات الفئات
    const categoryData = {};
    items.forEach(item => {
        if (!categoryData[item.category]) {
            categoryData[item.category] = 0;
        }
        const totalStock = Object.values(item.warehouseStocks).reduce((sum, stock) => sum + stock, 0);
        categoryData[item.category] += totalStock;
    });
    
    const categories = Object.keys(categoryData);
    const values = Object.values(categoryData);
    const maxValue = Math.max(...values);
    
    const colors = ['#667eea', '#764ba2', '#f093fb', '#f5576c'];
    const categoryNames = {
        'flour': 'دقيق',
        'ingredients': 'مكونات',
        'packaging': 'تعبئة',
        'tools': 'أدوات'
    };
    
    // رسم الأعمدة
    const barWidth = canvas.width / categories.length - 20;
    const barMaxHeight = canvas.height - 60;
    
    categories.forEach((category, index) => {
        const value = values[index];
        const barHeight = (value / maxValue) * barMaxHeight;
        const x = index * (barWidth + 20) + 10;
        const y = canvas.height - barHeight - 30;
        
        // رسم العمود
        ctx.fillStyle = colors[index % colors.length];
        ctx.fillRect(x, y, barWidth, barHeight);
        
        // كتابة القيمة
        ctx.fillStyle = '#333';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.fillText(value.toLocaleString(), x + barWidth/2, y - 5);
        
        // كتابة اسم الفئة
        ctx.fillText(categoryNames[category] || category, x + barWidth/2, canvas.height - 10);
    });
}

// تهيئة التقارير
function initializeReports() {
    generateTopMovingItems();
    generateInventoryValueReport();
    generateCategoryChart();
}

// تهيئة الصفحة
function initializePage() {
    // التحقق من المصادقة
    const user = checkAuth();
    if (user) {
        document.getElementById('sidebarUserName').textContent = user.name;
    }
    
    // استعادة حالة القائمة الجانبية
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        document.getElementById('sidebar').classList.add('collapsed');
        document.getElementById('mainContent').classList.add('expanded');
    }
    
    // عرض البيانات
    displayItems();
    displayWarehouses();
    displayMovements();
    updateStats();
    initializeReports();
    
    // تعيين التواريخ الافتراضية
    const today = new Date().toISOString().split('T')[0];
    const lastWeek = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
    
    document.getElementById('movementDateFrom').value = lastWeek;
    document.getElementById('movementDateTo').value = today;
    
    // ربط أحداث الفلترة
    document.getElementById('searchInput').addEventListener('input', filterItems);
    document.getElementById('categoryFilter').addEventListener('change', filterItems);
    document.getElementById('stockFilter').addEventListener('change', filterItems);
    
    // تأثيرات بصرية
    const cards = document.querySelectorAll('.stats-card, .card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// تحديث التقارير عند تغيير التبويب
function updateReportsOnTabChange() {
    const reportsTab = document.getElementById('reports-tab');
    reportsTab.addEventListener('shown.bs.tab', function() {
        initializeReports();
    });
}

// إضافة المزيد من الأصناف التجريبية
function addMoreSampleItems() {
    const additionalItems = [
        {
            id: 7,
            code: 'ITEM007',
            name: 'بيض طازج',
            category: 'ingredients',
            unit: 'piece',
            description: 'بيض طازج للخبز',
            minStock: 100,
            maxStock: 1000,
            reorderPoint: 200,
            costPrice: 15,
            sellingPrice: 20,
            isActive: true,
            warehouseStocks: { 1: 300, 2: 200, 3: 0, 4: 0 }
        },
        {
            id: 8,
            code: 'ITEM008',
            name: 'حليب طازج',
            category: 'ingredients',
            unit: 'l',
            description: 'حليب طازج للخبز',
            minStock: 20,
            maxStock: 200,
            reorderPoint: 50,
            costPrice: 1500,
            sellingPrice: 2000,
            isActive: true,
            warehouseStocks: { 1: 40, 2: 30, 3: 0, 4: 0 }
        },
        {
            id: 9,
            code: 'ITEM009',
            name: 'زبدة طبيعية',
            category: 'ingredients',
            unit: 'kg',
            description: 'زبدة طبيعية عالية الجودة',
            minStock: 5,
            maxStock: 50,
            reorderPoint: 15,
            costPrice: 8000,
            sellingPrice: 10000,
            isActive: true,
            warehouseStocks: { 1: 12, 2: 8, 3: 0, 4: 0 }
        },
        {
            id: 10,
            code: 'ITEM010',
            name: 'صناديق كرتون',
            category: 'packaging',
            unit: 'piece',
            description: 'صناديق كرتون للتوصيل',
            minStock: 100,
            maxStock: 1000,
            reorderPoint: 200,
            costPrice: 50,
            sellingPrice: 75,
            isActive: true,
            warehouseStocks: { 1: 0, 2: 0, 3: 300, 4: 0 }
        }
    ];
    
    items.push(...additionalItems);
}

// إضافة المزيد من الحركات التجريبية
function addMoreSampleMovements() {
    const additionalMovements = [
        {
            id: 4,
            itemId: 4,
            itemName: 'أكياس ورقية',
            type: 'out',
            warehouseId: 3,
            warehouseName: 'مخزن التعبئة',
            quantity: 500,
            date: '2024-01-12',
            reference: 'USE-001',
            user: 'سارة المشرفة',
            notes: 'استخدام في التعبئة'
        },
        {
            id: 5,
            itemId: 5,
            itemName: 'ملح طعام',
            type: 'transfer',
            warehouseId: 1,
            warehouseName: 'المخزن الرئيسي',
            quantity: 10,
            date: '2024-01-11',
            reference: 'TR-002',
            user: 'فاطمة المخزنية',
            notes: 'تحويل لمخزن المكونات'
        },
        {
            id: 6,
            itemId: 6,
            itemName: 'زيت نباتي',
            type: 'adjustment',
            warehouseId: 2,
            warehouseName: 'مخزن المكونات',
            quantity: 2,
            date: '2024-01-10',
            reference: 'ADJ-001',
            user: 'أحمد المحاسب',
            notes: 'تسوية جرد - نقص'
        }
    ];
    
    movements.push(...additionalMovements);
}

// تهيئة البيانات التجريبية الإضافية
function initializeAdditionalData() {
    addMoreSampleItems();
    addMoreSampleMovements();
}

// تشغيل التهيئة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeAdditionalData();
    initializePage();
    updateReportsOnTabChange();
});
