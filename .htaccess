# ===================================================
# ملف .htaccess لنظام محاسبة المخبز
# .htaccess file for Bakery Accounting System
# ===================================================

# تفعيل محرك إعادة الكتابة
RewriteEngine On

# منع الوصول إلى ملفات النظام الحساسة
<Files "*.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".env">
    Order Allow,Deny
    Deny from all
</Files>

<Files "config.php">
    Order Allow,Deny
    Deny from all
</Files>

# منع الوصول إلى مجلدات النظام
RedirectMatch 403 ^/config/
RedirectMatch 403 ^/includes/
RedirectMatch 403 ^/logs/
RedirectMatch 403 ^/backups/

# إعادة توجيه الصفحة الرئيسية
RewriteRule ^$ index.html [L]

# إعادة توجيه للوحة التحكم بعد تسجيل الدخول
RewriteRule ^dashboard$ dashboard.php [L]
RewriteRule ^admin$ dashboard.php [L]

# إعادة توجيه لصفحة تسجيل الدخول
RewriteRule ^login$ auth/login.php [L]
RewriteRule ^logout$ auth/logout.php [L]

# إعادة توجيه للوحدات
RewriteRule ^company$ modules/company/settings.php [L]
RewriteRule ^accounts$ modules/accounts/index.php [L]
RewriteRule ^users$ modules/users/index.php [L]
RewriteRule ^cash-banks$ modules/cash_banks/index.php [L]
RewriteRule ^employees$ modules/employees/index.php [L]
RewriteRule ^inventory$ modules/inventory/index.php [L]
RewriteRule ^invoices$ modules/invoices/index.php [L]
RewriteRule ^vouchers$ modules/vouchers/index.php [L]
RewriteRule ^assets$ modules/assets/index.php [L]
RewriteRule ^reports$ modules/reports/index.php [L]

# إعدادات الأمان
<IfModule mod_headers.c>
    # منع تضمين الموقع في إطارات خارجية
    Header always append X-Frame-Options SAMEORIGIN
    
    # منع تشغيل السكريبت في المتصفح
    Header set X-XSS-Protection "1; mode=block"
    
    # منع تخمين نوع المحتوى
    Header set X-Content-Type-Options nosniff
    
    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    
    # إعدادات HTTPS (للبيئة الإنتاجية)
    # Header always set Strict-Transport-Security "max-age=********; includeSubDomains"
</IfModule>

# ضغط الملفات لتحسين الأداء
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# إعدادات التخزين المؤقت
<IfModule mod_expires.c>
    ExpiresActive On
    
    # الصور
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/webp "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    
    # CSS و JavaScript
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    
    # الخطوط
    ExpiresByType font/woff "access plus 1 year"
    ExpiresByType font/woff2 "access plus 1 year"
    ExpiresByType application/font-woff "access plus 1 year"
    ExpiresByType application/font-woff2 "access plus 1 year"
    
    # الملفات الأخرى
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    
    # الافتراضي
    ExpiresDefault "access plus 2 days"
</IfModule>

# إعدادات PHP
<IfModule mod_php7.c>
    # زيادة حد الذاكرة
    php_value memory_limit 256M
    
    # زيادة حد رفع الملفات
    php_value upload_max_filesize 10M
    php_value post_max_size 10M
    
    # زيادة وقت التنفيذ
    php_value max_execution_time 300
    
    # تفعيل عرض الأخطاء (للتطوير فقط)
    # php_flag display_errors On
    # php_flag display_startup_errors On
    
    # إخفاء الأخطاء (للإنتاج)
    php_flag display_errors Off
    php_flag display_startup_errors Off
    
    # تسجيل الأخطاء
    php_flag log_errors On
    php_value error_log logs/php_errors.log
    
    # إعدادات الجلسة
    php_value session.gc_maxlifetime 3600
    php_value session.cookie_lifetime 0
    php_value session.cookie_httponly 1
    php_value session.use_strict_mode 1
</IfModule>

# منع الوصول إلى ملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|tmp|temp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات Git
<FilesMatch "^\.git">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# منع الوصول إلى ملفات النظام
<FilesMatch "^(README|CHANGELOG|LICENSE|INSTALL)">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# إعدادات الترميز
AddDefaultCharset UTF-8

# إعدادات نوع المحتوى
AddType application/javascript .js
AddType text/css .css

# منع عرض محتويات المجلدات
Options -Indexes

# صفحات الأخطاء المخصصة
ErrorDocument 403 /error/403.html
ErrorDocument 404 /error/404.html
ErrorDocument 500 /error/500.html

# إعدادات إضافية للأمان
<IfModule mod_rewrite.c>
    # منع الوصول إلى الملفات الحساسة
    RewriteRule ^(config|includes|logs|backups)/ - [F,L]
    
    # منع الوصول المباشر لملفات PHP في مجلدات معينة
    RewriteRule ^(uploads|assets)/.+\.php$ - [F,L]
    
    # إعادة توجيه HTTP إلى HTTPS (للإنتاج)
    # RewriteCond %{HTTPS} off
    # RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
</IfModule>
