<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة الفواتير</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .invoice-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
        }

        .invoice-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .invoice-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid #eee;
        }

        .invoice-sales {
            border-left-color: #28a745;
        }

        .invoice-purchase {
            border-left-color: #dc3545;
        }

        .invoice-return {
            border-left-color: #ffc107;
        }

        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            border: none;
            color: #6c757d;
            font-weight: 600;
        }

        .nav-tabs .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .tab-content {
            background: white;
            border-radius: 0 0 15px 15px;
            padding: 2rem;
        }

        .invoice-item-row {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .total-section {
            background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
            border-radius: 10px;
            padding: 1rem;
            margin-top: 1rem;
        }

        @media print {
            .sidebar, .btn, .action-buttons {
                display: none !important;
            }
            .main-content {
                margin-right: 0 !important;
            }
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="products.html">
                    <i class="bi bi-basket"></i>
                    <span class="menu-text">المنتجات والوصفات</span>
                </a>
            </li>

            <li>
                <a href="journal-entries.html">
                    <i class="bi bi-journal-text"></i>
                    <span class="menu-text">القيود المحاسبية</span>
                </a>
            </li>

            <li>
                <a href="invoices.html" class="active">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان والأزرار -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-receipt text-primary me-2"></i>
                    إدارة الفواتير
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-success" onclick="showSalesInvoiceModal()">
                        <i class="bi bi-plus-lg me-1"></i>فاتورة مبيعات
                    </button>
                    <button type="button" class="btn btn-primary" onclick="showPurchaseInvoiceModal()">
                        <i class="bi bi-plus-lg me-1"></i>فاتورة مشتريات
                    </button>
                    <button type="button" class="btn btn-warning" onclick="showReturnInvoiceModal()">
                        <i class="bi bi-arrow-return-left me-1"></i>فاتورة مرتجع
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="printPage()">
                        <i class="bi bi-printer me-1"></i>طباعة
                    </button>
                </div>
            </div>

            <!-- إحصائيات الفواتير -->
            <div class="row mb-4">
                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #28a745;">
                        <div class="icon text-success">
                            <i class="bi bi-cart-check"></i>
                        </div>
                        <div class="number text-success" id="totalSalesInvoices">28</div>
                        <div class="label">فواتير المبيعات</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #dc3545;">
                        <div class="icon text-danger">
                            <i class="bi bi-bag"></i>
                        </div>
                        <div class="number text-danger" id="totalPurchaseInvoices">15</div>
                        <div class="label">فواتير المشتريات</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #ffc107;">
                        <div class="icon text-warning">
                            <i class="bi bi-arrow-return-left"></i>
                        </div>
                        <div class="number text-warning" id="totalReturnInvoices">5</div>
                        <div class="label">فواتير المرتجعات</div>
                    </div>
                </div>

                <div class="col-lg-3 col-md-6">
                    <div class="stats-card" style="border-left-color: #17a2b8;">
                        <div class="icon text-info">
                            <i class="bi bi-currency-exchange"></i>
                        </div>
                        <div class="number text-info" id="totalSalesAmount">485,750</div>
                        <div class="label">إجمالي المبيعات (ر.ي)</div>
                    </div>
                </div>
            </div>

            <!-- التبويبات -->
            <div class="card">
                <div class="card-header p-0">
                    <ul class="nav nav-tabs" id="invoiceTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab">
                                <i class="bi bi-cart-check me-2"></i>فواتير المبيعات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="purchases-tab" data-bs-toggle="tab" data-bs-target="#purchases" type="button" role="tab">
                                <i class="bi bi-bag me-2"></i>فواتير المشتريات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="returns-tab" data-bs-toggle="tab" data-bs-target="#returns" type="button" role="tab">
                                <i class="bi bi-arrow-return-left me-2"></i>المرتجعات
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports" type="button" role="tab">
                                <i class="bi bi-graph-up me-2"></i>تقارير المبيعات
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="invoiceTabContent">
                    <!-- تبويب فواتير المبيعات -->
                    <div class="tab-pane fade show active" id="sales" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="salesDateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="salesDateFrom">
                            </div>
                            <div class="col-md-3">
                                <label for="salesDateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="salesDateTo">
                            </div>
                            <div class="col-md-3">
                                <label for="salesCustomerFilter" class="form-label">العميل</label>
                                <select class="form-select" id="salesCustomerFilter">
                                    <option value="">جميع العملاء</option>
                                    <option value="cash">عميل نقدي</option>
                                    <option value="credit">عملاء آجل</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="filterSalesInvoices()">
                                        <i class="bi bi-search me-1"></i>بحث
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- قائمة فواتير المبيعات -->
                        <div id="salesInvoicesList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- تبويب فواتير المشتريات -->
                    <div class="tab-pane fade" id="purchases" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="purchaseDateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="purchaseDateFrom">
                            </div>
                            <div class="col-md-3">
                                <label for="purchaseDateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="purchaseDateTo">
                            </div>
                            <div class="col-md-3">
                                <label for="supplierFilter" class="form-label">المورد</label>
                                <select class="form-select" id="supplierFilter">
                                    <option value="">جميع الموردين</option>
                                    <option value="supplier1">مورد الدقيق</option>
                                    <option value="supplier2">مورد المكونات</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="button" class="btn btn-primary" onclick="filterPurchaseInvoices()">
                                        <i class="bi bi-search me-1"></i>بحث
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- قائمة فواتير المشتريات -->
                        <div id="purchaseInvoicesList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- تبويب المرتجعات -->
                    <div class="tab-pane fade" id="returns" role="tabpanel">
                        <!-- فلاتر البحث -->
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="returnDateFrom" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="returnDateFrom">
                            </div>
                            <div class="col-md-4">
                                <label for="returnDateTo" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="returnDateTo">
                            </div>
                            <div class="col-md-4">
                                <label for="returnTypeFilter" class="form-label">نوع المرتجع</label>
                                <select class="form-select" id="returnTypeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="sales_return">مرتجع مبيعات</option>
                                    <option value="purchase_return">مرتجع مشتريات</option>
                                </select>
                            </div>
                        </div>

                        <!-- قائمة المرتجعات -->
                        <div id="returnInvoicesList">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>

                    <!-- تبويب تقارير المبيعات -->
                    <div class="tab-pane fade" id="reports" role="tabpanel">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">المبيعات اليومية</h6>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="dailySalesChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">أفضل المنتجات مبيعاً</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="list-group list-group-flush" id="topSellingProducts">
                                            <!-- سيتم ملؤها بـ JavaScript -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="card">
                                    <div class="card-header">
                                        <h6 class="card-title mb-0">ملخص المبيعات الشهرية</h6>
                                    </div>
                                    <div class="card-body">
                                        <div class="table-responsive">
                                            <table class="table table-striped">
                                                <thead>
                                                    <tr>
                                                        <th>الشهر</th>
                                                        <th>عدد الفواتير</th>
                                                        <th>إجمالي المبيعات</th>
                                                        <th>متوسط الفاتورة</th>
                                                        <th>المرتجعات</th>
                                                        <th>صافي المبيعات</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="monthlySalesTableBody">
                                                    <!-- سيتم ملؤها بـ JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة فاتورة مبيعات -->
    <div class="modal fade" id="salesInvoiceModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">فاتورة مبيعات جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="salesInvoiceForm">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="salesInvoiceNumber" class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="salesInvoiceNumber" readonly>
                            </div>
                            <div class="col-md-3">
                                <label for="salesInvoiceDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="salesInvoiceDate" required>
                            </div>
                            <div class="col-md-3">
                                <label for="salesCustomerName" class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" id="salesCustomerName" placeholder="عميل نقدي">
                            </div>
                            <div class="col-md-3">
                                <label for="salesPaymentMethod" class="form-label">طريقة الدفع *</label>
                                <select class="form-select" id="salesPaymentMethod" required>
                                    <option value="cash">نقدي</option>
                                    <option value="credit">آجل</option>
                                    <option value="bank">بنكي</option>
                                </select>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">أصناف الفاتورة</h6>

                        <!-- إضافة صنف جديد -->
                        <div class="invoice-item-row mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="salesItemSelect" class="form-label">الصنف</label>
                                    <select class="form-select" id="salesItemSelect">
                                        <option value="">اختر الصنف</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="salesItemQuantity" class="form-label">الكمية</label>
                                    <input type="number" class="form-control" id="salesItemQuantity" step="0.001" onchange="calculateSalesItemTotal()">
                                </div>
                                <div class="col-md-2">
                                    <label for="salesItemPrice" class="form-label">السعر</label>
                                    <input type="number" class="form-control" id="salesItemPrice" step="0.01" onchange="calculateSalesItemTotal()">
                                </div>
                                <div class="col-md-2">
                                    <label for="salesItemTotal" class="form-label">الإجمالي</label>
                                    <input type="text" class="form-control" id="salesItemTotal" readonly>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-success" onclick="addSalesItem()">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول أصناف الفاتورة -->
                        <div class="table-responsive">
                            <table class="table table-striped" id="salesItemsTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الكمية</th>
                                        <th>الوحدة</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="salesItemsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <!-- إجماليات الفاتورة -->
                        <div class="total-section">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="salesDiscount" class="form-label">الخصم</label>
                                            <input type="number" class="form-control" id="salesDiscount" step="0.01" value="0" onchange="calculateSalesTotals()">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="salesTax" class="form-label">الضريبة (%)</label>
                                            <input type="number" class="form-control" id="salesTax" step="0.01" value="0" onchange="calculateSalesTotals()">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-end">
                                        <div class="mb-2">
                                            <strong>المجموع الفرعي: </strong>
                                            <span id="salesSubtotal">0.00</span> ر.ي
                                        </div>
                                        <div class="mb-2">
                                            <strong>بعد الخصم: </strong>
                                            <span id="salesAfterDiscount">0.00</span> ر.ي
                                        </div>
                                        <div class="mb-2">
                                            <strong>الضريبة: </strong>
                                            <span id="salesTaxAmount">0.00</span> ر.ي
                                        </div>
                                        <div class="h5 text-primary">
                                            <strong>الإجمالي النهائي: </strong>
                                            <span id="salesGrandTotal">0.00</span> ر.ي
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label for="salesNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="salesNotes" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="saveSalesInvoiceAsDraft()">حفظ كمسودة</button>
                    <button type="button" class="btn btn-success" onclick="saveSalesInvoice()">حفظ وطباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة فاتورة مشتريات -->
    <div class="modal fade" id="purchaseInvoiceModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">فاتورة مشتريات جديدة</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="purchaseInvoiceForm">
                        <div class="row mb-4">
                            <div class="col-md-3">
                                <label for="purchaseInvoiceNumber" class="form-label">رقم الفاتورة</label>
                                <input type="text" class="form-control" id="purchaseInvoiceNumber" readonly>
                            </div>
                            <div class="col-md-3">
                                <label for="purchaseInvoiceDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="purchaseInvoiceDate" required>
                            </div>
                            <div class="col-md-3">
                                <label for="supplierName" class="form-label">اسم المورد *</label>
                                <input type="text" class="form-control" id="supplierName" required>
                            </div>
                            <div class="col-md-3">
                                <label for="supplierInvoiceNumber" class="form-label">رقم فاتورة المورد</label>
                                <input type="text" class="form-control" id="supplierInvoiceNumber">
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">أصناف الفاتورة</h6>

                        <!-- إضافة صنف جديد -->
                        <div class="invoice-item-row mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="purchaseItemSelect" class="form-label">الصنف</label>
                                    <select class="form-select" id="purchaseItemSelect">
                                        <option value="">اختر الصنف</option>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="purchaseItemQuantity" class="form-label">الكمية</label>
                                    <input type="number" class="form-control" id="purchaseItemQuantity" step="0.001" onchange="calculatePurchaseItemTotal()">
                                </div>
                                <div class="col-md-2">
                                    <label for="purchaseItemPrice" class="form-label">السعر</label>
                                    <input type="number" class="form-control" id="purchaseItemPrice" step="0.01" onchange="calculatePurchaseItemTotal()">
                                </div>
                                <div class="col-md-2">
                                    <label for="purchaseItemTotal" class="form-label">الإجمالي</label>
                                    <input type="text" class="form-control" id="purchaseItemTotal" readonly>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-success" onclick="addPurchaseItem()">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول أصناف الفاتورة -->
                        <div class="table-responsive">
                            <table class="table table-striped" id="purchaseItemsTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الكمية</th>
                                        <th>الوحدة</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="purchaseItemsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <!-- إجماليات الفاتورة -->
                        <div class="total-section">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label for="purchaseDiscount" class="form-label">الخصم</label>
                                            <input type="number" class="form-control" id="purchaseDiscount" step="0.01" value="0" onchange="calculatePurchaseTotals()">
                                        </div>
                                        <div class="col-md-6">
                                            <label for="purchaseTax" class="form-label">الضريبة (%)</label>
                                            <input type="number" class="form-control" id="purchaseTax" step="0.01" value="0" onchange="calculatePurchaseTotals()">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="text-end">
                                        <div class="mb-2">
                                            <strong>المجموع الفرعي: </strong>
                                            <span id="purchaseSubtotal">0.00</span> ر.ي
                                        </div>
                                        <div class="mb-2">
                                            <strong>بعد الخصم: </strong>
                                            <span id="purchaseAfterDiscount">0.00</span> ر.ي
                                        </div>
                                        <div class="mb-2">
                                            <strong>الضريبة: </strong>
                                            <span id="purchaseTaxAmount">0.00</span> ر.ي
                                        </div>
                                        <div class="h5 text-primary">
                                            <strong>الإجمالي النهائي: </strong>
                                            <span id="purchaseGrandTotal">0.00</span> ر.ي
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label for="purchaseNotes" class="form-label">ملاحظات</label>
                                <textarea class="form-control" id="purchaseNotes" rows="2"></textarea>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="savePurchaseInvoiceAsDraft()">حفظ كمسودة</button>
                    <button type="button" class="btn btn-primary" onclick="savePurchaseInvoice()">حفظ وطباعة</button>
                </div>
            </div>
        </div>
    </div>

    <!-- نافذة فاتورة مرتجع -->
    <div class="modal fade" id="returnInvoiceModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">فاتورة مرتجع</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="returnInvoiceForm">
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <label for="returnInvoiceNumber" class="form-label">رقم المرتجع</label>
                                <input type="text" class="form-control" id="returnInvoiceNumber" readonly>
                            </div>
                            <div class="col-md-4">
                                <label for="returnInvoiceDate" class="form-label">التاريخ *</label>
                                <input type="date" class="form-control" id="returnInvoiceDate" required>
                            </div>
                            <div class="col-md-4">
                                <label for="returnType" class="form-label">نوع المرتجع *</label>
                                <select class="form-select" id="returnType" required>
                                    <option value="">اختر النوع</option>
                                    <option value="sales_return">مرتجع مبيعات</option>
                                    <option value="purchase_return">مرتجع مشتريات</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label for="originalInvoiceNumber" class="form-label">رقم الفاتورة الأصلية *</label>
                                <input type="text" class="form-control" id="originalInvoiceNumber" required>
                            </div>
                            <div class="col-md-6">
                                <label for="returnReason" class="form-label">سبب المرتجع *</label>
                                <select class="form-select" id="returnReason" required>
                                    <option value="">اختر السبب</option>
                                    <option value="damaged">تالف</option>
                                    <option value="expired">منتهي الصلاحية</option>
                                    <option value="wrong_item">صنف خاطئ</option>
                                    <option value="customer_request">طلب العميل</option>
                                    <option value="quality_issue">مشكلة في الجودة</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-12">
                                <label for="returnNotes" class="form-label">ملاحظات المرتجع</label>
                                <textarea class="form-control" id="returnNotes" rows="3"></textarea>
                            </div>
                        </div>

                        <h6 class="text-primary mb-3">أصناف المرتجع</h6>

                        <!-- إضافة صنف مرتجع -->
                        <div class="invoice-item-row mb-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <label for="returnItemSelect" class="form-label">الصنف</label>
                                    <select class="form-select" id="returnItemSelect">
                                        <option value="">اختر الصنف</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="returnItemQuantity" class="form-label">الكمية المرتجعة</label>
                                    <input type="number" class="form-control" id="returnItemQuantity" step="0.001">
                                </div>
                                <div class="col-md-3">
                                    <label for="returnItemPrice" class="form-label">السعر</label>
                                    <input type="number" class="form-control" id="returnItemPrice" step="0.01" readonly>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="button" class="btn btn-warning" onclick="addReturnItem()">
                                            <i class="bi bi-plus-lg"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- جدول أصناف المرتجع -->
                        <div class="table-responsive">
                            <table class="table table-striped" id="returnItemsTable">
                                <thead>
                                    <tr>
                                        <th>الصنف</th>
                                        <th>الكمية المرتجعة</th>
                                        <th>السعر</th>
                                        <th>الإجمالي</th>
                                        <th>العمليات</th>
                                    </tr>
                                </thead>
                                <tbody id="returnItemsTableBody">
                                    <!-- سيتم ملؤها بـ JavaScript -->
                                </tbody>
                            </table>
                        </div>

                        <div class="total-section">
                            <div class="text-end">
                                <div class="h5 text-warning">
                                    <strong>إجمالي المرتجع: </strong>
                                    <span id="returnGrandTotal">0.00</span> ر.ي
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-warning" onclick="saveReturnInvoice()">حفظ المرتجع</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Invoice JS Files -->
    <script src="invoice-functions.js"></script>
    <script src="print-system.js"></script>
    <script src="system-init.js"></script>

    <script>
        // بيانات الفواتير التجريبية
        let salesInvoices = [
            {
                id: 1,
                number: 'INV-S-001',
                date: '2024-01-15',
                customerName: 'أحمد محمد',
                paymentMethod: 'cash',
                status: 'completed',
                subtotal: 150.00,
                discount: 10.00,
                tax: 7.00,
                total: 147.00,
                items: [
                    { itemId: 1, itemName: 'خبز أبيض', quantity: 10, price: 15.00, total: 150.00 }
                ]
            },
            {
                id: 2,
                number: 'INV-S-002',
                date: '2024-01-14',
                customerName: 'فاطمة علي',
                paymentMethod: 'credit',
                status: 'pending',
                subtotal: 85.00,
                discount: 0.00,
                tax: 4.25,
                total: 89.25,
                items: [
                    { itemId: 2, itemName: 'كرواسان', quantity: 5, price: 17.00, total: 85.00 }
                ]
            }
        ];

        let purchaseInvoices = [
            {
                id: 1,
                number: 'INV-P-001',
                date: '2024-01-10',
                supplierName: 'مورد الدقيق المحدود',
                supplierInvoiceNumber: 'SUP-001',
                status: 'completed',
                subtotal: 5000.00,
                discount: 100.00,
                tax: 245.00,
                total: 5145.00,
                items: [
                    { itemId: 1, itemName: 'دقيق أبيض', quantity: 100, price: 50.00, total: 5000.00 }
                ]
            }
        ];

        let returnInvoices = [
            {
                id: 1,
                number: 'RET-001',
                date: '2024-01-12',
                type: 'sales_return',
                originalInvoiceNumber: 'INV-S-001',
                reason: 'damaged',
                total: 30.00,
                items: [
                    { itemId: 1, itemName: 'خبز أبيض', quantity: 2, price: 15.00, total: 30.00 }
                ]
            }
        ];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // طباعة الصفحة
        function printPage() {
            window.print();
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            displaySalesInvoices();
            displayPurchaseInvoices();
            displayReturnInvoices();
            updateInvoiceStats();
        });
    </script>
</body>
</html>