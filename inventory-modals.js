// وظائف النوافذ المنبثقة والعمليات المتقدمة

// إظهار نافذة حركة المخزون
function showMovementModal() {
    document.getElementById('movementForm').reset();
    document.getElementById('movementDate').value = new Date().toISOString().split('T')[0];
    populateMovementItems();
    populateMovementWarehouses();
    const modal = new bootstrap.Modal(document.getElementById('movementModal'));
    modal.show();
}

// ملء قائمة الأصناف في نافذة الحركة
function populateMovementItems() {
    const select = document.getElementById('movementItem');
    select.innerHTML = '<option value="">اختر الصنف</option>';
    
    items.filter(item => item.isActive).forEach(item => {
        const option = document.createElement('option');
        option.value = item.id;
        option.textContent = `${item.name} (${item.code})`;
        select.appendChild(option);
    });
}

// ملء قائمة المخازن في نافذة الحركة
function populateMovementWarehouses() {
    const select = document.getElementById('movementWarehouse');
    const toSelect = document.getElementById('toWarehouse');
    
    select.innerHTML = '<option value="">اختر المخزن</option>';
    toSelect.innerHTML = '<option value="">اختر المخزن</option>';
    
    warehouses.filter(wh => wh.isActive).forEach(warehouse => {
        const option1 = document.createElement('option');
        option1.value = warehouse.id;
        option1.textContent = warehouse.name;
        select.appendChild(option1);
        
        const option2 = document.createElement('option');
        option2.value = warehouse.id;
        option2.textContent = warehouse.name;
        toSelect.appendChild(option2);
    });
}

// تبديل حقول الحركة حسب النوع
function toggleMovementFields() {
    const type = document.getElementById('movementType').value;
    const toWarehouseField = document.getElementById('toWarehouseField');
    
    if (type === 'transfer') {
        toWarehouseField.style.display = 'block';
    } else {
        toWarehouseField.style.display = 'none';
    }
}

// تحديث مخازن الصنف المختار
function updateItemWarehouses() {
    const itemId = parseInt(document.getElementById('movementItem').value);
    const warehouseSelect = document.getElementById('movementWarehouse');
    
    if (itemId) {
        const item = items.find(i => i.id === itemId);
        if (item) {
            // تحديث الرصيد الحالي عند تغيير المخزن
            warehouseSelect.addEventListener('change', function() {
                const warehouseId = parseInt(this.value);
                const currentStock = item.warehouseStocks[warehouseId] || 0;
                document.getElementById('currentStock').textContent = currentStock.toLocaleString();
            });
        }
    }
}

// حفظ حركة المخزون
function saveMovement() {
    const itemId = parseInt(document.getElementById('movementItem').value);
    const item = items.find(i => i.id === itemId);
    const warehouseId = parseInt(document.getElementById('movementWarehouse').value);
    const warehouse = warehouses.find(w => w.id === warehouseId);
    const type = document.getElementById('movementType').value;
    const quantity = parseFloat(document.getElementById('movementQuantity').value);
    
    if (!item || !warehouse || !type || !quantity) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    // التحقق من الرصيد في حالة الصرف
    if (type === 'out' || type === 'transfer') {
        const currentStock = item.warehouseStocks[warehouseId] || 0;
        if (currentStock < quantity) {
            alert('الكمية المطلوبة أكبر من الرصيد المتاح');
            return;
        }
    }
    
    // تنفيذ الحركة
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    
    const newMovement = {
        id: movements.length + 1,
        itemId: itemId,
        itemName: item.name,
        type: type,
        warehouseId: warehouseId,
        warehouseName: warehouse.name,
        quantity: quantity,
        date: document.getElementById('movementDate').value,
        reference: document.getElementById('movementReference').value,
        user: user.name,
        notes: document.getElementById('movementNotes').value
    };
    
    // تحديث المخزون
    if (type === 'in' || type === 'adjustment') {
        item.warehouseStocks[warehouseId] = (item.warehouseStocks[warehouseId] || 0) + quantity;
    } else if (type === 'out') {
        item.warehouseStocks[warehouseId] = (item.warehouseStocks[warehouseId] || 0) - quantity;
    } else if (type === 'transfer') {
        const toWarehouseId = parseInt(document.getElementById('toWarehouse').value);
        if (!toWarehouseId || toWarehouseId === warehouseId) {
            alert('يرجى اختيار مخزن مختلف للتحويل');
            return;
        }
        
        item.warehouseStocks[warehouseId] = (item.warehouseStocks[warehouseId] || 0) - quantity;
        item.warehouseStocks[toWarehouseId] = (item.warehouseStocks[toWarehouseId] || 0) + quantity;
        
        // إضافة حركة للمخزن المستقبل
        const toWarehouse = warehouses.find(w => w.id === toWarehouseId);
        movements.push({
            id: movements.length + 2,
            itemId: itemId,
            itemName: item.name,
            type: 'in',
            warehouseId: toWarehouseId,
            warehouseName: toWarehouse.name,
            quantity: quantity,
            date: document.getElementById('movementDate').value,
            reference: document.getElementById('movementReference').value + ' (تحويل)',
            user: user.name,
            notes: `تحويل من ${warehouse.name}`
        });
    }
    
    movements.push(newMovement);
    displayItems();
    displayMovements();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('movementModal'));
    modal.hide();
    
    alert('تم تنفيذ الحركة بنجاح!');
}

// إظهار نافذة إضافة مخزن
function showAddWarehouseModal() {
    document.getElementById('addWarehouseForm').reset();
    document.getElementById('warehouseActive').checked = true;
    const modal = new bootstrap.Modal(document.getElementById('addWarehouseModal'));
    modal.show();
}

// حفظ مخزن جديد
function saveWarehouse() {
    const newWarehouse = {
        id: warehouses.length + 1,
        code: document.getElementById('warehouseCode').value,
        name: document.getElementById('warehouseName').value,
        location: document.getElementById('warehouseLocation').value,
        manager: document.getElementById('warehouseManager').value,
        description: document.getElementById('warehouseDescription').value,
        isActive: document.getElementById('warehouseActive').checked
    };
    
    warehouses.push(newWarehouse);
    
    // إضافة المخزن الجديد لجميع الأصناف
    items.forEach(item => {
        item.warehouseStocks[newWarehouse.id] = 0;
    });
    
    displayWarehouses();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addWarehouseModal'));
    modal.hide();
    
    alert('تم إضافة المخزن بنجاح!');
}

// إظهار نافذة الجرد
function showInventoryModal() {
    document.getElementById('inventoryForm').reset();
    document.getElementById('inventoryDate').value = new Date().toISOString().split('T')[0];
    populateInventoryWarehouses();
    const modal = new bootstrap.Modal(document.getElementById('inventoryModal'));
    modal.show();
}

// ملء قائمة المخازن في نافذة الجرد
function populateInventoryWarehouses() {
    const select = document.getElementById('inventoryWarehouse');
    select.innerHTML = '<option value="">اختر المخزن</option>';
    
    warehouses.filter(wh => wh.isActive).forEach(warehouse => {
        const option = document.createElement('option');
        option.value = warehouse.id;
        option.textContent = warehouse.name;
        select.appendChild(option);
    });
}

// تحميل أصناف الجرد
function loadInventoryItems() {
    const warehouseId = parseInt(document.getElementById('inventoryWarehouse').value);
    const tbody = document.getElementById('inventoryTableBody');
    tbody.innerHTML = '';
    
    if (!warehouseId) return;
    
    items.filter(item => item.warehouseStocks[warehouseId] > 0).forEach(item => {
        const row = document.createElement('tr');
        const currentStock = item.warehouseStocks[warehouseId];
        
        row.innerHTML = `
            <td>${item.name}</td>
            <td>${currentStock.toLocaleString()}</td>
            <td>
                <input type="number" class="form-control" id="actual_${item.id}" step="0.001" value="${currentStock}" onchange="calculateDifference(${item.id}, ${currentStock})">
            </td>
            <td id="diff_${item.id}">0</td>
            <td>
                <input type="text" class="form-control" id="notes_${item.id}" placeholder="ملاحظات...">
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// حساب الفرق في الجرد
function calculateDifference(itemId, systemStock) {
    const actualInput = document.getElementById(`actual_${itemId}`);
    const diffCell = document.getElementById(`diff_${itemId}`);
    
    const actualStock = parseFloat(actualInput.value) || 0;
    const difference = actualStock - systemStock;
    
    diffCell.textContent = difference.toLocaleString();
    diffCell.className = difference > 0 ? 'text-success' : difference < 0 ? 'text-danger' : '';
}

// تنفيذ الجرد
function processInventory() {
    const warehouseId = parseInt(document.getElementById('inventoryWarehouse').value);
    const date = document.getElementById('inventoryDate').value;
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    
    if (!warehouseId || !date) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    let adjustmentsCount = 0;
    
    items.filter(item => item.warehouseStocks[warehouseId] > 0).forEach(item => {
        const actualInput = document.getElementById(`actual_${item.id}`);
        const notesInput = document.getElementById(`notes_${item.id}`);
        
        if (actualInput) {
            const systemStock = item.warehouseStocks[warehouseId];
            const actualStock = parseFloat(actualInput.value) || 0;
            const difference = actualStock - systemStock;
            
            if (difference !== 0) {
                // إنشاء حركة تسوية
                const warehouse = warehouses.find(w => w.id === warehouseId);
                movements.push({
                    id: movements.length + 1,
                    itemId: item.id,
                    itemName: item.name,
                    type: 'adjustment',
                    warehouseId: warehouseId,
                    warehouseName: warehouse.name,
                    quantity: Math.abs(difference),
                    date: date,
                    reference: `INV-${Date.now()}`,
                    user: user.name,
                    notes: `جرد - ${difference > 0 ? 'زيادة' : 'نقص'}: ${notesInput.value || 'بدون ملاحظات'}`
                });
                
                // تحديث المخزون
                item.warehouseStocks[warehouseId] = actualStock;
                adjustmentsCount++;
            }
        }
    });
    
    displayItems();
    displayMovements();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('inventoryModal'));
    modal.hide();
    
    alert(`تم تنفيذ الجرد بنجاح! تم إنشاء ${adjustmentsCount} حركة تسوية.`);
}

// تحميل الحركات حسب الفلاتر
function loadMovements() {
    const fromDate = document.getElementById('movementDateFrom').value;
    const toDate = document.getElementById('movementDateTo').value;
    const typeFilter = document.getElementById('movementTypeFilter').value;
    
    let filtered = movements.filter(movement => {
        const matchesDate = (!fromDate || movement.date >= fromDate) && 
                           (!toDate || movement.date <= toDate);
        const matchesType = !typeFilter || movement.type === typeFilter;
        
        return matchesDate && matchesType;
    });
    
    displayMovements(filtered);
}

// عرض تفاصيل الصنف
function viewItemDetails(itemId) {
    const item = items.find(i => i.id === itemId);
    if (!item) return;
    
    let warehouseDetails = '';
    warehouses.forEach(warehouse => {
        const stock = item.warehouseStocks[warehouse.id] || 0;
        warehouseDetails += `${warehouse.name}: ${stock.toLocaleString()}\n`;
    });
    
    alert(`تفاصيل الصنف:\nالاسم: ${item.name}\nالكود: ${item.code}\nالفئة: ${item.category}\nالوحدة: ${item.unit}\n\nالمخزون في المخازن:\n${warehouseDetails}`);
}

// تعديل الصنف
function editItem(itemId) {
    alert('سيتم فتح نافذة التعديل قريباً');
}

// إضافة مخزون
function addStock(itemId) {
    const item = items.find(i => i.id === itemId);
    if (item) {
        document.getElementById('movementItem').value = itemId;
        document.getElementById('movementType').value = 'in';
        showMovementModal();
    }
}

// تحويل مخزون
function transferStock(itemId) {
    const item = items.find(i => i.id === itemId);
    if (item) {
        document.getElementById('movementItem').value = itemId;
        document.getElementById('movementType').value = 'transfer';
        toggleMovementFields();
        showMovementModal();
    }
}

// عرض مخزون المخزن
function viewWarehouseStock(warehouseId) {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    if (!warehouse) return;
    
    let stockDetails = `مخزون ${warehouse.name}:\n\n`;
    let totalItems = 0;
    
    items.forEach(item => {
        const stock = item.warehouseStocks[warehouseId] || 0;
        if (stock > 0) {
            stockDetails += `${item.name}: ${stock.toLocaleString()} ${item.unit}\n`;
            totalItems++;
        }
    });
    
    if (totalItems === 0) {
        stockDetails += 'لا توجد أصناف في هذا المخزن';
    }
    
    alert(stockDetails);
}

// تعديل المخزن
function editWarehouse(warehouseId) {
    alert('سيتم فتح نافذة التعديل قريباً');
}

// تبديل حالة المخزن
function toggleWarehouseStatus(warehouseId) {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    if (warehouse) {
        warehouse.isActive = !warehouse.isActive;
        displayWarehouses();
        updateStats();
        alert(`تم ${warehouse.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المخزن بنجاح!`);
    }
}
