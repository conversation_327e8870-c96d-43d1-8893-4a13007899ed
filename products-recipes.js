// وظائف الوصفات والإنتاج والتالف

// إظهار نافذة الوصفة
function showRecipeModal() {
    document.getElementById('recipeForm').reset();
    populateProductSelects();
    populateIngredientSelect();
    clearIngredientsTable();
    const modal = new bootstrap.Modal(document.getElementById('recipeModal'));
    modal.show();
}

// تحميل تفاصيل الوصفة
function loadRecipeDetails() {
    const productId = parseInt(document.getElementById('recipeProductFilter').value);
    const recipeDetails = document.getElementById('recipeDetails');
    
    if (!productId) {
        recipeDetails.classList.add('d-none');
        return;
    }
    
    const recipe = recipes.find(r => r.productId === productId);
    if (!recipe) {
        recipeDetails.classList.add('d-none');
        alert('لا توجد وصفة لهذا المنتج');
        return;
    }
    
    recipeDetails.classList.remove('d-none');
    displayRecipeIngredients(recipe);
    updateCostBreakdown(recipe);
}

// عرض مكونات الوصفة
function displayRecipeIngredients(recipe) {
    const container = document.getElementById('recipeIngredients');
    container.innerHTML = '';
    
    recipe.ingredients.forEach(ingredient => {
        const div = document.createElement('div');
        div.className = 'recipe-ingredient';
        div.innerHTML = `
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${ingredient.itemName}</strong>
                    <br><small class="text-muted">${ingredient.quantity} ${ingredient.unit}</small>
                </div>
                <div class="text-end">
                    <div class="fw-bold">${ingredient.cost.toLocaleString()} ر.ي</div>
                    <small class="text-muted">${ingredient.unitPrice} ر.ي/${ingredient.unit}</small>
                </div>
            </div>
        `;
        container.appendChild(div);
    });
}

// تحديث تحليل التكلفة
function updateCostBreakdown(recipe) {
    const product = products.find(p => p.id === recipe.productId);
    if (!product) return;
    
    const materialCost = recipe.totalMaterialCost;
    const laborCost = product.laborCost * recipe.yield;
    const overheadCost = product.overheadCost * recipe.yield;
    const totalCost = materialCost + laborCost + overheadCost;
    const sellingPrice = product.sellingPrice * recipe.yield;
    const profitMargin = sellingPrice > 0 ? ((sellingPrice - totalCost) / sellingPrice * 100).toFixed(1) : 0;
    
    document.getElementById('materialCost').textContent = `${materialCost.toLocaleString()} ر.ي`;
    document.getElementById('laborCost').textContent = `${laborCost.toLocaleString()} ر.ي`;
    document.getElementById('overheadCost').textContent = `${overheadCost.toLocaleString()} ر.ي`;
    document.getElementById('totalCost').textContent = `${totalCost.toLocaleString()} ر.ي`;
    document.getElementById('sellingPrice').textContent = `${sellingPrice.toLocaleString()} ر.ي`;
    document.getElementById('profitMargin').textContent = `${profitMargin}%`;
}

// إضافة مكون للوصفة
function addIngredient() {
    const ingredientSelect = document.getElementById('ingredientSelect');
    const quantity = parseFloat(document.getElementById('ingredientQuantity').value);
    const unit = document.getElementById('ingredientUnit').value;
    
    if (!ingredientSelect.value || !quantity || !unit) {
        alert('يرجى ملء جميع حقول المكون');
        return;
    }
    
    const selectedOption = ingredientSelect.options[ingredientSelect.selectedIndex];
    const itemName = selectedOption.textContent;
    const unitPrice = parseFloat(selectedOption.dataset.unitPrice) || 0;
    const cost = quantity * unitPrice;
    
    const tbody = document.getElementById('ingredientsTableBody');
    const row = document.createElement('tr');
    
    row.innerHTML = `
        <td>${itemName}</td>
        <td>${quantity}</td>
        <td>${unit}</td>
        <td>${unitPrice.toLocaleString()} ر.ي</td>
        <td>${cost.toLocaleString()} ر.ي</td>
        <td>
            <button class="btn btn-sm btn-outline-danger" onclick="removeIngredient(this)">
                <i class="bi bi-trash"></i>
            </button>
        </td>
    `;
    
    row.dataset.itemId = ingredientSelect.value;
    row.dataset.quantity = quantity;
    row.dataset.unit = unit;
    row.dataset.unitPrice = unitPrice;
    row.dataset.cost = cost;
    
    tbody.appendChild(row);
    
    // مسح الحقول
    document.getElementById('ingredientQuantity').value = '';
    ingredientSelect.value = '';
    
    updateTotalMaterialCost();
}

// حذف مكون من الوصفة
function removeIngredient(button) {
    button.closest('tr').remove();
    updateTotalMaterialCost();
}

// تحديث إجمالي تكلفة المواد
function updateTotalMaterialCost() {
    const rows = document.querySelectorAll('#ingredientsTableBody tr');
    let total = 0;
    
    rows.forEach(row => {
        total += parseFloat(row.dataset.cost) || 0;
    });
    
    document.getElementById('totalMaterialCost').textContent = `${total.toLocaleString()} ر.ي`;
}

// مسح جدول المكونات
function clearIngredientsTable() {
    document.getElementById('ingredientsTableBody').innerHTML = '';
    updateTotalMaterialCost();
}

// حفظ الوصفة
function saveRecipe() {
    const productId = parseInt(document.getElementById('recipeProduct').value);
    const yield_ = parseFloat(document.getElementById('recipeYield').value);
    const yieldUnit = document.getElementById('recipeYieldUnit').value;
    const instructions = document.getElementById('recipeInstructions').value;
    
    if (!productId || !yield_) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const rows = document.querySelectorAll('#ingredientsTableBody tr');
    if (rows.length === 0) {
        alert('يرجى إضافة مكونات للوصفة');
        return;
    }
    
    const ingredients = [];
    let totalMaterialCost = 0;
    
    rows.forEach(row => {
        const ingredient = {
            itemId: parseInt(row.dataset.itemId),
            itemName: row.cells[0].textContent,
            quantity: parseFloat(row.dataset.quantity),
            unit: row.dataset.unit,
            unitPrice: parseFloat(row.dataset.unitPrice),
            cost: parseFloat(row.dataset.cost)
        };
        ingredients.push(ingredient);
        totalMaterialCost += ingredient.cost;
    });
    
    const product = products.find(p => p.id === productId);
    const existingRecipeIndex = recipes.findIndex(r => r.productId === productId);
    
    const recipe = {
        id: existingRecipeIndex >= 0 ? recipes[existingRecipeIndex].id : recipes.length + 1,
        productId: productId,
        productName: product.name,
        yield: yield_,
        yieldUnit: yieldUnit,
        ingredients: ingredients,
        instructions: instructions,
        totalMaterialCost: totalMaterialCost
    };
    
    if (existingRecipeIndex >= 0) {
        recipes[existingRecipeIndex] = recipe;
    } else {
        recipes.push(recipe);
    }
    
    // تحديث تكلفة الإنتاج في المنتج
    product.productionCost = totalMaterialCost / yield_;
    
    displayProducts();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('recipeModal'));
    modal.hide();
    
    alert('تم حفظ الوصفة بنجاح!');
}

// إظهار نافذة الإنتاج
function showProductionModal() {
    document.getElementById('productionForm').reset();
    document.getElementById('productionDateInput').value = new Date().toISOString().split('T')[0];
    document.getElementById('autoUpdateInventory').checked = true;
    populateProductSelects();
    const modal = new bootstrap.Modal(document.getElementById('productionModal'));
    modal.show();
}

// تحديث تكلفة الإنتاج
function updateProductionCost() {
    const productId = parseInt(document.getElementById('productionProductSelect').value);
    const quantity = parseFloat(document.getElementById('productionQuantity').value) || 0;
    
    if (!productId || !quantity) {
        document.getElementById('productionCostCalculated').value = '';
        return;
    }
    
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const unitCost = product.productionCost + product.laborCost + product.overheadCost;
    const totalCost = unitCost * quantity;
    
    document.getElementById('productionCostCalculated').value = `${totalCost.toLocaleString()} ر.ي`;
}

// حفظ الإنتاج
function saveProduction() {
    const productId = parseInt(document.getElementById('productionProductSelect').value);
    const quantity = parseFloat(document.getElementById('productionQuantity').value);
    const date = document.getElementById('productionDateInput').value;
    const shift = document.getElementById('productionShiftSelect').value;
    const supervisor = document.getElementById('productionSupervisor').value;
    const notes = document.getElementById('productionNotes').value;
    const autoUpdate = document.getElementById('autoUpdateInventory').checked;
    
    if (!productId || !quantity || !date || !shift) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const product = products.find(p => p.id === productId);
    const unitCost = product.productionCost + product.laborCost + product.overheadCost;
    const totalCost = unitCost * quantity;
    
    const newProduction = {
        id: production.length + 1,
        productId: productId,
        productName: product.name,
        quantity: quantity,
        unit: product.unit,
        cost: totalCost,
        date: date,
        shift: shift,
        supervisor: supervisor || 'غير محدد',
        notes: notes || ''
    };
    
    production.push(newProduction);
    
    // تحديث المخزون إذا كان مطلوباً
    if (autoUpdate) {
        // هنا يمكن إضافة كود تحديث المخزون
        console.log('تحديث المخزون:', newProduction);
    }
    
    displayProductionData();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('productionModal'));
    modal.hide();
    
    alert('تم تسجيل الإنتاج بنجاح!');
}

// عرض بيانات الإنتاج
function displayProductionData(dataToShow = production) {
    const tbody = document.getElementById('productionTableBody');
    tbody.innerHTML = '';
    
    dataToShow.forEach(prod => {
        const row = document.createElement('tr');
        
        const shiftText = {
            'morning': 'الصباحية',
            'evening': 'المسائية',
            'night': 'الليلية'
        }[prod.shift];
        
        row.innerHTML = `
            <td>${prod.productName}</td>
            <td>${prod.quantity.toLocaleString()}</td>
            <td>${prod.unit}</td>
            <td>${prod.cost.toLocaleString()} ر.ي</td>
            <td><span class="badge bg-info">${shiftText}</span></td>
            <td>${prod.supervisor}</td>
            <td>${prod.notes || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="viewProductionDetails(${prod.id})" title="تفاصيل">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="editProduction(${prod.id})" title="تعديل">
                    <i class="bi bi-pencil"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// تحميل بيانات الإنتاج
function loadProductionData() {
    const date = document.getElementById('productionDate').value;
    const shift = document.getElementById('productionShift').value;
    
    let filtered = production.filter(prod => {
        const matchesDate = !date || prod.date === date;
        const matchesShift = !shift || prod.shift === shift;
        return matchesDate && matchesShift;
    });
    
    displayProductionData(filtered);
}

// إظهار نافذة التالف
function showDamageModal() {
    document.getElementById('damageForm').reset();
    document.getElementById('damageDateInput').value = new Date().toISOString().split('T')[0];
    document.getElementById('createJournalEntry').checked = true;
    populateProductSelects();
    const modal = new bootstrap.Modal(document.getElementById('damageModal'));
    modal.show();
}

// تحديث قيمة التالف
function updateDamageValue() {
    const productId = parseInt(document.getElementById('damageProduct').value);
    const quantity = parseFloat(document.getElementById('damageQuantity').value) || 0;
    
    if (!productId || !quantity) {
        document.getElementById('damageValue').value = '';
        return;
    }
    
    const product = products.find(p => p.id === productId);
    if (!product) return;
    
    const unitCost = product.productionCost + product.laborCost + product.overheadCost;
    const totalValue = unitCost * quantity;
    
    document.getElementById('damageValue').value = `${totalValue.toLocaleString()} ر.ي`;
}

// حفظ التالف
function saveDamage() {
    const productId = parseInt(document.getElementById('damageProduct').value);
    const quantity = parseFloat(document.getElementById('damageQuantity').value);
    const reason = document.getElementById('damageReasonSelect').value;
    const date = document.getElementById('damageDateInput').value;
    const responsible = document.getElementById('damageResponsible').value;
    const notes = document.getElementById('damageNotesInput').value;
    const createJournal = document.getElementById('createJournalEntry').checked;
    
    if (!productId || !quantity || !reason || !date) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    const product = products.find(p => p.id === productId);
    const unitCost = product.productionCost + product.laborCost + product.overheadCost;
    const totalValue = unitCost * quantity;
    
    const reasonText = {
        'expired': 'انتهاء صلاحية',
        'broken': 'كسر',
        'burnt': 'احتراق',
        'contaminated': 'تلوث',
        'other': 'أخرى'
    }[reason];
    
    const newDamage = {
        id: damages.length + 1,
        productId: productId,
        productName: product.name,
        quantity: quantity,
        reason: reason,
        reasonText: reasonText,
        value: totalValue,
        date: date,
        responsible: responsible || 'غير محدد',
        journalEntryId: createJournal ? `JE${String(damages.length + 1).padStart(3, '0')}` : null,
        notes: notes || ''
    };
    
    damages.push(newDamage);
    
    // إنشاء قيد محاسبي إذا كان مطلوباً
    if (createJournal) {
        console.log('إنشاء قيد محاسبي للتالف:', newDamage);
        // هنا يمكن إضافة كود إنشاء القيد المحاسبي
    }
    
    displayDamageData();
    updateStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('damageModal'));
    modal.hide();
    
    alert('تم تسجيل التالف بنجاح!');
}

// عرض بيانات التالف
function displayDamageData(dataToShow = damages) {
    const tbody = document.getElementById('damageTableBody');
    tbody.innerHTML = '';
    
    dataToShow.forEach(damage => {
        const row = document.createElement('tr');
        
        row.innerHTML = `
            <td>${formatDate(damage.date)}</td>
            <td>${damage.productName}</td>
            <td>${damage.quantity.toLocaleString()}</td>
            <td><span class="badge bg-warning">${damage.reasonText}</span></td>
            <td>${damage.value.toLocaleString()} ر.ي</td>
            <td>${damage.responsible}</td>
            <td>${damage.journalEntryId || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-info" onclick="viewDamageDetails(${damage.id})" title="تفاصيل">
                    <i class="bi bi-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-primary" onclick="editDamage(${damage.id})" title="تعديل">
                    <i class="bi bi-pencil"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// تحميل بيانات التالف
function loadDamageData() {
    const date = document.getElementById('damageDate').value;
    const reason = document.getElementById('damageReason').value;
    
    let filtered = damages.filter(damage => {
        const matchesDate = !date || damage.date === date;
        const matchesReason = !reason || damage.reason === reason;
        return matchesDate && matchesReason;
    });
    
    displayDamageData(filtered);
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// وظائف إضافية للعرض والتعديل
function viewRecipe(productId) {
    document.getElementById('recipeProductFilter').value = productId;
    loadRecipeDetails();
    document.querySelector('[data-bs-target="#recipes"]').click();
}

function produceProduct(productId) {
    document.getElementById('productionProductSelect').value = productId;
    updateProductionCost();
    showProductionModal();
}

function editProduct(productId) {
    alert('سيتم فتح نافذة التعديل قريباً');
}

function viewProductionDetails(productionId) {
    const prod = production.find(p => p.id === productionId);
    if (prod) {
        alert(`تفاصيل الإنتاج:
المنتج: ${prod.productName}
الكمية: ${prod.quantity} ${prod.unit}
التكلفة: ${prod.cost} ر.ي
التاريخ: ${formatDate(prod.date)}
الوردية: ${prod.shift}
المشرف: ${prod.supervisor}`);
    }
}

function editProduction(productionId) {
    alert('سيتم فتح نافذة التعديل قريباً');
}

function viewDamageDetails(damageId) {
    const damage = damages.find(d => d.id === damageId);
    if (damage) {
        alert(`تفاصيل التالف:
المنتج: ${damage.productName}
الكمية: ${damage.quantity}
السبب: ${damage.reasonText}
القيمة: ${damage.value} ر.ي
التاريخ: ${formatDate(damage.date)}
المسؤول: ${damage.responsible}`);
    }
}

function editDamage(damageId) {
    alert('سيتم فتح نافذة التعديل قريباً');
}
