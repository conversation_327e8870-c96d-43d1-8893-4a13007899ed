# 📊 تتبع تقدم مشروع نظام المحاسبة للمخبز

## 🎯 نظرة عامة على المشروع
نظام محاسبي شامل للمخبز يدعم العربية RTL مع تقنيات HTML+PHP+MySQL

## 📅 تاريخ البدء
$(date)

## 🏗️ هيكل المشروع المخطط
```
anwarsoft/
├── index.html                 # الصفحة الرئيسية
├── config/
│   ├── database.php          # إعدادات قاعدة البيانات
│   ├── config.php            # الإعدادات العامة
│   └── constants.php         # الثوابت
├── includes/
│   ├── header.php            # رأس الصفحة
│   ├── footer.php            # تذييل الصفحة
│   ├── sidebar.php           # الشريط الجانبي
│   └── functions.php         # الدوال المساعدة
├── auth/
│   ├── login.php             # تسجيل الدخول
│   ├── logout.php            # تسجيل الخروج
│   └── check_auth.php        # فحص المصادقة
├── modules/
│   ├── company/              # بيانات المنشأة
│   ├── accounts/             # شجرة الحسابات
│   ├── users/                # إدارة المستخدمين
│   ├── cash_banks/           # الصناديق والبنوك
│   ├── employees/            # الموظفين والرواتب
│   ├── inventory/            # المخزون والأصناف
│   ├── invoices/             # الفواتير
│   ├── vouchers/             # سندات القبض والصرف
│   ├── assets/               # الأصول والإهلاك
│   └── reports/              # التقارير
├── assets/
│   ├── css/                  # ملفات التنسيق
│   ├── js/                   # ملفات JavaScript
│   └── images/               # الصور
├── database/
│   ├── bakery_accounting.sql # قاعدة البيانات الكاملة
│   └── sample_data.sql       # البيانات التجريبية
└── docs/
    ├── README.md             # دليل المستخدم
    └── INSTALLATION.md       # دليل التثبيت
```

## ✅ المراحل المكتملة

### المرحلة 1: البنية الأساسية ✅
- [x] إنشاء هيكل المجلدات
- [x] إعداد قاعدة البيانات الشاملة (4 أجزاء)
- [x] ملفات الاتصال والإعدادات
- [x] نظام المصادقة الأساسي
- [x] الصفحة الرئيسية التعريفية
- [x] صفحة تسجيل الدخول المتقدمة
- [x] نظام الصلاحيات والأمان

### المرحلة 2: الوحدات الأساسية
- [x] 1. بيانات النظام والمنشأة ✅
- [x] 2. شجرة الحسابات المحاسبية ✅
- [x] 3. إدارة المستخدمين والصلاحيات ✅
- [ ] 4. إدارة الصناديق والبنوك
- [ ] 5. إدارة الموظفين والرواتب
- [ ] 6. إدارة الأصناف والمنتجات
- [ ] 7. إدارة الفواتير
- [ ] 8. سندات القبض والصرف
- [ ] 9. إدارة الأصول والإهلاك
- [ ] 10. وحدة التقارير
- [ ] 11. واجهة المستخدم النهائية

## 🔗 ربط قاعدة البيانات

### الجداول المنشأة:
1. **company_settings** - إعدادات المنشأة
2. **users** - المستخدمين والصلاحيات
3. **user_logs** - سجل عمليات المستخدمين
4. **chart_of_accounts** - شجرة الحسابات المحاسبية
5. **journal_entries** - القيود المحاسبية
6. **journal_entry_details** - تفاصيل القيود المحاسبية
7. **cash_banks** - الصناديق والبنوك
8. **employees** - بيانات الموظفين
9. **employee_salaries** - رواتب الموظفين
10. **units** - وحدات القياس
11. **item_categories** - فئات الأصناف
12. **items** - الأصناف (خامات/منتجات/خدمات)
13. **product_recipes** - وصفات المنتجات
14. **customers** - العملاء
15. **suppliers** - الموردين
16. **invoices** - الفواتير
17. **invoice_details** - تفاصيل الفواتير
18. **vouchers** - سندات القبض والصرف
19. **fixed_assets** - الأصول الثابتة
20. **asset_depreciation** - إهلاك الأصول
21. **inventory_movements** - حركة المخزون
22. **production_orders** - أوامر الإنتاج

### الملفات المنشأة:
- **database/bakery_accounting.sql** - الجداول الأساسية
- **database/bakery_accounting_part2.sql** - جداول الأصناف والعملاء
- **database/bakery_accounting_part3.sql** - جداول الفواتير والسندات
- **database/bakery_accounting_part4.sql** - جداول الأصول والإنتاج
- **database/sample_data.sql** - البيانات التجريبية
- **config/database.php** - كلاس الاتصال بقاعدة البيانات
- **config/config.php** - الإعدادات العامة والدوال المساعدة
- **config/constants.php** - الثوابت والتعريفات
- **auth/check_auth.php** - نظام المصادقة والصلاحيات
- **auth/login.php** - صفحة تسجيل الدخول
- **auth/logout.php** - صفحة تسجيل الخروج
- **index.html** - الصفحة الرئيسية التعريفية
- **assets/css/style.css** - ملف التنسيقات الرئيسي
- **assets/js/app.js** - ملف JavaScript الرئيسي
- **docs/README.md** - دليل المستخدم الشامل
- **docs/INSTALLATION.md** - دليل التثبيت المفصل
- **database/install_database.sql** - ملف التثبيت الكامل
- **database/warehouses_update.sql** - تحديث المخازن المتعددة
- **includes/functions.php** - الدوال المساعدة العامة
- **includes/header.php** - رأس الصفحة مع القائمة
- **includes/footer.php** - تذييل الصفحة مع JavaScript
- **modules/company/settings.php** - صفحة إعدادات المنشأة
- **dashboard.php** - لوحة التحكم الرئيسية
- **modules/accounts/index.php** - عرض شجرة الحسابات
- **modules/accounts/create.php** - إضافة حساب جديد
- **modules/accounts/view.php** - عرض تفاصيل الحساب
- **modules/accounts/edit.php** - تعديل الحساب
- **modules/users/index.php** - إدارة المستخدمين
- **modules/users/create.php** - إضافة مستخدم جديد
- **index.php** - الصفحة الرئيسية (إعادة توجيه)
- **.htaccess** - ملف الحماية والتوجيه

## 📝 ملاحظات التطوير
- العملة: الريال اليمني فقط
- اللغة: العربية RTL
- المعايير المحاسبية: صارمة ودقيقة
- القيود التلقائية: لجميع العمليات
- البيانات التجريبية: متوفرة لكل وحدة

## 🚨 نقاط مهمة
- عدم حذف الحسابات المرتبطة بالقيود عند إعادة الضبط
- جميع العمليات تنشئ قيود محاسبية تلقائية
- التحقق من التوازن المحاسبي في كل عملية
- دعم الطباعة العادية والحرارية
- نظام صلاحيات دقيق للمستخدمين
