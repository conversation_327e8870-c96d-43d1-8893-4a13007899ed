<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة المستخدمين</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: white !important;
            font-weight: 500;
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }

        .stats-card .number {
            font-size: 1.5rem;
            font-weight: 700;
            margin-bottom: 0.25rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
            font-size: 0.9rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }

        .action-buttons .btn {
            margin: 0 2px;
        }

        .avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html" class="active">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
        <!-- العنوان والأزرار -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-people text-primary me-2"></i>
                إدارة المستخدمين
            </h1>
            <div class="btn-group">
                <button type="button" class="btn btn-primary" onclick="showAddUserModal()">
                    <i class="bi bi-person-plus me-1"></i>إضافة مستخدم جديد
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="refreshUsers()">
                    <i class="bi bi-arrow-clockwise me-1"></i>تحديث
                </button>
            </div>
        </div>

        <!-- إحصائيات المستخدمين -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #17a2b8;">
                    <div class="icon text-info">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="number text-info" id="totalUsers">12</div>
                    <div class="label">إجمالي المستخدمين</div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #28a745;">
                    <div class="icon text-success">
                        <i class="bi bi-person-check"></i>
                    </div>
                    <div class="number text-success" id="activeUsers">10</div>
                    <div class="label">نشط</div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #dc3545;">
                    <div class="icon text-danger">
                        <i class="bi bi-person-x"></i>
                    </div>
                    <div class="number text-danger" id="inactiveUsers">2</div>
                    <div class="label">غير نشط</div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #6f42c1;">
                    <div class="icon text-primary">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <div class="number text-primary" id="adminUsers">2</div>
                    <div class="label">مديرين</div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #fd7e14;">
                    <div class="icon text-warning">
                        <i class="bi bi-calculator"></i>
                    </div>
                    <div class="number text-warning" id="accountantUsers">4</div>
                    <div class="label">محاسبين</div>
                </div>
            </div>

            <div class="col-lg-2 col-md-4 col-sm-6">
                <div class="stats-card" style="border-left-color: #20c997;">
                    <div class="icon text-info">
                        <i class="bi bi-cash-stack"></i>
                    </div>
                    <div class="number text-info" id="cashierUsers">3</div>
                    <div class="label">أمناء صندوق</div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label for="searchInput" class="form-label">البحث</label>
                        <input type="text" class="form-control" id="searchInput" placeholder="البحث في المستخدمين...">
                    </div>
                    <div class="col-md-3">
                        <label for="roleFilter" class="form-label">الدور</label>
                        <select class="form-select" id="roleFilter">
                            <option value="">جميع الأدوار</option>
                            <option value="admin">مدير النظام</option>
                            <option value="manager">مدير</option>
                            <option value="accountant">محاسب</option>
                            <option value="cashier">أمين صندوق</option>
                            <option value="user">مستخدم</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="statusFilter" class="form-label">الحالة</label>
                        <select class="form-select" id="statusFilter">
                            <option value="">جميع الحالات</option>
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="bi bi-x-lg me-1"></i>مسح الفلاتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- جدول المستخدمين -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list-ul me-2"></i>قائمة المستخدمين
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="usersTable">
                        <thead>
                            <tr>
                                <th>الصورة</th>
                                <th>اسم المستخدم</th>
                                <th>الاسم الكامل</th>
                                <th>البريد الإلكتروني</th>
                                <th>الهاتف</th>
                                <th>الدور</th>
                                <th>آخر دخول</th>
                                <th>الحالة</th>
                                <th>العمليات</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- نافذة إضافة مستخدم جديد -->
    <div class="modal fade" id="addUserModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">إضافة مستخدم جديد</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addUserForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="username" class="form-label">اسم المستخدم *</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>

                                <div class="mb-3">
                                    <label for="fullName" class="form-label">الاسم الكامل *</label>
                                    <input type="text" class="form-control" id="fullName" required>
                                </div>

                                <div class="mb-3">
                                    <label for="email" class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" id="email">
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="password" class="form-label">كلمة المرور *</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>

                                <div class="mb-3">
                                    <label for="phone" class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" id="phone">
                                </div>

                                <div class="mb-3">
                                    <label for="role" class="form-label">الدور *</label>
                                    <select class="form-select" id="role" required>
                                        <option value="">اختر الدور</option>
                                        <option value="admin">مدير النظام</option>
                                        <option value="manager">مدير</option>
                                        <option value="accountant">محاسب</option>
                                        <option value="cashier">أمين صندوق</option>
                                        <option value="user">مستخدم</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="isActive" checked>
                            <label class="form-check-label" for="isActive">
                                مستخدم نشط
                            </label>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-primary" onclick="saveUser()">حفظ المستخدم</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // بيانات المستخدمين التجريبية
        let users = [
            {
                id: 1,
                username: 'admin',
                fullName: 'مدير النظام',
                email: '<EMAIL>',
                phone: '*********',
                role: 'admin',
                lastLogin: '2024-01-15 10:30:00',
                isActive: true,
                avatar: 'A'
            },
            {
                id: 2,
                username: 'accountant',
                fullName: 'أحمد المحاسب',
                email: '<EMAIL>',
                phone: '*********',
                role: 'accountant',
                lastLogin: '2024-01-15 09:15:00',
                isActive: true,
                avatar: 'أ'
            },
            {
                id: 3,
                username: 'cashier',
                fullName: 'فاطمة أمين الصندوق',
                email: '<EMAIL>',
                phone: '*********',
                role: 'cashier',
                lastLogin: '2024-01-14 16:45:00',
                isActive: true,
                avatar: 'ف'
            },
            {
                id: 4,
                username: 'manager',
                fullName: 'محمد المدير',
                email: '<EMAIL>',
                phone: '*********',
                role: 'manager',
                lastLogin: '2024-01-15 08:00:00',
                isActive: true,
                avatar: 'م'
            },
            {
                id: 5,
                username: 'user1',
                fullName: 'سالم المستخدم',
                email: '<EMAIL>',
                phone: '777567890',
                role: 'user',
                lastLogin: null,
                isActive: false,
                avatar: 'س'
            }
        ];

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // حفظ حالة القائمة
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // عرض المستخدمين
        function displayUsers(usersToShow = users) {
            const tbody = document.getElementById('usersTableBody');
            tbody.innerHTML = '';

            usersToShow.forEach(user => {
                const row = document.createElement('tr');

                const roleClass = {
                    'admin': 'danger',
                    'manager': 'primary',
                    'accountant': 'warning',
                    'cashier': 'info',
                    'user': 'secondary'
                }[user.role];

                const roleText = {
                    'admin': 'مدير النظام',
                    'manager': 'مدير',
                    'accountant': 'محاسب',
                    'cashier': 'أمين صندوق',
                    'user': 'مستخدم'
                }[user.role];

                const avatarColor = {
                    'admin': '#dc3545',
                    'manager': '#0d6efd',
                    'accountant': '#ffc107',
                    'cashier': '#0dcaf0',
                    'user': '#6c757d'
                }[user.role];

                row.innerHTML = `
                    <td>
                        <div class="avatar" style="background-color: ${avatarColor};">
                            ${user.avatar}
                        </div>
                    </td>
                    <td>
                        <strong>${user.username}</strong>
                        ${user.id === 1 ? '<span class="badge bg-warning ms-1">رئيسي</span>' : ''}
                    </td>
                    <td>${user.fullName}</td>
                    <td>
                        ${user.email ? `<a href="mailto:${user.email}" class="text-decoration-none">${user.email}</a>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td>
                        ${user.phone ? `<a href="tel:${user.phone}" class="text-decoration-none">${user.phone}</a>` : '<span class="text-muted">-</span>'}
                    </td>
                    <td><span class="badge bg-${roleClass}">${roleText}</span></td>
                    <td>
                        ${user.lastLogin ? `<small class="text-muted">${new Date(user.lastLogin).toLocaleDateString('ar-SA')}</small>` : '<span class="text-muted">لم يسجل دخول</span>'}
                    </td>
                    <td><span class="badge bg-${user.isActive ? 'success' : 'secondary'}">${user.isActive ? 'نشط' : 'غير نشط'}</span></td>
                    <td>
                        <div class="action-buttons">
                            <button class="btn btn-sm btn-outline-info" onclick="viewUser(${user.id})" title="عرض">
                                <i class="bi bi-eye"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.id})" title="تعديل">
                                <i class="bi bi-pencil"></i>
                            </button>
                            ${user.id !== 1 ? `
                                <button class="btn btn-sm btn-outline-${user.isActive ? 'warning' : 'success'}" onclick="toggleUser(${user.id})" title="${user.isActive ? 'إلغاء تفعيل' : 'تفعيل'}">
                                    <i class="bi bi-${user.isActive ? 'person-x' : 'person-check'}"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.id})" title="حذف">
                                    <i class="bi bi-trash"></i>
                                </button>
                            ` : ''}
                        </div>
                    </td>
                `;

                tbody.appendChild(row);
            });
        }

        // تحديث الإحصائيات
        function updateStats() {
            const stats = {
                total: users.length,
                active: users.filter(u => u.isActive).length,
                inactive: users.filter(u => !u.isActive).length,
                admin: users.filter(u => u.role === 'admin').length,
                manager: users.filter(u => u.role === 'manager').length,
                accountant: users.filter(u => u.role === 'accountant').length,
                cashier: users.filter(u => u.role === 'cashier').length,
                user: users.filter(u => u.role === 'user').length
            };

            document.getElementById('totalUsers').textContent = stats.total;
            document.getElementById('activeUsers').textContent = stats.active;
            document.getElementById('inactiveUsers').textContent = stats.inactive;
            document.getElementById('adminUsers').textContent = stats.admin;
            document.getElementById('accountantUsers').textContent = stats.accountant;
            document.getElementById('cashierUsers').textContent = stats.cashier;
        }

        // فلترة المستخدمين
        function filterUsers() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const roleFilter = document.getElementById('roleFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = users.filter(user => {
                const matchesSearch = !searchTerm ||
                    user.username.toLowerCase().includes(searchTerm) ||
                    user.fullName.toLowerCase().includes(searchTerm) ||
                    (user.email && user.email.toLowerCase().includes(searchTerm));

                const matchesRole = !roleFilter || user.role === roleFilter;
                const matchesStatus = !statusFilter ||
                    (statusFilter === 'active' && user.isActive) ||
                    (statusFilter === 'inactive' && !user.isActive);

                return matchesSearch && matchesRole && matchesStatus;
            });

            displayUsers(filtered);
        }

        // مسح الفلاتر
        function clearFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('roleFilter').value = '';
            document.getElementById('statusFilter').value = '';
            displayUsers();
        }

        // إظهار نافذة إضافة مستخدم
        function showAddUserModal() {
            const modal = new bootstrap.Modal(document.getElementById('addUserModal'));
            modal.show();
        }

        // حفظ مستخدم جديد
        function saveUser() {
            const newUser = {
                id: users.length + 1,
                username: document.getElementById('username').value,
                fullName: document.getElementById('fullName').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                role: document.getElementById('role').value,
                lastLogin: null,
                isActive: document.getElementById('isActive').checked,
                avatar: document.getElementById('fullName').value.charAt(0)
            };

            users.push(newUser);
            displayUsers();
            updateStats();

            const modal = bootstrap.Modal.getInstance(document.getElementById('addUserModal'));
            modal.hide();

            document.getElementById('addUserForm').reset();
            alert('تم إضافة المستخدم بنجاح!');
        }

        // عرض تفاصيل المستخدم
        function viewUser(id) {
            const user = users.find(u => u.id === id);
            if (user) {
                alert(`تفاصيل المستخدم:\nالاسم: ${user.fullName}\nاسم المستخدم: ${user.username}\nالدور: ${user.role}\nالحالة: ${user.isActive ? 'نشط' : 'غير نشط'}`);
            }
        }

        // تعديل المستخدم
        function editUser(id) {
            alert('سيتم فتح نافذة التعديل قريباً');
        }

        // تفعيل/إلغاء تفعيل المستخدم
        function toggleUser(id) {
            const user = users.find(u => u.id === id);
            if (user) {
                user.isActive = !user.isActive;
                displayUsers();
                updateStats();
                alert(`تم ${user.isActive ? 'تفعيل' : 'إلغاء تفعيل'} المستخدم بنجاح!`);
            }
        }

        // حذف المستخدم
        function deleteUser(id) {
            if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
                users = users.filter(u => u.id !== id);
                displayUsers();
                updateStats();
                alert('تم حذف المستخدم بنجاح!');
            }
        }

        // تحديث المستخدمين
        function refreshUsers() {
            displayUsers();
            updateStats();
            alert('تم تحديث البيانات!');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من المصادقة
            const user = checkAuth();
            if (user) {
                document.getElementById('sidebarUserName').textContent = user.name;
            }

            // استعادة حالة القائمة الجانبية
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }

            displayUsers();
            updateStats();

            // ربط أحداث الفلترة
            document.getElementById('searchInput').addEventListener('input', filterUsers);
            document.getElementById('roleFilter').addEventListener('change', filterUsers);
            document.getElementById('statusFilter').addEventListener('change', filterUsers);
        });
    </script>
</body>
</html>
