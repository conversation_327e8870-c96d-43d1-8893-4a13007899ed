// وظائف إدارة المخازن

// عرض قائمة المخازن
function displayWarehouses(dataToShow = warehouses) {
    const container = document.getElementById('warehousesList');
    container.innerHTML = '';
    
    dataToShow.forEach(warehouse => {
        const warehouseDiv = document.createElement('div');
        warehouseDiv.className = `warehouse-card ${warehouse.isActive ? 'warehouse-active' : 'warehouse-inactive'}`;
        
        const typeText = {
            'main': 'رئيسي',
            'branch': 'فرعي',
            'production': 'إنتاج',
            'damaged': 'تالف'
        }[warehouse.type];
        
        const statusText = warehouse.isActive ? 'نشط' : 'غير نشط';
        const statusClass = warehouse.isActive ? 'success' : 'danger';
        
        warehouseDiv.innerHTML = `
            <div class="warehouse-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1">
                            <strong>${warehouse.name}</strong>
                            <span class="badge bg-secondary ms-2">${warehouse.code}</span>
                        </h6>
                        <small class="text-muted">${typeText} | أمين المخزن: ${warehouse.manager}</small>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-${statusClass}">${statusText}</span>
                        <div class="mt-1">
                            <small class="text-muted">السعة: ${warehouse.capacity} م³</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row mb-3">
                <div class="col-md-6">
                    <strong>العنوان:</strong> ${warehouse.address || 'غير محدد'}
                    <br><strong>الهاتف:</strong> ${warehouse.phone || 'غير محدد'}
                </div>
                <div class="col-md-6">
                    <strong>تاريخ الإنشاء:</strong> ${formatDate(warehouse.createdDate)}
                    <br><strong>الرصيد السالب:</strong> ${warehouse.allowNegative ? 'مسموح' : 'غير مسموح'}
                </div>
            </div>
            
            ${warehouse.notes ? `
                <div class="mb-3">
                    <strong>ملاحظات:</strong> ${warehouse.notes}
                </div>
            ` : ''}
            
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        عدد الأصناف: <span class="fw-bold">0</span> |
                        القيمة الإجمالية: <span class="fw-bold">0 ر.ي</span>
                    </small>
                </div>
                <div class="action-buttons">
                    <button class="btn btn-sm btn-outline-info" onclick="viewWarehouseDetails(${warehouse.id})" title="تفاصيل">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="editWarehouse(${warehouse.id})" title="تعديل">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="showWarehouseStock(${warehouse.id})" title="المخزون">
                        <i class="bi bi-box-seam"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="showTransferModal(${warehouse.id})" title="نقل">
                        <i class="bi bi-arrow-left-right"></i>
                    </button>
                    ${warehouse.isActive ? `
                        <button class="btn btn-sm btn-outline-danger" onclick="deactivateWarehouse(${warehouse.id})" title="إلغاء تفعيل">
                            <i class="bi bi-x-circle"></i>
                        </button>
                    ` : `
                        <button class="btn btn-sm btn-outline-success" onclick="activateWarehouse(${warehouse.id})" title="تفعيل">
                            <i class="bi bi-check-circle"></i>
                        </button>
                    `}
                </div>
            </div>
        `;
        
        container.appendChild(warehouseDiv);
    });
}

// تحديث إحصائيات المخازن
function updateWarehouseStats() {
    const totalWarehouses = warehouses.length;
    const activeWarehouses = warehouses.filter(w => w.isActive).length;
    const totalItems = 1245; // سيتم حسابها من قاعدة البيانات
    const lowStockItems = 23; // سيتم حسابها من قاعدة البيانات
    
    document.getElementById('totalWarehouses').textContent = totalWarehouses;
    document.getElementById('activeWarehouses').textContent = activeWarehouses;
    document.getElementById('totalItems').textContent = totalItems.toLocaleString();
    document.getElementById('lowStockItems').textContent = lowStockItems;
}

// إظهار نافذة إضافة مخزن
function showAddWarehouseModal() {
    document.getElementById('addWarehouseForm').reset();
    document.getElementById('warehouseCode').value = generateWarehouseCode();
    const modal = new bootstrap.Modal(document.getElementById('addWarehouseModal'));
    modal.show();
}

// إظهار نافذة نقل بين المخازن
function showWarehouseTransferModal() {
    populateWarehouseSelects();
    populateItemSelect();
    document.getElementById('transferDate').value = new Date().toISOString().split('T')[0];
    const modal = new bootstrap.Modal(document.getElementById('warehouseTransferModal'));
    modal.show();
}

// ملء قوائم المخازن
function populateWarehouseSelects() {
    const activeWarehouses = warehouses.filter(w => w.isActive);
    const selects = ['fromWarehouse', 'toWarehouse'];
    
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        select.innerHTML = '<option value="">اختر المخزن</option>';
        
        activeWarehouses.forEach(warehouse => {
            const option = document.createElement('option');
            option.value = warehouse.id;
            option.textContent = `${warehouse.name} (${warehouse.code})`;
            select.appendChild(option);
        });
    });
}

// ملء قائمة الأصناف
function populateItemSelect() {
    const select = document.getElementById('transferItem');
    select.innerHTML = '<option value="">اختر الصنف</option>';
    
    // إضافة المواد الخام
    if (typeof items !== 'undefined') {
        items.forEach(item => {
            const option = document.createElement('option');
            option.value = `item_${item.id}`;
            option.textContent = `${item.name} (${item.code})`;
            option.dataset.type = 'item';
            select.appendChild(option);
        });
    }
    
    // إضافة المنتجات
    if (typeof products !== 'undefined') {
        products.forEach(product => {
            const option = document.createElement('option');
            option.value = `product_${product.id}`;
            option.textContent = `${product.name} (${product.code})`;
            option.dataset.type = 'product';
            select.appendChild(option);
        });
    }
}

// حفظ مخزن جديد
function saveWarehouse() {
    const form = document.getElementById('addWarehouseForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // التحقق من عدم تكرار الكود
    const code = document.getElementById('warehouseCode').value;
    if (warehouses.some(w => w.code === code)) {
        alert('كود المخزن موجود مسبقاً. يرجى اختيار كود آخر.');
        return;
    }
    
    const newWarehouse = {
        id: warehouses.length + 1,
        name: document.getElementById('warehouseName').value,
        code: code,
        type: document.getElementById('warehouseType').value,
        manager: document.getElementById('warehouseManager').options[document.getElementById('warehouseManager').selectedIndex].text || '',
        address: document.getElementById('warehouseAddress').value,
        phone: document.getElementById('warehousePhone').value,
        capacity: parseFloat(document.getElementById('warehouseCapacity').value) || 0,
        isActive: document.getElementById('warehouseIsActive').checked,
        allowNegative: document.getElementById('warehouseAllowNegative').checked,
        notes: document.getElementById('warehouseNotes').value,
        createdDate: new Date().toISOString().split('T')[0]
    };
    
    warehouses.push(newWarehouse);
    
    // حفظ في localStorage
    localStorage.setItem('warehouses', JSON.stringify(warehouses));
    
    displayWarehouses();
    updateWarehouseStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addWarehouseModal'));
    modal.hide();
    
    alert('تم حفظ المخزن بنجاح!');
}

// تنفيذ النقل بين المخازن
function executeTransfer() {
    const form = document.getElementById('warehouseTransferForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const fromWarehouseId = document.getElementById('fromWarehouse').value;
    const toWarehouseId = document.getElementById('toWarehouse').value;
    const itemId = document.getElementById('transferItem').value;
    const quantity = parseFloat(document.getElementById('transferQuantity').value);
    
    if (fromWarehouseId === toWarehouseId) {
        alert('لا يمكن النقل من وإلى نفس المخزن');
        return;
    }
    
    // التحقق من توفر الكمية في المخزن المصدر
    // هذا سيتم تنفيذه عند ربط قاعدة البيانات
    
    // إنشاء سجل النقل
    const transfer = {
        id: Date.now(),
        fromWarehouseId: parseInt(fromWarehouseId),
        toWarehouseId: parseInt(toWarehouseId),
        itemId: itemId,
        quantity: quantity,
        date: document.getElementById('transferDate').value,
        reference: document.getElementById('transferReference').value,
        notes: document.getElementById('transferNotes').value,
        createdBy: 'المستخدم الحالي',
        createdDate: new Date().toISOString()
    };
    
    // حفظ سجل النقل
    let transfers = JSON.parse(localStorage.getItem('warehouseTransfers') || '[]');
    transfers.push(transfer);
    localStorage.setItem('warehouseTransfers', JSON.stringify(transfers));
    
    // إنشاء قيد محاسبي للنقل
    createTransferJournalEntry(transfer);
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('warehouseTransferModal'));
    modal.hide();
    
    alert('تم تنفيذ النقل بنجاح!');
}

// إنشاء قيد محاسبي للنقل
function createTransferJournalEntry(transfer) {
    if (typeof journalEntries === 'undefined') return;
    
    const fromWarehouse = warehouses.find(w => w.id === transfer.fromWarehouseId);
    const toWarehouse = warehouses.find(w => w.id === transfer.toWarehouseId);
    
    const newEntry = {
        id: journalEntries.length + 1,
        number: `JE-TR-${transfer.id}`,
        date: transfer.date,
        type: 'auto',
        description: `قيد نقل بين المخازن - من ${fromWarehouse.name} إلى ${toWarehouse.name}`,
        reference: transfer.reference || `TR-${transfer.id}`,
        status: 'approved',
        createdBy: 'النظام',
        reviewedBy: null,
        isBalanced: true,
        totalAmount: 0, // سيتم حساب القيمة عند ربط الأسعار
        details: [
            { accountId: 1, accountName: `مخزون ${toWarehouse.name}`, debit: 0, credit: 0 },
            { accountId: 2, accountName: `مخزون ${fromWarehouse.name}`, debit: 0, credit: 0 }
        ]
    };
    
    journalEntries.push(newEntry);
}

// إنشاء كود مخزن تلقائي
function generateWarehouseCode() {
    const year = new Date().getFullYear().toString().slice(-2);
    const nextNumber = warehouses.length + 1;
    return `WH${year}${String(nextNumber).padStart(3, '0')}`;
}

// البحث في المخازن
function searchWarehouses() {
    const searchTerm = document.getElementById('warehouseSearchInput').value.toLowerCase();
    const typeFilter = document.getElementById('warehouseTypeFilter').value;
    const statusFilter = document.getElementById('warehouseStatusFilter').value;
    
    let filtered = warehouses.filter(warehouse => {
        const matchesSearch = !searchTerm || 
            warehouse.name.toLowerCase().includes(searchTerm) ||
            warehouse.code.toLowerCase().includes(searchTerm) ||
            warehouse.manager.toLowerCase().includes(searchTerm);
        
        const matchesType = !typeFilter || warehouse.type === typeFilter;
        const matchesStatus = !statusFilter || 
            (statusFilter === 'active' && warehouse.isActive) ||
            (statusFilter === 'inactive' && !warehouse.isActive);
        
        return matchesSearch && matchesType && matchesStatus;
    });
    
    displayWarehouses(filtered);
}

// تعديل مخزن
function editWarehouse(warehouseId) {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    if (!warehouse) return;
    
    // ملء النموذج بالبيانات الحالية
    document.getElementById('warehouseName').value = warehouse.name;
    document.getElementById('warehouseCode').value = warehouse.code;
    document.getElementById('warehouseType').value = warehouse.type;
    document.getElementById('warehouseAddress').value = warehouse.address || '';
    document.getElementById('warehousePhone').value = warehouse.phone || '';
    document.getElementById('warehouseCapacity').value = warehouse.capacity || '';
    document.getElementById('warehouseIsActive').checked = warehouse.isActive;
    document.getElementById('warehouseAllowNegative').checked = warehouse.allowNegative;
    document.getElementById('warehouseNotes').value = warehouse.notes || '';
    
    // تغيير عنوان النافذة
    document.querySelector('#addWarehouseModal .modal-title').textContent = 'تعديل المخزن';
    
    // تغيير وظيفة الحفظ
    const saveButton = document.querySelector('#addWarehouseModal .btn-primary');
    saveButton.onclick = () => updateWarehouse(warehouseId);
    
    const modal = new bootstrap.Modal(document.getElementById('addWarehouseModal'));
    modal.show();
}

// تحديث مخزن
function updateWarehouse(warehouseId) {
    const warehouseIndex = warehouses.findIndex(w => w.id === warehouseId);
    if (warehouseIndex === -1) return;
    
    const form = document.getElementById('addWarehouseForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    // تحديث البيانات
    warehouses[warehouseIndex] = {
        ...warehouses[warehouseIndex],
        name: document.getElementById('warehouseName').value,
        code: document.getElementById('warehouseCode').value,
        type: document.getElementById('warehouseType').value,
        address: document.getElementById('warehouseAddress').value,
        phone: document.getElementById('warehousePhone').value,
        capacity: parseFloat(document.getElementById('warehouseCapacity').value) || 0,
        isActive: document.getElementById('warehouseIsActive').checked,
        allowNegative: document.getElementById('warehouseAllowNegative').checked,
        notes: document.getElementById('warehouseNotes').value
    };
    
    // حفظ في localStorage
    localStorage.setItem('warehouses', JSON.stringify(warehouses));
    
    displayWarehouses();
    updateWarehouseStats();
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('addWarehouseModal'));
    modal.hide();
    
    // إعادة تعيين النافذة للإضافة
    document.querySelector('#addWarehouseModal .modal-title').textContent = 'إضافة مخزن جديد';
    document.querySelector('#addWarehouseModal .btn-primary').onclick = saveWarehouse;
    
    alert('تم تحديث المخزن بنجاح!');
}

// تفعيل/إلغاء تفعيل المخزن
function activateWarehouse(warehouseId) {
    updateWarehouseStatus(warehouseId, true);
}

function deactivateWarehouse(warehouseId) {
    if (confirm('هل أنت متأكد من إلغاء تفعيل هذا المخزن؟')) {
        updateWarehouseStatus(warehouseId, false);
    }
}

function updateWarehouseStatus(warehouseId, isActive) {
    const warehouseIndex = warehouses.findIndex(w => w.id === warehouseId);
    if (warehouseIndex === -1) return;
    
    warehouses[warehouseIndex].isActive = isActive;
    
    // حفظ في localStorage
    localStorage.setItem('warehouses', JSON.stringify(warehouses));
    
    displayWarehouses();
    updateWarehouseStats();
    
    const statusText = isActive ? 'تم تفعيل' : 'تم إلغاء تفعيل';
    alert(`${statusText} المخزن بنجاح!`);
}

// عرض تفاصيل المخزن
function viewWarehouseDetails(warehouseId) {
    const warehouse = warehouses.find(w => w.id === warehouseId);
    if (!warehouse) return;
    
    alert(`تفاصيل المخزن:
الاسم: ${warehouse.name}
الكود: ${warehouse.code}
النوع: ${warehouse.type}
أمين المخزن: ${warehouse.manager}
الحالة: ${warehouse.isActive ? 'نشط' : 'غير نشط'}`);
}

// عرض مخزون المخزن
function showWarehouseStock(warehouseId) {
    alert(`سيتم عرض مخزون المخزن رقم ${warehouseId}`);
}

// إظهار نافذة النقل لمخزن محدد
function showTransferModal(warehouseId) {
    showWarehouseTransferModal();
    document.getElementById('fromWarehouse').value = warehouseId;
}

// تنسيق التاريخ
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

// تحميل البيانات من localStorage
function loadWarehousesFromStorage() {
    const stored = localStorage.getItem('warehouses');
    if (stored) {
        warehouses = JSON.parse(stored);
    }
}
