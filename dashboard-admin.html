<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - مدير النظام</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
        }

        .navbar-nav .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .navbar-nav .nav-link:hover {
            color: white !important;
            transform: translateY(-1px);
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 4px solid;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .stats-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        .stats-card .number {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .stats-card .label {
            color: #6c757d;
            font-weight: 500;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .quick-action {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
            display: block;
            margin-bottom: 1rem;
        }

        .quick-action:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            color: inherit;
            text-decoration: none;
        }

        .quick-action .icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }

        .welcome-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .table {
            border-radius: 10px;
            overflow: hidden;
        }

        .badge {
            font-size: 0.75rem;
            padding: 0.5rem 0.75rem;
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html" class="active">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
        <!-- قسم الترحيب -->
        <div class="welcome-section">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2><i class="bi bi-emoji-smile me-2"></i>مرحباً بك، <span id="welcomeName">مدير النظام</span></h2>
                    <p class="mb-0">إليك نظرة سريعة على أداء النظام اليوم</p>
                </div>
                <div class="col-md-4 text-end">
                    <h4 id="currentDate"></h4>
                    <p class="mb-0" id="currentTime"></p>
                </div>
            </div>
        </div>

        <!-- الإحصائيات الرئيسية -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="border-left-color: #28a745;">
                    <div class="icon text-success">
                        <i class="bi bi-cash-stack"></i>
                    </div>
                    <div class="number text-success" id="totalRevenue">125,450</div>
                    <div class="label">إجمالي الإيرادات (ر.ي)</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="border-left-color: #dc3545;">
                    <div class="icon text-danger">
                        <i class="bi bi-graph-down"></i>
                    </div>
                    <div class="number text-danger" id="totalExpenses">45,230</div>
                    <div class="label">إجمالي المصروفات (ر.ي)</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="border-left-color: #17a2b8;">
                    <div class="icon text-info">
                        <i class="bi bi-receipt"></i>
                    </div>
                    <div class="number text-info" id="totalInvoices">1,247</div>
                    <div class="label">عدد الفواتير</div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6">
                <div class="stats-card" style="border-left-color: #ffc107;">
                    <div class="icon text-warning">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="number text-warning" id="totalUsers">12</div>
                    <div class="label">عدد المستخدمين</div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- العمليات السريعة -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-lightning me-2"></i>العمليات السريعة
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6">
                                <a href="invoice-create.html" class="quick-action">
                                    <div class="icon text-primary">
                                        <i class="bi bi-receipt"></i>
                                    </div>
                                    <h6>فاتورة جديدة</h6>
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="voucher-create.html" class="quick-action">
                                    <div class="icon text-success">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                    <h6>سند قبض</h6>
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="expense-create.html" class="quick-action">
                                    <div class="icon text-danger">
                                        <i class="bi bi-file-earmark-minus"></i>
                                    </div>
                                    <h6>سند صرف</h6>
                                </a>
                            </div>
                            <div class="col-6">
                                <a href="accounts.html" class="quick-action">
                                    <div class="icon text-info">
                                        <i class="bi bi-diagram-3"></i>
                                    </div>
                                    <h6>حساب جديد</h6>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- آخر الفواتير -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>آخر الفواتير
                        </h5>
                        <a href="invoices.html" class="btn btn-sm btn-light">عرض الكل</a>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>رقم الفاتورة</th>
                                        <th>العميل</th>
                                        <th>المبلغ</th>
                                        <th>التاريخ</th>
                                        <th>الحالة</th>
                                    </tr>
                                </thead>
                                <tbody id="recentInvoices">
                                    <tr>
                                        <td><strong>INV-001</strong></td>
                                        <td>أحمد محمد</td>
                                        <td>2,500 ر.ي</td>
                                        <td>اليوم</td>
                                        <td><span class="badge bg-success">مدفوعة</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>INV-002</strong></td>
                                        <td>فاطمة علي</td>
                                        <td>1,800 ر.ي</td>
                                        <td>أمس</td>
                                        <td><span class="badge bg-warning">معلقة</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>INV-003</strong></td>
                                        <td>محمد سالم</td>
                                        <td>3,200 ر.ي</td>
                                        <td>أمس</td>
                                        <td><span class="badge bg-success">مدفوعة</span></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- إحصائيات إضافية -->
        <div class="row mt-4">
            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>أداء المبيعات الشهرية
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="salesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart me-2"></i>توزيع المصروفات
                        </h5>
                    </div>
                    <div class="card-body">
                        <canvas id="expensesChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>
        </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // التحقق من تسجيل الدخول
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            localStorage.removeItem('rememberLogin');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            // حفظ حالة القائمة
            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // تحديث الوقت والتاريخ
        function updateDateTime() {
            const now = new Date();
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };

            document.getElementById('currentDate').textContent = now.toLocaleDateString('ar-SA', dateOptions);
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('ar-SA', timeOptions);
        }

        // تحديث الإحصائيات (محاكاة)
        function updateStats() {
            // محاكاة بيانات متغيرة
            const revenue = Math.floor(Math.random() * 50000) + 100000;
            const expenses = Math.floor(Math.random() * 20000) + 30000;
            const invoices = Math.floor(Math.random() * 500) + 1000;
            const users = Math.floor(Math.random() * 10) + 8;

            document.getElementById('totalRevenue').textContent = revenue.toLocaleString();
            document.getElementById('totalExpenses').textContent = expenses.toLocaleString();
            document.getElementById('totalInvoices').textContent = invoices.toLocaleString();
            document.getElementById('totalUsers').textContent = users;
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من المصادقة
            const user = checkAuth();
            if (user) {
                document.getElementById('sidebarUserName').textContent = user.name;
                document.getElementById('welcomeName').textContent = user.name;
            }

            // استعادة حالة القائمة الجانبية
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }

            // تحديث الوقت كل ثانية
            updateDateTime();
            setInterval(updateDateTime, 1000);

            // تحديث الإحصائيات كل 30 ثانية
            updateStats();
            setInterval(updateStats, 30000);

            // تأثيرات بصرية
            const cards = document.querySelectorAll('.stats-card, .card');
            cards.forEach((card, index) => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.6s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });

        // محاكاة الرسوم البيانية (يمكن استبدالها بـ Chart.js لاحقاً)
        function initCharts() {
            // هذا مكان للرسوم البيانية
            console.log('Charts initialized');
        }

        // تهيئة الرسوم البيانية
        setTimeout(initCharts, 1000);
    </script>
</body>
</html>
