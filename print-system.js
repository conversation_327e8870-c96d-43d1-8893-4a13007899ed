// نظام الطباعة الشامل

// إعدادات الطباعة
const printSettings = {
    companyName: 'مخبز الأصالة',
    companyAddress: 'صنعاء - شارع الزبيري',
    companyPhone: '+967 1 234567',
    companyEmail: '<EMAIL>',
    logo: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIHZpZXdCb3g9IjAgMCA1MCA1MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjUiIGN5PSIyNSIgcj0iMjUiIGZpbGw9IiM2NjdlZWEiLz4KPHN2ZyB3aWR0aD0iMzAiIGhlaWdodD0iMzAiIHg9IjEwIiB5PSIxMCIgZmlsbD0id2hpdGUiPgo8cGF0aCBkPSJNMTUgMTBIMjVWMjBIMTVWMTBaIi8+CjwvZz4KPC9zdmc+'
};

// طباعة القيد المحاسبي
function printJournalEntry(entryId) {
    const entry = journalEntries.find(e => e.id === entryId);
    if (!entry) {
        alert('لم يتم العثور على القيد');
        return;
    }
    
    const printContent = generateJournalEntryPrint(entry);
    openPrintWindow(printContent, `قيد محاسبي - ${entry.number}`);
}

// إنشاء محتوى طباعة القيد
function generateJournalEntryPrint(entry) {
    const statusText = {
        'draft': 'مسودة',
        'pending': 'في انتظار المراجعة',
        'approved': 'معتمد',
        'rejected': 'مرفوض'
    }[entry.status];
    
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>قيد محاسبي - ${entry.number}</title>
            <style>
                ${getPrintStyles()}
                .entry-header {
                    text-align: center;
                    margin-bottom: 2rem;
                    border-bottom: 2px solid #333;
                    padding-bottom: 1rem;
                }
                .entry-details {
                    margin-bottom: 2rem;
                }
                .entry-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 2rem;
                }
                .entry-table th,
                .entry-table td {
                    border: 1px solid #333;
                    padding: 8px;
                    text-align: center;
                }
                .entry-table th {
                    background-color: #f5f5f5;
                    font-weight: bold;
                }
                .debit-amount {
                    color: #dc3545;
                    font-weight: bold;
                }
                .credit-amount {
                    color: #28a745;
                    font-weight: bold;
                }
                .signatures {
                    margin-top: 3rem;
                    display: flex;
                    justify-content: space-between;
                }
                .signature-box {
                    text-align: center;
                    width: 200px;
                }
                .signature-line {
                    border-top: 1px solid #333;
                    margin-top: 2rem;
                    padding-top: 0.5rem;
                }
            </style>
        </head>
        <body>
            ${getCompanyHeader()}
            
            <div class="entry-header">
                <h2>قيد محاسبي</h2>
                <h3>رقم القيد: ${entry.number}</h3>
            </div>
            
            <div class="entry-details">
                <div class="row">
                    <div class="col">
                        <strong>التاريخ:</strong> ${formatDateForPrint(entry.date)}
                    </div>
                    <div class="col">
                        <strong>الحالة:</strong> ${statusText}
                    </div>
                </div>
                <div class="row">
                    <div class="col">
                        <strong>البيان:</strong> ${entry.description}
                    </div>
                </div>
                ${entry.reference ? `
                <div class="row">
                    <div class="col">
                        <strong>المرجع:</strong> ${entry.reference}
                    </div>
                </div>
                ` : ''}
            </div>
            
            <table class="entry-table">
                <thead>
                    <tr>
                        <th>رقم الحساب</th>
                        <th>اسم الحساب</th>
                        <th>البيان</th>
                        <th>مدين</th>
                        <th>دائن</th>
                    </tr>
                </thead>
                <tbody>
                    ${entry.details.map(detail => `
                        <tr>
                            <td>${getAccountCode(detail.accountId)}</td>
                            <td>${detail.accountName}</td>
                            <td>${entry.description}</td>
                            <td class="${detail.debit > 0 ? 'debit-amount' : ''}">${detail.debit > 0 ? detail.debit.toLocaleString() : '-'}</td>
                            <td class="${detail.credit > 0 ? 'credit-amount' : ''}">${detail.credit > 0 ? detail.credit.toLocaleString() : '-'}</td>
                        </tr>
                    `).join('')}
                </tbody>
                <tfoot>
                    <tr style="font-weight: bold; background-color: #f5f5f5;">
                        <td colspan="3">الإجمالي</td>
                        <td class="debit-amount">${entry.totalAmount.toLocaleString()}</td>
                        <td class="credit-amount">${entry.totalAmount.toLocaleString()}</td>
                    </tr>
                </tfoot>
            </table>
            
            <div class="signatures">
                <div class="signature-box">
                    <div class="signature-line">المحاسب</div>
                    <div>${entry.createdBy}</div>
                </div>
                ${entry.reviewedBy ? `
                <div class="signature-box">
                    <div class="signature-line">المراجع</div>
                    <div>${entry.reviewedBy}</div>
                </div>
                ` : ''}
                <div class="signature-box">
                    <div class="signature-line">المدير المالي</div>
                </div>
            </div>
            
            ${getPrintFooter()}
        </body>
        </html>
    `;
}

// طباعة تقرير المخزون
function printInventoryReport() {
    const printContent = generateInventoryReportPrint();
    openPrintWindow(printContent, 'تقرير المخزون');
}

// إنشاء تقرير المخزون للطباعة
function generateInventoryReportPrint() {
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>تقرير المخزون</title>
            <style>
                ${getPrintStyles()}
                .report-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 2rem;
                }
                .report-table th,
                .report-table td {
                    border: 1px solid #333;
                    padding: 6px;
                    text-align: center;
                    font-size: 12px;
                }
                .report-table th {
                    background-color: #f5f5f5;
                    font-weight: bold;
                }
                .stock-high { color: #28a745; }
                .stock-medium { color: #ffc107; }
                .stock-low { color: #dc3545; }
                .stock-out { color: #6c757d; }
            </style>
        </head>
        <body>
            ${getCompanyHeader()}
            
            <div class="report-header">
                <h2>تقرير المخزون</h2>
                <p>تاريخ التقرير: ${formatDateForPrint(new Date().toISOString().split('T')[0])}</p>
            </div>
            
            <table class="report-table">
                <thead>
                    <tr>
                        <th>الكود</th>
                        <th>اسم الصنف</th>
                        <th>الفئة</th>
                        <th>الوحدة</th>
                        <th>المخزون الكلي</th>
                        <th>الحد الأدنى</th>
                        <th>الحالة</th>
                        <th>القيمة</th>
                    </tr>
                </thead>
                <tbody>
                    ${typeof items !== 'undefined' ? items.map(item => {
                        const totalStock = Object.values(item.warehouseStocks || {}).reduce((sum, stock) => sum + stock, 0);
                        const stockClass = totalStock === 0 ? 'stock-out' : 
                                         totalStock <= item.minStock ? 'stock-low' : 
                                         totalStock <= (item.reorderPoint || item.minStock * 2) ? 'stock-medium' : 'stock-high';
                        const stockStatus = totalStock === 0 ? 'منتهي' : 
                                          totalStock <= item.minStock ? 'منخفض' : 
                                          totalStock <= (item.reorderPoint || item.minStock * 2) ? 'متوسط' : 'عالي';
                        const value = totalStock * (item.costPrice || 0);
                        
                        return `
                            <tr>
                                <td>${item.code}</td>
                                <td>${item.name}</td>
                                <td>${getCategoryName(item.category)}</td>
                                <td>${getUnitName(item.unit)}</td>
                                <td class="${stockClass}">${totalStock.toLocaleString()}</td>
                                <td>${item.minStock.toLocaleString()}</td>
                                <td class="${stockClass}">${stockStatus}</td>
                                <td>${value.toLocaleString()} ر.ي</td>
                            </tr>
                        `;
                    }).join('') : '<tr><td colspan="8">لا توجد بيانات</td></tr>'}
                </tbody>
            </table>
            
            ${getPrintFooter()}
        </body>
        </html>
    `;
}

// طباعة كشف الرواتب
function printPayrollReport() {
    const printContent = generatePayrollReportPrint();
    openPrintWindow(printContent, 'كشف الرواتب');
}

// إنشاء كشف الرواتب للطباعة
function generatePayrollReportPrint() {
    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>كشف الرواتب</title>
            <style>
                ${getPrintStyles()}
                .payroll-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 2rem;
                }
                .payroll-table th,
                .payroll-table td {
                    border: 1px solid #333;
                    padding: 8px;
                    text-align: center;
                    font-size: 11px;
                }
                .payroll-table th {
                    background-color: #f5f5f5;
                    font-weight: bold;
                }
                .total-row {
                    font-weight: bold;
                    background-color: #e9ecef;
                }
            </style>
        </head>
        <body>
            ${getCompanyHeader()}
            
            <div class="report-header">
                <h2>كشف الرواتب</h2>
                <p>الشهر: ${getCurrentMonth()} ${new Date().getFullYear()}</p>
            </div>
            
            <table class="payroll-table">
                <thead>
                    <tr>
                        <th>الرقم</th>
                        <th>اسم الموظف</th>
                        <th>المنصب</th>
                        <th>الراتب الأساسي</th>
                        <th>البدلات</th>
                        <th>الخصومات</th>
                        <th>السلف</th>
                        <th>صافي الراتب</th>
                        <th>التوقيع</th>
                    </tr>
                </thead>
                <tbody>
                    ${typeof employees !== 'undefined' ? employees.filter(emp => emp.isActive).map((emp, index) => {
                        const allowances = (emp.allowances || 0);
                        const deductions = (emp.deductions || 0);
                        const advance = (emp.currentAdvance || 0);
                        const netSalary = emp.salary + allowances - deductions - advance;
                        
                        return `
                            <tr>
                                <td>${index + 1}</td>
                                <td>${emp.name}</td>
                                <td>${emp.position}</td>
                                <td>${emp.salary.toLocaleString()}</td>
                                <td>${allowances.toLocaleString()}</td>
                                <td>${deductions.toLocaleString()}</td>
                                <td>${advance.toLocaleString()}</td>
                                <td>${netSalary.toLocaleString()}</td>
                                <td style="width: 100px;"></td>
                            </tr>
                        `;
                    }).join('') : '<tr><td colspan="9">لا توجد بيانات</td></tr>'}
                </tbody>
            </table>
            
            ${getPrintFooter()}
        </body>
        </html>
    `;
}

// طباعة فاتورة
function printInvoice(invoiceId) {
    // سيتم تنفيذها عند إنشاء نظام الفواتير
    alert('سيتم تفعيل طباعة الفواتير عند إنشاء نظام الفواتير');
}

// الحصول على أنماط الطباعة
function getPrintStyles() {
    return `
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            direction: rtl;
            text-align: right;
        }
        
        .company-header {
            text-align: center;
            margin-bottom: 2rem;
            border-bottom: 2px solid #333;
            padding-bottom: 1rem;
        }
        
        .company-logo {
            width: 60px;
            height: 60px;
            margin: 0 auto 1rem;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .company-details {
            font-size: 12px;
            color: #666;
        }
        
        .report-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .report-header h2 {
            font-size: 20px;
            margin-bottom: 0.5rem;
        }
        
        .row {
            display: flex;
            margin-bottom: 1rem;
        }
        
        .col {
            flex: 1;
            padding: 0 1rem;
        }
        
        .print-footer {
            margin-top: 3rem;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 1rem;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
            
            .no-print {
                display: none !important;
            }
            
            @page {
                margin: 1cm;
                size: A4;
            }
        }
    `;
}

// الحصول على رأس الشركة
function getCompanyHeader() {
    return `
        <div class="company-header">
            <div class="company-logo">
                <img src="${printSettings.logo}" alt="شعار الشركة" style="width: 100%; height: 100%;">
            </div>
            <div class="company-name">${printSettings.companyName}</div>
            <div class="company-details">
                ${printSettings.companyAddress}<br>
                هاتف: ${printSettings.companyPhone} | بريد إلكتروني: ${printSettings.companyEmail}
            </div>
        </div>
    `;
}

// الحصول على تذييل الطباعة
function getPrintFooter() {
    return `
        <div class="print-footer">
            <p>تم الطباعة في: ${new Date().toLocaleString('ar-SA')}</p>
            <p>نظام إدارة المخبز - جميع الحقوق محفوظة</p>
        </div>
    `;
}

// فتح نافذة الطباعة
function openPrintWindow(content, title) {
    const printWindow = window.open('', '_blank', 'width=800,height=600');
    printWindow.document.write(content);
    printWindow.document.close();
    
    printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
    };
}

// تنسيق التاريخ للطباعة
function formatDateForPrint(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
}

// الحصول على كود الحساب
function getAccountCode(accountId) {
    const account = accounts.find(acc => acc.id === accountId);
    return account ? account.code : '';
}

// الحصول على اسم الفئة
function getCategoryName(category) {
    const categories = {
        'flour': 'دقيق ومواد أساسية',
        'ingredients': 'مكونات',
        'packaging': 'مواد تعبئة',
        'tools': 'أدوات'
    };
    return categories[category] || category;
}

// الحصول على اسم الوحدة
function getUnitName(unit) {
    const units = {
        'kg': 'كيلوجرام',
        'g': 'جرام',
        'l': 'لتر',
        'ml': 'مليلتر',
        'piece': 'قطعة',
        'box': 'صندوق',
        'bag': 'كيس'
    };
    return units[unit] || unit;
}

// الحصول على الشهر الحالي
function getCurrentMonth() {
    const months = [
        'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
        'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
    ];
    return months[new Date().getMonth()];
}

// طباعة تقرير مخصص
function printCustomReport(reportType, data, title) {
    let content = '';
    
    switch (reportType) {
        case 'sales':
            content = generateSalesReportPrint(data);
            break;
        case 'purchases':
            content = generatePurchasesReportPrint(data);
            break;
        case 'financial':
            content = generateFinancialReportPrint(data);
            break;
        default:
            alert('نوع التقرير غير مدعوم');
            return;
    }
    
    openPrintWindow(content, title);
}

// تصدير إلى PDF (محاكاة)
function exportToPDF(content, filename) {
    // في التطبيق الحقيقي، يمكن استخدام مكتبة مثل jsPDF
    alert(`سيتم تصدير ${filename} إلى PDF`);
}

// طباعة باركود
function printBarcode(code, type = 'CODE128') {
    // في التطبيق الحقيقي، يمكن استخدام مكتبة مثل JsBarcode
    alert(`سيتم طباعة الباركود: ${code}`);
}

// إعدادات الطباعة المتقدمة
function showPrintSettings() {
    const modal = document.createElement('div');
    modal.innerHTML = `
        <div class="modal fade" id="printSettingsModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">إعدادات الطباعة</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">اسم الشركة</label>
                            <input type="text" class="form-control" id="printCompanyName" value="${printSettings.companyName}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">عنوان الشركة</label>
                            <input type="text" class="form-control" id="printCompanyAddress" value="${printSettings.companyAddress}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">هاتف الشركة</label>
                            <input type="text" class="form-control" id="printCompanyPhone" value="${printSettings.companyPhone}">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">بريد الشركة الإلكتروني</label>
                            <input type="email" class="form-control" id="printCompanyEmail" value="${printSettings.companyEmail}">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                        <button type="button" class="btn btn-primary" onclick="savePrintSettings()">حفظ</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    const printModal = new bootstrap.Modal(document.getElementById('printSettingsModal'));
    printModal.show();
}

// حفظ إعدادات الطباعة
function savePrintSettings() {
    printSettings.companyName = document.getElementById('printCompanyName').value;
    printSettings.companyAddress = document.getElementById('printCompanyAddress').value;
    printSettings.companyPhone = document.getElementById('printCompanyPhone').value;
    printSettings.companyEmail = document.getElementById('printCompanyEmail').value;
    
    localStorage.setItem('printSettings', JSON.stringify(printSettings));
    
    const modal = bootstrap.Modal.getInstance(document.getElementById('printSettingsModal'));
    modal.hide();
    
    alert('تم حفظ إعدادات الطباعة بنجاح!');
}

// تحميل إعدادات الطباعة
function loadPrintSettings() {
    const saved = localStorage.getItem('printSettings');
    if (saved) {
        Object.assign(printSettings, JSON.parse(saved));
    }
}
