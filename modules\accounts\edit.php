<?php
/**
 * تعديل الحساب المحاسبي
 * Edit Account
 */

require_once __DIR__ . '/../../config/config.php';
require_once __DIR__ . '/../../auth/check_auth.php';
require_once __DIR__ . '/../../includes/functions.php';

// فحص الصلاحيات
checkPermission('accounts');

$page_title = 'تعديل الحساب المحاسبي';
$accountId = intval($_GET['id'] ?? 0);
$error_message = '';
$success_message = '';

if (!$accountId) {
    header('Location: index.php');
    exit();
}

// الحصول على بيانات الحساب
try {
    $account = queryOne(
        "SELECT * FROM chart_of_accounts WHERE id = ?",
        [$accountId]
    );
    
    if (!$account) {
        showMessage('الحساب غير موجود', 'error');
        header('Location: index.php');
        exit();
    }
} catch (Exception $e) {
    logError("خطأ في تحميل بيانات الحساب: " . $e->getMessage());
    showMessage('خطأ في تحميل البيانات', 'error');
    header('Location: index.php');
    exit();
}

// معالجة النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    checkCSRF();
    
    try {
        $data = [
            'account_code' => cleanInput($_POST['account_code']),
            'account_name' => cleanInput($_POST['account_name']),
            'account_name_en' => cleanInput($_POST['account_name_en']),
            'parent_id' => !empty($_POST['parent_id']) ? intval($_POST['parent_id']) : null,
            'account_type' => cleanInput($_POST['account_type']),
            'account_nature' => cleanInput($_POST['account_nature']),
            'is_main' => isset($_POST['is_main']) ? 1 : 0,
            'is_active' => isset($_POST['is_active']) ? 1 : 0,
            'description' => cleanInput($_POST['description'])
        ];
        
        // التحقق من صحة البيانات
        if (empty($data['account_code'])) {
            throw new Exception('رمز الحساب مطلوب');
        }
        
        if (empty($data['account_name'])) {
            throw new Exception('اسم الحساب مطلوب');
        }
        
        if (empty($data['account_type'])) {
            throw new Exception('نوع الحساب مطلوب');
        }
        
        if (empty($data['account_nature'])) {
            throw new Exception('طبيعة الحساب مطلوبة');
        }
        
        // التحقق من عدم تكرار رمز الحساب (باستثناء الحساب الحالي)
        $existingAccount = queryOne(
            "SELECT id FROM chart_of_accounts WHERE account_code = ? AND id != ?",
            [$data['account_code'], $accountId]
        );
        
        if ($existingAccount) {
            throw new Exception('رمز الحساب موجود مسبقاً');
        }
        
        // التحقق من عدم اختيار نفس الحساب كحساب أب
        if ($data['parent_id'] == $accountId) {
            throw new Exception('لا يمكن اختيار نفس الحساب كحساب أب');
        }
        
        // التحقق من عدم إنشاء حلقة في الهيكل الهرمي
        if ($data['parent_id']) {
            $checkParent = $data['parent_id'];
            while ($checkParent) {
                if ($checkParent == $accountId) {
                    throw new Exception('لا يمكن اختيار حساب فرعي كحساب أب');
                }
                
                $parentData = queryOne(
                    "SELECT parent_id FROM chart_of_accounts WHERE id = ?",
                    [$checkParent]
                );
                
                $checkParent = $parentData['parent_id'] ?? null;
            }
        }
        
        // تحديد المستوى
        $level = 1;
        if ($data['parent_id']) {
            $parentAccount = queryOne(
                "SELECT level FROM chart_of_accounts WHERE id = ?",
                [$data['parent_id']]
            );
            
            if (!$parentAccount) {
                throw new Exception('الحساب الأب غير موجود');
            }
            
            $level = $parentAccount['level'] + 1;
        }
        
        $data['level'] = $level;
        
        // تحديث الحساب
        execute(
            "UPDATE chart_of_accounts SET 
                account_code = ?, account_name = ?, account_name_en = ?, parent_id = ?, 
                account_type = ?, account_nature = ?, level = ?, is_main = ?, 
                is_active = ?, description = ?, updated_at = NOW()
             WHERE id = ?",
            [
                $data['account_code'], $data['account_name'], $data['account_name_en'],
                $data['parent_id'], $data['account_type'], $data['account_nature'],
                $data['level'], $data['is_main'], $data['is_active'], $data['description'],
                $accountId
            ]
        );
        
        // تحديث مستويات الحسابات الفرعية إذا تغير المستوى
        if ($level != $account['level']) {
            updateSubAccountsLevel($accountId, $level);
        }
        
        // تسجيل النشاط
        logUserActivity('update', 'chart_of_accounts', $accountId, $account, $data);
        
        $success_message = 'تم تحديث الحساب بنجاح';
        
        // تحديث البيانات المحلية
        $account = array_merge($account, $data);
        
    } catch (Exception $e) {
        $error_message = $e->getMessage();
        logError("خطأ في تحديث الحساب: " . $e->getMessage());
    }
}

/**
 * تحديث مستويات الحسابات الفرعية
 */
function updateSubAccountsLevel($parentId, $parentLevel) {
    try {
        $subAccounts = query(
            "SELECT id FROM chart_of_accounts WHERE parent_id = ?",
            [$parentId]
        );
        
        foreach ($subAccounts as $subAccount) {
            $newLevel = $parentLevel + 1;
            
            execute(
                "UPDATE chart_of_accounts SET level = ? WHERE id = ?",
                [$newLevel, $subAccount['id']]
            );
            
            // تحديث الحسابات الفرعية للحساب الفرعي (تكرار)
            updateSubAccountsLevel($subAccount['id'], $newLevel);
        }
    } catch (Exception $e) {
        logError("خطأ في تحديث مستويات الحسابات الفرعية: " . $e->getMessage());
    }
}

// الحصول على الحسابات الرئيسية للاختيار كحساب أب (باستثناء الحساب الحالي وفروعه)
$parentAccounts = query(
    "SELECT * FROM chart_of_accounts 
     WHERE id != ? AND id NOT IN (
         SELECT id FROM chart_of_accounts 
         WHERE FIND_IN_SET(?, CONCAT(',', account_code, ',')) > 0
     )
     ORDER BY account_code",
    [$accountId, $account['account_code']]
);

include __DIR__ . '/../../includes/header.php';
?>

<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="bi bi-pencil text-primary me-2"></i>
        تعديل الحساب المحاسبي
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="view.php?id=<?php echo $accountId; ?>" class="btn btn-outline-info">
                <i class="bi bi-eye me-1"></i>
                عرض التفاصيل
            </a>
            <a href="index.php" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-right me-1"></i>
                العودة للقائمة
            </a>
        </div>
    </div>
</div>

<?php if ($error_message): ?>
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>
        <?php echo htmlspecialchars($error_message); ?>
    </div>
<?php endif; ?>

<?php if ($success_message): ?>
    <div class="alert alert-success">
        <i class="bi bi-check-circle me-2"></i>
        <?php echo htmlspecialchars($success_message); ?>
    </div>
<?php endif; ?>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    تعديل بيانات الحساب
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="account-form">
                    <?php echo csrfField(); ?>
                    
                    <div class="row">
                        <!-- المعلومات الأساسية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-info-circle me-2"></i>
                                المعلومات الأساسية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="account_code" class="form-label">رمز الحساب *</label>
                                <input type="text" class="form-control" id="account_code" name="account_code" 
                                       value="<?php echo htmlspecialchars($account['account_code']); ?>" required>
                                <div class="form-text">رمز فريد للحساب</div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_name" class="form-label">اسم الحساب *</label>
                                <input type="text" class="form-control" id="account_name" name="account_name" 
                                       value="<?php echo htmlspecialchars($account['account_name']); ?>" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_name_en" class="form-label">اسم الحساب بالإنجليزية</label>
                                <input type="text" class="form-control" id="account_name_en" name="account_name_en" 
                                       value="<?php echo htmlspecialchars($account['account_name_en'] ?? ''); ?>">
                            </div>
                            
                            <div class="mb-3">
                                <label for="parent_id" class="form-label">الحساب الأب</label>
                                <select class="form-select" id="parent_id" name="parent_id">
                                    <option value="">اختر الحساب الأب (اختياري)</option>
                                    <?php foreach ($parentAccounts as $parentAccount): ?>
                                        <option value="<?php echo $parentAccount['id']; ?>" 
                                                <?php echo ($account['parent_id'] == $parentAccount['id']) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($parentAccount['account_code'] . ' - ' . $parentAccount['account_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        
                        <!-- الإعدادات المحاسبية -->
                        <div class="col-md-6">
                            <h6 class="text-primary mb-3">
                                <i class="bi bi-calculator me-2"></i>
                                الإعدادات المحاسبية
                            </h6>
                            
                            <div class="mb-3">
                                <label for="account_type" class="form-label">نوع الحساب *</label>
                                <select class="form-select" id="account_type" name="account_type" required>
                                    <option value="">اختر نوع الحساب</option>
                                    <?php foreach (ACCOUNT_TYPES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo ($account['account_type'] == $key) ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="account_nature" class="form-label">طبيعة الحساب *</label>
                                <select class="form-select" id="account_nature" name="account_nature" required>
                                    <option value="">اختر طبيعة الحساب</option>
                                    <?php foreach (ACCOUNT_NATURES as $key => $value): ?>
                                        <option value="<?php echo $key; ?>" 
                                                <?php echo ($account['account_nature'] == $key) ? 'selected' : ''; ?>>
                                            <?php echo $value; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="form-text">
                                    <small>
                                        <strong>مدين:</strong> الأصول والمصروفات<br>
                                        <strong>دائن:</strong> الخصوم وحقوق الملكية والإيرادات
                                    </small>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_main" name="is_main" 
                                           <?php echo $account['is_main'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_main">
                                        حساب رئيسي
                                    </label>
                                    <div class="form-text">الحسابات الرئيسية لا يمكن حذفها</div>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           <?php echo $account['is_active'] ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="is_active">
                                        حساب نشط
                                    </label>
                                </div>
                            </div>
                            
                            <!-- معلومات إضافية -->
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle me-2"></i>معلومات إضافية</h6>
                                <small>
                                    <strong>المستوى الحالي:</strong> <?php echo $account['level']; ?><br>
                                    <strong>الرصيد الافتتاحي:</strong> <?php echo formatMoney($account['opening_balance']); ?><br>
                                    <strong>الرصيد الحالي:</strong> <?php echo formatMoney($account['current_balance']); ?>
                                </small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"><?php echo htmlspecialchars($account['description'] ?? ''); ?></textarea>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex justify-content-end gap-2">
                                <a href="view.php?id=<?php echo $accountId; ?>" class="btn btn-secondary">
                                    <i class="bi bi-x-lg me-1"></i>
                                    إلغاء
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="bi bi-check-lg me-1"></i>
                                    حفظ التغييرات
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
$inline_scripts = "
    document.addEventListener('DOMContentLoaded', function() {
        const accountTypeSelect = document.getElementById('account_type');
        const accountNatureSelect = document.getElementById('account_nature');
        
        // تحديد طبيعة الحساب تلقائياً حسب النوع
        accountTypeSelect.addEventListener('change', function() {
            const type = this.value;
            
            if (type === 'asset' || type === 'expense') {
                accountNatureSelect.value = 'debit';
            } else if (type === 'liability' || type === 'equity' || type === 'revenue') {
                accountNatureSelect.value = 'credit';
            }
        });
        
        // التحقق من صحة النموذج
        document.getElementById('account-form').addEventListener('submit', function(e) {
            const accountCode = document.getElementById('account_code').value.trim();
            const accountName = document.getElementById('account_name').value.trim();
            const accountType = document.getElementById('account_type').value;
            const accountNature = document.getElementById('account_nature').value;
            
            if (!accountCode || !accountName || !accountType || !accountNature) {
                e.preventDefault();
                showErrorMessage('يرجى ملء جميع الحقول المطلوبة');
                return false;
            }
            
            showLoading(true);
        });
    });
";

include __DIR__ . '/../../includes/footer.php';
?>
