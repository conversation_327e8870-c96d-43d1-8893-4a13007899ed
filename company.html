<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعدادات المنشأة</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
        }

        .sidebar {
            position: fixed;
            top: 0;
            right: 0;
            height: 100vh;
            width: 280px;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: -2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar.collapsed {
            width: 70px;
        }

        .sidebar-header {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            text-align: center;
        }

        .sidebar.collapsed .sidebar-header {
            padding: 1rem 0.5rem;
        }

        .sidebar-brand {
            font-size: 1.2rem;
            font-weight: 700;
            color: white;
            text-decoration: none;
        }

        .sidebar.collapsed .sidebar-brand .brand-text {
            display: none;
        }

        .sidebar-toggle {
            position: absolute;
            top: 1.5rem;
            left: 1rem;
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .sidebar-toggle:hover {
            background: rgba(255,255,255,0.3);
        }

        .sidebar-menu {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-menu li {
            margin: 0;
        }

        .sidebar-menu a {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .sidebar.collapsed .sidebar-menu a {
            padding: 1rem 0.5rem;
            justify-content: center;
        }

        .sidebar-menu a:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: rgba(255,255,255,0.5);
        }

        .sidebar-menu a.active {
            background: rgba(255,255,255,0.2);
            color: white;
            border-right-color: white;
        }

        .sidebar-menu a i {
            font-size: 1.2rem;
            margin-left: 1rem;
            width: 20px;
            text-align: center;
        }

        .sidebar.collapsed .sidebar-menu a i {
            margin-left: 0;
        }

        .sidebar-menu a .menu-text {
            font-weight: 500;
        }

        .sidebar.collapsed .sidebar-menu a .menu-text {
            display: none;
        }

        .main-content {
            margin-right: 280px;
            transition: all 0.3s ease;
            min-height: 100vh;
        }

        .main-content.expanded {
            margin-right: 70px;
        }

        .content-wrapper {
            padding: 2rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }

        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .logo-preview {
            max-width: 200px;
            max-height: 150px;
            border-radius: 10px;
            border: 2px dashed #dee2e6;
            padding: 1rem;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .logo-preview:hover {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .logo-preview img {
            max-width: 100%;
            max-height: 100px;
            border-radius: 5px;
        }

        .settings-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .section-title {
            color: #667eea;
            font-weight: 700;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #e9ecef;
        }

        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- القائمة الجانبية -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a href="dashboard-admin.html" class="sidebar-brand">
                <i class="bi bi-shop me-2"></i>
                <span class="brand-text">نظام المخبز</span>
            </a>
            <button class="sidebar-toggle" onclick="toggleSidebar()">
                <i class="bi bi-list"></i>
            </button>
        </div>

        <ul class="sidebar-menu">
            <li>
                <a href="dashboard-admin.html">
                    <i class="bi bi-speedometer2"></i>
                    <span class="menu-text">لوحة التحكم</span>
                </a>
            </li>

            <li>
                <a href="company.html" class="active">
                    <i class="bi bi-building"></i>
                    <span class="menu-text">إعدادات المنشأة</span>
                </a>
            </li>

            <li>
                <a href="accounts.html">
                    <i class="bi bi-diagram-3"></i>
                    <span class="menu-text">شجرة الحسابات</span>
                </a>
            </li>

            <li>
                <a href="users.html">
                    <i class="bi bi-people"></i>
                    <span class="menu-text">إدارة المستخدمين</span>
                </a>
            </li>

            <li>
                <a href="cash-banks.html">
                    <i class="bi bi-bank"></i>
                    <span class="menu-text">الصناديق والبنوك</span>
                </a>
            </li>

            <li>
                <a href="employees.html">
                    <i class="bi bi-person-badge"></i>
                    <span class="menu-text">الموظفين والرواتب</span>
                </a>
            </li>

            <li>
                <a href="inventory.html">
                    <i class="bi bi-box-seam"></i>
                    <span class="menu-text">إدارة المخزون</span>
                </a>
            </li>

            <li>
                <a href="invoices.html">
                    <i class="bi bi-receipt"></i>
                    <span class="menu-text">الفواتير</span>
                </a>
            </li>

            <li>
                <a href="vouchers.html">
                    <i class="bi bi-file-earmark-text"></i>
                    <span class="menu-text">سندات القبض والصرف</span>
                </a>
            </li>

            <li>
                <a href="assets.html">
                    <i class="bi bi-gear"></i>
                    <span class="menu-text">الأصول الثابتة</span>
                </a>
            </li>

            <li>
                <a href="reports.html">
                    <i class="bi bi-graph-up"></i>
                    <span class="menu-text">التقارير</span>
                </a>
            </li>

            <li style="margin-top: 2rem; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 1rem;">
                <a href="#" onclick="showUserProfile()">
                    <i class="bi bi-person-circle"></i>
                    <span class="menu-text" id="sidebarUserName">مدير النظام</span>
                </a>
            </li>

            <li>
                <a href="#" onclick="logout()">
                    <i class="bi bi-box-arrow-right"></i>
                    <span class="menu-text">تسجيل الخروج</span>
                </a>
            </li>
        </ul>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="main-content" id="mainContent">
        <div class="content-wrapper">
            <!-- العنوان -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h2">
                    <i class="bi bi-building text-primary me-2"></i>
                    إعدادات المنشأة
                </h1>
                <div class="btn-group">
                    <button type="button" class="btn btn-primary" onclick="saveAllSettings()">
                        <i class="bi bi-check-lg me-1"></i>حفظ جميع الإعدادات
                    </button>
                    <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                        <i class="bi bi-arrow-clockwise me-1"></i>إعادة تعيين
                    </button>
                </div>
            </div>

            <!-- رسائل التنبيه -->
            <div id="alertContainer"></div>

            <!-- المعلومات الأساسية -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-info-circle me-2"></i>
                    المعلومات الأساسية
                </h3>

                <form id="basicInfoForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="companyName" class="form-label">اسم المنشأة *</label>
                                <input type="text" class="form-control" id="companyName" required>
                            </div>

                            <div class="mb-3">
                                <label for="companyNameEn" class="form-label">اسم المنشأة بالإنجليزية</label>
                                <input type="text" class="form-control" id="companyNameEn">
                            </div>

                            <div class="mb-3">
                                <label for="businessType" class="form-label">نوع النشاط *</label>
                                <select class="form-select" id="businessType" required>
                                    <option value="">اختر نوع النشاط</option>
                                    <option value="bakery">مخبز</option>
                                    <option value="restaurant">مطعم</option>
                                    <option value="cafe">مقهى</option>
                                    <option value="pastry">حلويات</option>
                                    <option value="catering">تموين</option>
                                    <option value="other">أخرى</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="taxNumber" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="taxNumber">
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="commercialRecord" class="form-label">رقم السجل التجاري</label>
                                <input type="text" class="form-control" id="commercialRecord">
                            </div>

                            <div class="mb-3">
                                <label for="establishmentDate" class="form-label">تاريخ التأسيس</label>
                                <input type="date" class="form-control" id="establishmentDate">
                            </div>

                            <div class="mb-3">
                                <label for="currency" class="form-label">العملة الأساسية *</label>
                                <select class="form-select" id="currency" required>
                                    <option value="YER">ريال يمني (YER)</option>
                                    <option value="SAR">ريال سعودي (SAR)</option>
                                    <option value="USD">دولار أمريكي (USD)</option>
                                    <option value="EUR">يورو (EUR)</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="fiscalYearStart" class="form-label">بداية السنة المالية</label>
                                <select class="form-select" id="fiscalYearStart">
                                    <option value="01-01">1 يناير</option>
                                    <option value="04-01">1 أبريل</option>
                                    <option value="07-01">1 يوليو</option>
                                    <option value="10-01">1 أكتوبر</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- معلومات الاتصال -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-telephone me-2"></i>
                    معلومات الاتصال
                </h3>

                <form id="contactInfoForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" rows="3"></textarea>
                            </div>

                            <div class="mb-3">
                                <label for="city" class="form-label">المدينة</label>
                                <input type="text" class="form-control" id="city">
                            </div>

                            <div class="mb-3">
                                <label for="country" class="form-label">الدولة</label>
                                <select class="form-select" id="country">
                                    <option value="YE">اليمن</option>
                                    <option value="SA">السعودية</option>
                                    <option value="AE">الإمارات</option>
                                    <option value="QA">قطر</option>
                                    <option value="KW">الكويت</option>
                                    <option value="BH">البحرين</option>
                                    <option value="OM">عمان</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone">
                            </div>

                            <div class="mb-3">
                                <label for="mobile" class="form-label">رقم الجوال</label>
                                <input type="tel" class="form-control" id="mobile">
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email">
                            </div>

                            <div class="mb-3">
                                <label for="website" class="form-label">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" id="website">
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- الشعار والهوية البصرية -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-image me-2"></i>
                    الشعار والهوية البصرية
                </h3>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="logoUpload" class="form-label">رفع الشعار</label>
                            <input type="file" class="form-control" id="logoUpload" accept="image/*" onchange="previewLogo(this)">
                            <div class="form-text">الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG, SVG</div>
                        </div>

                        <div class="mb-3">
                            <label for="primaryColor" class="form-label">اللون الأساسي</label>
                            <input type="color" class="form-control form-control-color" id="primaryColor" value="#667eea">
                        </div>

                        <div class="mb-3">
                            <label for="secondaryColor" class="form-label">اللون الثانوي</label>
                            <input type="color" class="form-control form-control-color" id="secondaryColor" value="#764ba2">
                        </div>
                    </div>

                    <div class="col-md-6">
                        <label class="form-label">معاينة الشعار</label>
                        <div class="logo-preview" id="logoPreview">
                            <i class="bi bi-image" style="font-size: 3rem; color: #dee2e6;"></i>
                            <p class="text-muted mt-2">لا يوجد شعار</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- الإعدادات المحاسبية -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-calculator me-2"></i>
                    الإعدادات المحاسبية
                </h3>

                <form id="accountingSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="accountingMethod" class="form-label">طريقة المحاسبة</label>
                                <select class="form-select" id="accountingMethod">
                                    <option value="accrual">محاسبة الاستحقاق</option>
                                    <option value="cash">المحاسبة النقدية</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="inventoryMethod" class="form-label">طريقة تقييم المخزون</label>
                                <select class="form-select" id="inventoryMethod">
                                    <option value="fifo">الوارد أولاً صادر أولاً (FIFO)</option>
                                    <option value="lifo">الوارد أخيراً صادر أولاً (LIFO)</option>
                                    <option value="average">المتوسط المرجح</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="depreciationMethod" class="form-label">طريقة الإهلاك</label>
                                <select class="form-select" id="depreciationMethod">
                                    <option value="straight">القسط الثابت</option>
                                    <option value="declining">القسط المتناقص</option>
                                    <option value="units">وحدات الإنتاج</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="decimalPlaces" class="form-label">عدد الخانات العشرية</label>
                                <select class="form-select" id="decimalPlaces">
                                    <option value="2">2 خانات</option>
                                    <option value="3">3 خانات</option>
                                    <option value="4">4 خانات</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="taxRate" class="form-label">معدل الضريبة (%)</label>
                                <input type="number" class="form-control" id="taxRate" step="0.01" min="0" max="100">
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="autoJournalEntry" checked>
                                    <label class="form-check-label" for="autoJournalEntry">
                                        إنشاء القيود المحاسبية تلقائياً
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="requireApproval">
                                    <label class="form-check-label" for="requireApproval">
                                        يتطلب موافقة على القيود
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <!-- إعدادات النظام -->
            <div class="settings-section">
                <h3 class="section-title">
                    <i class="bi bi-gear me-2"></i>
                    إعدادات النظام
                </h3>

                <form id="systemSettingsForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="language" class="form-label">لغة النظام</label>
                                <select class="form-select" id="language">
                                    <option value="ar">العربية</option>
                                    <option value="en">English</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="dateFormat" class="form-label">تنسيق التاريخ</label>
                                <select class="form-select" id="dateFormat">
                                    <option value="dd/mm/yyyy">يوم/شهر/سنة</option>
                                    <option value="mm/dd/yyyy">شهر/يوم/سنة</option>
                                    <option value="yyyy-mm-dd">سنة-شهر-يوم</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <label for="timeFormat" class="form-label">تنسيق الوقت</label>
                                <select class="form-select" id="timeFormat">
                                    <option value="24">24 ساعة</option>
                                    <option value="12">12 ساعة</option>
                                </select>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="backupFrequency" class="form-label">تكرار النسخ الاحتياطي</label>
                                <select class="form-select" id="backupFrequency">
                                    <option value="daily">يومي</option>
                                    <option value="weekly">أسبوعي</option>
                                    <option value="monthly">شهري</option>
                                    <option value="manual">يدوي</option>
                                </select>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableNotifications" checked>
                                    <label class="form-check-label" for="enableNotifications">
                                        تفعيل الإشعارات
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableAuditLog" checked>
                                    <label class="form-check-label" for="enableAuditLog">
                                        تفعيل سجل المراجعة
                                    </label>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="enableAutoSave" checked>
                                    <label class="form-check-label" for="enableAutoSave">
                                        الحفظ التلقائي
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // بيانات المنشأة الافتراضية
        const defaultCompanyData = {
            basicInfo: {
                companyName: 'مخبز الأصالة',
                companyNameEn: 'Al-Asala Bakery',
                businessType: 'bakery',
                taxNumber: '*********',
                commercialRecord: 'CR-2024-001',
                establishmentDate: '2020-01-01',
                currency: 'YER',
                fiscalYearStart: '01-01'
            },
            contactInfo: {
                address: 'شارع الزبيري، حي الحصبة، صنعاء',
                city: 'صنعاء',
                country: 'YE',
                phone: '+967-1-234567',
                mobile: '+967-**********',
                email: '<EMAIL>',
                website: 'www.alasala-bakery.com'
            },
            branding: {
                primaryColor: '#667eea',
                secondaryColor: '#764ba2',
                logo: null
            },
            accountingSettings: {
                accountingMethod: 'accrual',
                inventoryMethod: 'fifo',
                depreciationMethod: 'straight',
                decimalPlaces: '3',
                taxRate: '5.00',
                autoJournalEntry: true,
                requireApproval: false
            },
            systemSettings: {
                language: 'ar',
                dateFormat: 'dd/mm/yyyy',
                timeFormat: '24',
                backupFrequency: 'daily',
                enableNotifications: true,
                enableAuditLog: true,
                enableAutoSave: true
            }
        };

        // التحقق من المصادقة
        function checkAuth() {
            const user = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
            if (!user) {
                window.location.href = 'login.html';
                return null;
            }
            return JSON.parse(user);
        }

        // تسجيل الخروج
        function logout() {
            localStorage.removeItem('currentUser');
            sessionStorage.removeItem('currentUser');
            window.location.href = 'login.html';
        }

        // تبديل القائمة الجانبية
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const mainContent = document.getElementById('mainContent');

            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');

            localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
        }

        // عرض ملف المستخدم
        function showUserProfile() {
            alert('سيتم فتح صفحة الملف الشخصي قريباً');
        }

        // عرض رسالة تنبيه
        function showAlert(message, type = 'info') {
            const alertTypes = {
                success: 'alert-success',
                error: 'alert-danger',
                warning: 'alert-warning',
                info: 'alert-info'
            };

            const alertClass = alertTypes[type] || 'alert-info';
            const iconClass = type === 'success' ? 'check-circle' :
                             type === 'error' ? 'exclamation-triangle' :
                             type === 'warning' ? 'exclamation-triangle' : 'info-circle';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="bi bi-${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;

            document.getElementById('alertContainer').innerHTML = alertHtml;

            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = document.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        // معاينة الشعار
        function previewLogo(input) {
            const file = input.files[0];
            const preview = document.getElementById('logoPreview');

            if (file) {
                // التحقق من حجم الملف (2MB)
                if (file.size > 2 * 1024 * 1024) {
                    showAlert('حجم الملف يجب أن يكون أقل من 2MB', 'error');
                    input.value = '';
                    return;
                }

                // التحقق من نوع الملف
                const allowedTypes = ['image/jpeg', 'image/png', 'image/svg+xml'];
                if (!allowedTypes.includes(file.type)) {
                    showAlert('نوع الملف غير مدعوم. يرجى اختيار JPG أو PNG أو SVG', 'error');
                    input.value = '';
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    preview.innerHTML = `<img src="${e.target.result}" alt="شعار المنشأة">`;

                    // حفظ الشعار في localStorage
                    const companyData = getCompanyData();
                    companyData.branding.logo = e.target.result;
                    saveCompanyData(companyData);

                    showAlert('تم رفع الشعار بنجاح', 'success');
                };
                reader.readAsDataURL(file);
            }
        }

        // الحصول على بيانات المنشأة
        function getCompanyData() {
            const saved = localStorage.getItem('companyData');
            return saved ? JSON.parse(saved) : defaultCompanyData;
        }

        // حفظ بيانات المنشأة
        function saveCompanyData(data) {
            localStorage.setItem('companyData', JSON.stringify(data));
        }

        // تحميل البيانات في النموذج
        function loadFormData() {
            const data = getCompanyData();

            // المعلومات الأساسية
            Object.keys(data.basicInfo).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = data.basicInfo[key];
                }
            });

            // معلومات الاتصال
            Object.keys(data.contactInfo).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    element.value = data.contactInfo[key];
                }
            });

            // الهوية البصرية
            document.getElementById('primaryColor').value = data.branding.primaryColor;
            document.getElementById('secondaryColor').value = data.branding.secondaryColor;

            if (data.branding.logo) {
                document.getElementById('logoPreview').innerHTML = `<img src="${data.branding.logo}" alt="شعار المنشأة">`;
            }

            // الإعدادات المحاسبية
            Object.keys(data.accountingSettings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = data.accountingSettings[key];
                    } else {
                        element.value = data.accountingSettings[key];
                    }
                }
            });

            // إعدادات النظام
            Object.keys(data.systemSettings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        element.checked = data.systemSettings[key];
                    } else {
                        element.value = data.systemSettings[key];
                    }
                }
            });
        }

        // جمع البيانات من النموذج
        function collectFormData() {
            const data = {
                basicInfo: {},
                contactInfo: {},
                branding: {},
                accountingSettings: {},
                systemSettings: {}
            };

            // المعلومات الأساسية
            Object.keys(defaultCompanyData.basicInfo).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    data.basicInfo[key] = element.value;
                }
            });

            // معلومات الاتصال
            Object.keys(defaultCompanyData.contactInfo).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    data.contactInfo[key] = element.value;
                }
            });

            // الهوية البصرية
            data.branding.primaryColor = document.getElementById('primaryColor').value;
            data.branding.secondaryColor = document.getElementById('secondaryColor').value;

            const currentData = getCompanyData();
            data.branding.logo = currentData.branding.logo;

            // الإعدادات المحاسبية
            Object.keys(defaultCompanyData.accountingSettings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        data.accountingSettings[key] = element.checked;
                    } else {
                        data.accountingSettings[key] = element.value;
                    }
                }
            });

            // إعدادات النظام
            Object.keys(defaultCompanyData.systemSettings).forEach(key => {
                const element = document.getElementById(key);
                if (element) {
                    if (element.type === 'checkbox') {
                        data.systemSettings[key] = element.checked;
                    } else {
                        data.systemSettings[key] = element.value;
                    }
                }
            });

            return data;
        }

        // حفظ جميع الإعدادات
        function saveAllSettings() {
            try {
                const data = collectFormData();

                // التحقق من البيانات المطلوبة
                if (!data.basicInfo.companyName) {
                    showAlert('اسم المنشأة مطلوب', 'error');
                    document.getElementById('companyName').focus();
                    return;
                }

                if (!data.basicInfo.businessType) {
                    showAlert('نوع النشاط مطلوب', 'error');
                    document.getElementById('businessType').focus();
                    return;
                }

                if (!data.basicInfo.currency) {
                    showAlert('العملة الأساسية مطلوبة', 'error');
                    document.getElementById('currency').focus();
                    return;
                }

                // حفظ البيانات
                saveCompanyData(data);

                // تطبيق الألوان على النظام
                applyBrandColors(data.branding.primaryColor, data.branding.secondaryColor);

                showAlert('تم حفظ جميع الإعدادات بنجاح', 'success');

                // تسجيل النشاط
                logActivity('تحديث إعدادات المنشأة');

            } catch (error) {
                showAlert('حدث خطأ أثناء حفظ الإعدادات', 'error');
                console.error('Error saving settings:', error);
            }
        }

        // إعادة تعيين الإعدادات
        function resetSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟')) {
                saveCompanyData(defaultCompanyData);
                loadFormData();
                showAlert('تم إعادة تعيين الإعدادات بنجاح', 'info');
            }
        }

        // تطبيق ألوان العلامة التجارية
        function applyBrandColors(primary, secondary) {
            const root = document.documentElement;
            root.style.setProperty('--primary-color', primary);
            root.style.setProperty('--secondary-color', secondary);

            // تحديث الألوان في localStorage
            localStorage.setItem('brandColors', JSON.stringify({ primary, secondary }));
        }

        // تسجيل النشاط
        function logActivity(action) {
            const activities = JSON.parse(localStorage.getItem('userActivities') || '[]');
            const user = checkAuth();

            activities.push({
                id: Date.now(),
                user: user.name,
                action: action,
                timestamp: new Date().toISOString(),
                module: 'company_settings'
            });

            // الاحتفاظ بآخر 100 نشاط فقط
            if (activities.length > 100) {
                activities.splice(0, activities.length - 100);
            }

            localStorage.setItem('userActivities', JSON.stringify(activities));
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من المصادقة
            const user = checkAuth();
            if (user) {
                document.getElementById('sidebarUserName').textContent = user.name;
            }

            // استعادة حالة القائمة الجانبية
            const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
            if (sidebarCollapsed) {
                document.getElementById('sidebar').classList.add('collapsed');
                document.getElementById('mainContent').classList.add('expanded');
            }

            // تحميل بيانات المنشأة
            loadFormData();

            // تطبيق الألوان المحفوظة
            const savedColors = localStorage.getItem('brandColors');
            if (savedColors) {
                const colors = JSON.parse(savedColors);
                applyBrandColors(colors.primary, colors.secondary);
            }

            // الحفظ التلقائي كل 30 ثانية
            const autoSaveEnabled = document.getElementById('enableAutoSave').checked;
            if (autoSaveEnabled) {
                setInterval(() => {
                    const data = collectFormData();
                    saveCompanyData(data);
                }, 30000);
            }

            // تأثيرات بصرية
            const sections = document.querySelectorAll('.settings-section');
            sections.forEach((section, index) => {
                section.style.opacity = '0';
                section.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    section.style.transition = 'all 0.6s ease';
                    section.style.opacity = '1';
                    section.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>
