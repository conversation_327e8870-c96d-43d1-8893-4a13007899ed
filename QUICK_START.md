# 🚀 دليل البدء السريع - نظام محاسبة المخبز

## 📋 ملخص سريع

تم إنشاء نظام محاسبي شامل للمخبز يدعم العربية RTL مع جميع الميزات المطلوبة.

## 🎯 ما تم إنجازه

### ✅ البنية الأساسية الكاملة
- **22 جدول** في قاعدة البيانات مع علاقات محكمة
- **نظام مصادقة متقدم** مع صلاحيات دقيقة
- **واجهة عربية RTL** احترافية
- **نظام أمان شامل** مع تتبع العمليات

### ✅ الملفات المنشأة (23 ملف)
1. **قاعدة البيانات** (6 ملفات):
   - `database/bakery_accounting.sql` - الجداول الأساسية
   - `database/bakery_accounting_part2.sql` - جداول الأصناف
   - `database/bakery_accounting_part3.sql` - جداول الفواتير
   - `database/bakery_accounting_part4.sql` - جداول الأصول
   - `database/install_database.sql` - ملف التثبيت الكامل
   - `database/warehouses_update.sql` - تحديث المخازن المتعددة

2. **الإعدادات** (3 ملفات):
   - `config/database.php` - اتصال قاعدة البيانات
   - `config/config.php` - الإعدادات العامة
   - `config/constants.php` - الثوابت

3. **المصادقة** (3 ملفات):
   - `auth/check_auth.php` - نظام الصلاحيات
   - `auth/login.php` - صفحة تسجيل الدخول
   - `auth/logout.php` - تسجيل الخروج

4. **الواجهة** (4 ملفات):
   - `index.html` - الصفحة الرئيسية الهجينة
   - `assets/css/style.css` - التنسيقات
   - `assets/js/app.js` - JavaScript
   - `.htaccess` - الحماية والتوجيه

5. **الوحدات** (6 ملفات):
   - `dashboard.php` - لوحة التحكم
   - `modules/company/settings.php` - إعدادات المنشأة
   - `modules/accounts/index.php` - شجرة الحسابات
   - `modules/accounts/create.php` - إضافة حساب
   - `modules/accounts/view.php` - عرض الحساب
   - `modules/accounts/edit.php` - تعديل الحساب

6. **الوثائق** (2 ملف):
   - `docs/README.md` - دليل شامل
   - `docs/INSTALLATION.md` - دليل التثبيت

## 🏗️ هيكل قاعدة البيانات

### الجداول الرئيسية (22 جدول):
1. **company_settings** - إعدادات المنشأة
2. **users** - المستخدمين والأدوار
3. **user_logs** - سجل العمليات
4. **chart_of_accounts** - شجرة الحسابات
5. **journal_entries** - القيود المحاسبية
6. **journal_entry_details** - تفاصيل القيود
7. **cash_banks** - الصناديق والبنوك
8. **employees** - بيانات الموظفين
9. **employee_salaries** - رواتب الموظفين
10. **units** - وحدات القياس
11. **item_categories** - فئات الأصناف
12. **items** - الأصناف (خامات/منتجات/خدمات)
13. **product_recipes** - وصفات المنتجات
14. **customers** - العملاء
15. **suppliers** - الموردين
16. **invoices** - الفواتير
17. **invoice_details** - تفاصيل الفواتير
18. **vouchers** - سندات القبض والصرف
19. **fixed_assets** - الأصول الثابتة
20. **asset_depreciation** - إهلاك الأصول
21. **inventory_movements** - حركة المخزون
22. **production_orders** - أوامر الإنتاج

## 🚀 خطوات التشغيل السريع

### 1. تحضير البيئة
```bash
# تشغيل Laragon أو WAMP أو XAMPP
# التأكد من تشغيل Apache و MySQL
```

### 2. نسخ المشروع
```bash
# نسخ مجلد anwarsoft إلى:
# Laragon: C:\laragon\www\anwarsoft
# WAMP: C:\wamp64\www\anwarsoft
# XAMPP: C:\xampp\htdocs\anwarsoft
```

### 3. إنشاء قاعدة البيانات
1. فتح phpMyAdmin: `http://localhost/phpmyadmin`
2. إنشاء قاعدة بيانات: `bakery_accounting`
3. استيراد: `database/install_database.sql`

### 4. تشغيل النظام
1. فتح: `http://localhost/anwarsoft`
2. النقر على "دخول النظام"
3. تسجيل الدخول:
   - **المستخدم**: `admin`
   - **كلمة المرور**: `password`

## 🎯 الميزات المتاحة حالياً

### ✅ جاهز للاستخدام:
- **صفحة ترحيبية هجينة** تفحص الخادم تلقائياً
- **نظام تسجيل دخول** آمن
- **قاعدة بيانات شاملة** مع بيانات تجريبية + المخازن المتعددة
- **نظام صلاحيات** متقدم
- **واجهة عربية RTL** متجاوبة
- **تتبع العمليات** والأمان
- **لوحة تحكم** تفاعلية مع إحصائيات
- **إعدادات المنشأة** كاملة مع رفع الشعار
- **شجرة الحسابات** كاملة (عرض/إضافة/تعديل/حذف)
- **دوال مساعدة** شاملة للعمليات المحاسبية
- **حماية متقدمة** مع ملف .htaccess

### 🔄 المرحلة التالية:
- إدارة المستخدمين والصلاحيات
- إدارة الصناديق والبنوك
- إدارة الموظفين والرواتب
- تطبيق القيود المحاسبية التلقائية

## 🔧 الإعدادات المطلوبة

### تحرير ملف الإعدادات:
```php
// config/database.php
define('DB_HOST', 'localhost');
define('DB_NAME', 'bakery_accounting');
define('DB_USER', 'root');
define('DB_PASS', ''); // كلمة مرور MySQL

// config/config.php
define('BASE_URL', 'http://localhost/anwarsoft');
```

## 📊 البيانات التجريبية المتوفرة

### تم إدراج بيانات تجريبية:
- **إعدادات المنشأة**: مخبز الأنوار
- **المستخدمين**: admin, accountant, cashier
- **شجرة الحسابات**: حسابات محاسبية كاملة
- **وحدات القياس**: كجم، جم، قطعة، لتر...
- **فئات الأصناف**: مواد خام، منتجات، حلويات...
- **أصناف تجريبية**: دقيق، سكر، خبز، كعك...
- **الصناديق**: صندوق رئيسي، صندوق مبيعات، بنك

## 🎨 المظهر والتصميم

### الميزات البصرية:
- **ألوان احترافية**: تدرج أزرق وبنفسجي
- **خطوط عربية**: Cairo font
- **أيقونات واضحة**: Bootstrap Icons
- **تأثيرات بصرية**: انيميشن وتحولات
- **تصميم متجاوب**: يعمل على جميع الأجهزة

## 🔒 الأمان المطبق

### إجراءات الحماية:
- **تشفير كلمات المرور**: bcrypt
- **حماية CSRF**: رموز أمان
- **تسجيل العمليات**: تتبع شامل
- **صلاحيات دقيقة**: تحكم في الوصول
- **جلسات آمنة**: انتهاء صلاحية تلقائي

## 📱 التوافق

### المتصفحات المدعومة:
- Chrome ✅
- Firefox ✅
- Safari ✅
- Edge ✅

### الأجهزة المدعومة:
- Desktop ✅
- Tablet ✅
- Mobile ✅

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:
1. **خطأ قاعدة البيانات**: فحص إعدادات الاتصال
2. **الصفحة لا تظهر**: التأكد من BASE_URL
3. **مشاكل الترميز**: استخدام utf8mb4_unicode_ci
4. **صلاحيات الملفات**: إعطاء صلاحيات للمجلدات

## 📞 الدعم

### للمساعدة:
- مراجعة `docs/README.md` للدليل الشامل
- مراجعة `docs/INSTALLATION.md` للتثبيت المفصل
- فحص ملف `logs/error.log` للأخطاء

---

**🎉 تهانينا! النظام جاهز للاستخدام والتطوير**

**المرحلة التالية**: إنشاء صفحات الوحدات وربطها بقاعدة البيانات
