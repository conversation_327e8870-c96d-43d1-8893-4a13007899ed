-- ===================================================
-- قاعدة بيانات نظام المحاسبة للمخبز - الجزء الثاني
-- Bakery Accounting System Database - Part 2
-- ===================================================

-- ===================================================
-- 10. جدول وحدات القياس
-- ===================================================
CREATE TABLE `units` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `unit_name` varchar(50) NOT NULL COMMENT 'اسم الوحدة',
  `unit_symbol` varchar(10) NOT NULL COMMENT 'رمز الوحدة',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_units_created_by` (`created_by`),
  CONSTRAINT `fk_units_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='وحدات القياس';

-- ===================================================
-- 11. جدول فئات الأصناف
-- ===================================================
CREATE TABLE `item_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(100) NOT NULL COMMENT 'اسم الفئة',
  `parent_id` int(11) DEFAULT NULL COMMENT 'الفئة الأب',
  `description` text COMMENT 'الوصف',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_categories_parent` (`parent_id`),
  KEY `fk_categories_created_by` (`created_by`),
  CONSTRAINT `fk_categories_parent` FOREIGN KEY (`parent_id`) REFERENCES `item_categories` (`id`),
  CONSTRAINT `fk_categories_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='فئات الأصناف';

-- ===================================================
-- 12. جدول الأصناف (خامات ومنتجات وخدمات)
-- ===================================================
CREATE TABLE `items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `item_code` varchar(20) NOT NULL UNIQUE COMMENT 'رمز الصنف',
  `barcode` varchar(50) DEFAULT NULL COMMENT 'الباركود',
  `item_name` varchar(255) NOT NULL COMMENT 'اسم الصنف',
  `item_type` enum('raw_material','product','service') NOT NULL COMMENT 'نوع الصنف',
  `category_id` int(11) DEFAULT NULL COMMENT 'الفئة',
  `main_unit_id` int(11) NOT NULL COMMENT 'الوحدة الرئيسية',
  `sub_unit_id` int(11) DEFAULT NULL COMMENT 'الوحدة الفرعية',
  `conversion_factor` decimal(10,3) DEFAULT 1.000 COMMENT 'معامل التحويل',
  `purchase_price` decimal(10,3) DEFAULT 0.000 COMMENT 'سعر الشراء',
  `selling_price` decimal(10,3) DEFAULT 0.000 COMMENT 'سعر البيع',
  `min_stock` decimal(10,3) DEFAULT 0.000 COMMENT 'الحد الأدنى للمخزون',
  `max_stock` decimal(10,3) DEFAULT 0.000 COMMENT 'الحد الأقصى للمخزون',
  `current_stock` decimal(10,3) DEFAULT 0.000 COMMENT 'المخزون الحالي',
  `cost_method` enum('fifo','lifo','average') DEFAULT 'average' COMMENT 'طريقة التكلفة',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `description` text COMMENT 'الوصف',
  `image` varchar(255) DEFAULT NULL COMMENT 'صورة الصنف',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_barcode` (`barcode`),
  KEY `fk_items_category` (`category_id`),
  KEY `fk_items_main_unit` (`main_unit_id`),
  KEY `fk_items_sub_unit` (`sub_unit_id`),
  KEY `fk_items_created_by` (`created_by`),
  KEY `idx_items_type` (`item_type`),
  CONSTRAINT `fk_items_category` FOREIGN KEY (`category_id`) REFERENCES `item_categories` (`id`),
  CONSTRAINT `fk_items_main_unit` FOREIGN KEY (`main_unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `fk_items_sub_unit` FOREIGN KEY (`sub_unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `fk_items_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الأصناف';

-- ===================================================
-- 13. جدول وصفات المنتجات
-- ===================================================
CREATE TABLE `product_recipes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `product_id` int(11) NOT NULL COMMENT 'المنتج',
  `ingredient_id` int(11) NOT NULL COMMENT 'المكون',
  `quantity` decimal(10,3) NOT NULL COMMENT 'الكمية المطلوبة',
  `unit_id` int(11) NOT NULL COMMENT 'الوحدة',
  `cost_per_unit` decimal(10,3) DEFAULT 0.000 COMMENT 'التكلفة لكل وحدة',
  `total_cost` decimal(10,3) DEFAULT 0.000 COMMENT 'إجمالي التكلفة',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_product_ingredient` (`product_id`, `ingredient_id`),
  KEY `fk_recipes_product` (`product_id`),
  KEY `fk_recipes_ingredient` (`ingredient_id`),
  KEY `fk_recipes_unit` (`unit_id`),
  KEY `fk_recipes_created_by` (`created_by`),
  CONSTRAINT `fk_recipes_product` FOREIGN KEY (`product_id`) REFERENCES `items` (`id`),
  CONSTRAINT `fk_recipes_ingredient` FOREIGN KEY (`ingredient_id`) REFERENCES `items` (`id`),
  CONSTRAINT `fk_recipes_unit` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`),
  CONSTRAINT `fk_recipes_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='وصفات المنتجات';

-- ===================================================
-- 14. جدول العملاء
-- ===================================================
CREATE TABLE `customers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `customer_code` varchar(20) NOT NULL UNIQUE COMMENT 'رمز العميل',
  `customer_name` varchar(255) NOT NULL COMMENT 'اسم العميل',
  `customer_type` enum('individual','company') DEFAULT 'individual' COMMENT 'نوع العميل',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `email` varchar(100) DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `address` text COMMENT 'العنوان',
  `tax_number` varchar(50) DEFAULT NULL COMMENT 'الرقم الضريبي',
  `credit_limit` decimal(15,3) DEFAULT 0.000 COMMENT 'حد الائتمان',
  `current_balance` decimal(15,3) DEFAULT 0.000 COMMENT 'الرصيد الحالي',
  `account_id` int(11) DEFAULT NULL COMMENT 'الحساب المرتبط',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_customers_account` (`account_id`),
  KEY `fk_customers_created_by` (`created_by`),
  CONSTRAINT `fk_customers_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_customers_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='العملاء';
