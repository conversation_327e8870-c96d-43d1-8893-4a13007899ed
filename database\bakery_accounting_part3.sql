-- ===================================================
-- قاعدة بيانات نظام المحاسبة للمخبز - الجزء الثالث
-- Bakery Accounting System Database - Part 3
-- ===================================================

-- ===================================================
-- 15. جدول الموردين
-- ===================================================
CREATE TABLE `suppliers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `supplier_code` varchar(20) NOT NULL UNIQUE COMMENT 'رمز المورد',
  `supplier_name` varchar(255) NOT NULL COMMENT 'اسم المورد',
  `supplier_type` enum('individual','company') DEFAULT 'company' COMMENT 'نوع المورد',
  `phone` varchar(20) DEFAULT NULL COMMENT 'رقم الهاتف',
  `email` varchar(100) DEFAULT NULL COMMENT 'البريد الإلكتروني',
  `address` text COMMENT 'العنوان',
  `tax_number` varchar(50) DEFAULT NULL COMMENT 'الرقم الضريبي',
  `current_balance` decimal(15,3) DEFAULT 0.000 COMMENT 'الرصيد الحالي',
  `account_id` int(11) DEFAULT NULL COMMENT 'الحساب المرتبط',
  `is_active` tinyint(1) DEFAULT 1 COMMENT 'نشط',
  `notes` text COMMENT 'ملاحظات',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_suppliers_account` (`account_id`),
  KEY `fk_suppliers_created_by` (`created_by`),
  CONSTRAINT `fk_suppliers_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_suppliers_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الموردين';

-- ===================================================
-- 16. جدول الفواتير
-- ===================================================
CREATE TABLE `invoices` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_number` varchar(20) NOT NULL UNIQUE COMMENT 'رقم الفاتورة',
  `invoice_type` enum('sales','purchase','sales_return','purchase_return') NOT NULL COMMENT 'نوع الفاتورة',
  `invoice_date` date NOT NULL COMMENT 'تاريخ الفاتورة',
  `customer_id` int(11) DEFAULT NULL COMMENT 'العميل',
  `supplier_id` int(11) DEFAULT NULL COMMENT 'المورد',
  `subtotal` decimal(15,3) NOT NULL DEFAULT 0.000 COMMENT 'المجموع الفرعي',
  `discount_percentage` decimal(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
  `discount_amount` decimal(15,3) DEFAULT 0.000 COMMENT 'مبلغ الخصم',
  `tax_percentage` decimal(5,2) DEFAULT 0.00 COMMENT 'نسبة الضريبة',
  `tax_amount` decimal(15,3) DEFAULT 0.000 COMMENT 'مبلغ الضريبة',
  `total_amount` decimal(15,3) NOT NULL DEFAULT 0.000 COMMENT 'المبلغ الإجمالي',
  `paid_amount` decimal(15,3) DEFAULT 0.000 COMMENT 'المبلغ المدفوع',
  `remaining_amount` decimal(15,3) DEFAULT 0.000 COMMENT 'المبلغ المتبقي',
  `payment_status` enum('pending','partial','paid') DEFAULT 'pending' COMMENT 'حالة الدفع',
  `cash_bank_id` int(11) DEFAULT NULL COMMENT 'الصندوق/البنك',
  `journal_entry_id` int(11) DEFAULT NULL COMMENT 'القيد المحاسبي',
  `notes` text COMMENT 'ملاحظات',
  `is_posted` tinyint(1) DEFAULT 0 COMMENT 'مرحلة',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_invoices_customer` (`customer_id`),
  KEY `fk_invoices_supplier` (`supplier_id`),
  KEY `fk_invoices_cash_bank` (`cash_bank_id`),
  KEY `fk_invoices_journal` (`journal_entry_id`),
  KEY `fk_invoices_created_by` (`created_by`),
  KEY `idx_invoices_type` (`invoice_type`),
  KEY `idx_invoices_date` (`invoice_date`),
  CONSTRAINT `fk_invoices_customer` FOREIGN KEY (`customer_id`) REFERENCES `customers` (`id`),
  CONSTRAINT `fk_invoices_supplier` FOREIGN KEY (`supplier_id`) REFERENCES `suppliers` (`id`),
  CONSTRAINT `fk_invoices_cash_bank` FOREIGN KEY (`cash_bank_id`) REFERENCES `cash_banks` (`id`),
  CONSTRAINT `fk_invoices_journal` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`),
  CONSTRAINT `fk_invoices_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='الفواتير';

-- ===================================================
-- 17. جدول تفاصيل الفواتير
-- ===================================================
CREATE TABLE `invoice_details` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `invoice_id` int(11) NOT NULL COMMENT 'الفاتورة',
  `item_id` int(11) NOT NULL COMMENT 'الصنف',
  `quantity` decimal(10,3) NOT NULL COMMENT 'الكمية',
  `unit_id` int(11) NOT NULL COMMENT 'الوحدة',
  `unit_price` decimal(10,3) NOT NULL COMMENT 'سعر الوحدة',
  `discount_percentage` decimal(5,2) DEFAULT 0.00 COMMENT 'نسبة الخصم',
  `discount_amount` decimal(10,3) DEFAULT 0.000 COMMENT 'مبلغ الخصم',
  `line_total` decimal(15,3) NOT NULL COMMENT 'إجمالي السطر',
  `notes` text COMMENT 'ملاحظات',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_invoice_details_invoice` (`invoice_id`),
  KEY `fk_invoice_details_item` (`item_id`),
  KEY `fk_invoice_details_unit` (`unit_id`),
  CONSTRAINT `fk_invoice_details_invoice` FOREIGN KEY (`invoice_id`) REFERENCES `invoices` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_invoice_details_item` FOREIGN KEY (`item_id`) REFERENCES `items` (`id`),
  CONSTRAINT `fk_invoice_details_unit` FOREIGN KEY (`unit_id`) REFERENCES `units` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='تفاصيل الفواتير';

-- ===================================================
-- 18. جدول سندات القبض والصرف
-- ===================================================
CREATE TABLE `vouchers` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `voucher_number` varchar(20) NOT NULL UNIQUE COMMENT 'رقم السند',
  `voucher_type` enum('receipt','payment') NOT NULL COMMENT 'نوع السند',
  `voucher_date` date NOT NULL COMMENT 'تاريخ السند',
  `party_type` enum('customer','supplier','employee','owner','expense','revenue','other') NOT NULL COMMENT 'نوع الطرف',
  `party_id` int(11) DEFAULT NULL COMMENT 'معرف الطرف',
  `party_name` varchar(255) DEFAULT NULL COMMENT 'اسم الطرف',
  `amount` decimal(15,3) NOT NULL COMMENT 'المبلغ',
  `cash_bank_id` int(11) NOT NULL COMMENT 'الصندوق/البنك',
  `account_id` int(11) DEFAULT NULL COMMENT 'الحساب المقابل',
  `reference_type` varchar(50) DEFAULT NULL COMMENT 'نوع المرجع',
  `reference_id` int(11) DEFAULT NULL COMMENT 'معرف المرجع',
  `description` text COMMENT 'البيان',
  `journal_entry_id` int(11) DEFAULT NULL COMMENT 'القيد المحاسبي',
  `is_posted` tinyint(1) DEFAULT 0 COMMENT 'مرحل',
  `created_by` int(11) NOT NULL COMMENT 'أنشأ بواسطة',
  `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `fk_vouchers_cash_bank` (`cash_bank_id`),
  KEY `fk_vouchers_account` (`account_id`),
  KEY `fk_vouchers_journal` (`journal_entry_id`),
  KEY `fk_vouchers_created_by` (`created_by`),
  KEY `idx_vouchers_type` (`voucher_type`),
  KEY `idx_vouchers_date` (`voucher_date`),
  KEY `idx_vouchers_party` (`party_type`, `party_id`),
  CONSTRAINT `fk_vouchers_cash_bank` FOREIGN KEY (`cash_bank_id`) REFERENCES `cash_banks` (`id`),
  CONSTRAINT `fk_vouchers_account` FOREIGN KEY (`account_id`) REFERENCES `chart_of_accounts` (`id`),
  CONSTRAINT `fk_vouchers_journal` FOREIGN KEY (`journal_entry_id`) REFERENCES `journal_entries` (`id`),
  CONSTRAINT `fk_vouchers_created_by` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='سندات القبض والصرف';
