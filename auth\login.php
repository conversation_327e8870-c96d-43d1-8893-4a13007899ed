<?php
/**
 * صفحة تسجيل الدخول
 * Login Page
 */

require_once __DIR__ . '/../config/config.php';

// إعادة توجيه إذا كان المستخدم مسجل دخول بالفعل
if (isset($_SESSION['user_id'])) {
    header('Location: ' . BASE_URL . '/index.php');
    exit();
}

$error_message = '';
$success_message = '';

// معالجة تسجيل الدخول
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = cleanInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $remember_me = isset($_POST['remember_me']);
    
    if (empty($username) || empty($password)) {
        $error_message = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        // فحص محاولات تسجيل الدخول
        $failed_attempts = checkLoginAttempts($username);
        
        if ($failed_attempts >= MAX_LOGIN_ATTEMPTS) {
            $error_message = 'تم تجاوز الحد الأقصى لمحاولات تسجيل الدخول. يرجى المحاولة بعد 15 دقيقة.';
        } else {
            try {
                // البحث عن المستخدم
                $user = queryOne(
                    "SELECT id, username, password, full_name, email, phone, role, permissions, is_active 
                     FROM users 
                     WHERE username = ? AND is_active = 1",
                    [$username]
                );
                
                if ($user && password_verify($password, $user['password'])) {
                    // تسجيل دخول ناجح
                    $_SESSION['user_id'] = $user['id'];
                    $_SESSION['username'] = $user['username'];
                    $_SESSION['full_name'] = $user['full_name'];
                    $_SESSION['role'] = $user['role'];
                    $_SESSION['permissions'] = $user['permissions'];
                    $_SESSION['last_activity'] = time();
                    
                    // تحديث وقت آخر تسجيل دخول
                    updateLastLogin($user['id']);
                    
                    // تسجيل النشاط
                    logUserActivity('login', 'users', $user['id']);
                    
                    // تذكرني
                    if ($remember_me) {
                        $token = bin2hex(random_bytes(32));
                        setcookie('remember_token', $token, time() + (30 * 24 * 60 * 60), '/'); // 30 يوم
                        
                        // حفظ الرمز في قاعدة البيانات (يمكن إضافة جدول للرموز)
                    }
                    
                    // إعادة توجيه
                    $redirect = $_GET['redirect'] ?? BASE_URL . '/index.php';
                    header('Location: ' . $redirect);
                    exit();
                } else {
                    // تسجيل دخول فاشل
                    $error_message = 'اسم المستخدم أو كلمة المرور غير صحيحة';
                    logFailedLogin($username);
                }
            } catch (Exception $e) {
                $error_message = 'خطأ في النظام. يرجى المحاولة لاحقاً.';
                logError("خطأ في تسجيل الدخول: " . $e->getMessage());
            }
        }
    }
}

// معالجة الرسائل من URL
if (isset($_GET['timeout'])) {
    $error_message = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
} elseif (isset($_GET['logout'])) {
    $success_message = 'تم تسجيل الخروج بنجاح.';
} elseif (isset($_GET['error'])) {
    switch ($_GET['error']) {
        case 'permission':
            $error_message = 'ليس لديك صلاحية للوصول إلى هذه الصفحة.';
            break;
        case 'user_not_found':
            $error_message = 'المستخدم غير موجود أو غير نشط.';
            break;
        case 'database':
            $error_message = 'خطأ في قاعدة البيانات.';
            break;
    }
}

$company_settings = getCompanySettings();
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - <?php echo htmlspecialchars($company_settings['company_name']); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            max-width: 400px;
            width: 100%;
        }
        
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .login-header h2 {
            margin: 0;
            font-weight: 600;
        }
        
        .login-body {
            padding: 2rem;
        }
        
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        .input-group-text {
            background: transparent;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }
        
        .form-control.with-icon {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }
        
        .system-info {
            text-align: center;
            margin-top: 1rem;
            color: #6c757d;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <i class="bi bi-shop fs-1 mb-3"></i>
                <h2><?php echo htmlspecialchars($company_settings['company_name']); ?></h2>
                <p class="mb-0">نظام المحاسبة والإدارة</p>
            </div>
            
            <div class="login-body">
                <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle me-2"></i>
                        <?php echo htmlspecialchars($success_message); ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" action="">
                    <div class="mb-3">
                        <label for="username" class="form-label">اسم المستخدم</label>
                        <div class="input-group">
                            <input type="text" class="form-control with-icon" id="username" name="username" 
                                   value="<?php echo htmlspecialchars($_POST['username'] ?? ''); ?>" required>
                            <span class="input-group-text">
                                <i class="bi bi-person"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">كلمة المرور</label>
                        <div class="input-group">
                            <input type="password" class="form-control with-icon" id="password" name="password" required>
                            <span class="input-group-text">
                                <i class="bi bi-lock"></i>
                            </span>
                        </div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            تذكرني
                        </label>
                    </div>
                    
                    <button type="submit" class="btn btn-primary btn-login w-100">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="system-info">
                    <small>
                        نظام محاسبة المخبز v1.0.0<br>
                        تطوير: AnwarSoft
                    </small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
