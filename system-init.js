// ملف التهيئة الشامل للنظام

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializeSystem();
});

// تهيئة النظام
function initializeSystem() {
    // التحقق من المصادقة
    const user = checkAuth();
    if (user) {
        updateUserInterface(user);
    }
    
    // تحميل إعدادات النظام
    loadSystemSettings();
    
    // تحميل إعدادات الطباعة
    loadPrintSettings();
    
    // تهيئة القائمة الجانبية
    initializeSidebar();
    
    // تهيئة الصفحة الحالية
    initializeCurrentPage();
    
    // تهيئة نظام التنبيهات
    initializeNotifications();
    
    // تهيئة نظام النسخ الاحتياطي
    initializeBackupSystem();
    
    // تهيئة نظام التدقيق
    initializeAuditSystem();
}

// تحديث واجهة المستخدم
function updateUserInterface(user) {
    // تحديث اسم المستخدم في القائمة الجانبية
    const sidebarUserName = document.getElementById('sidebarUserName');
    if (sidebarUserName) {
        sidebarUserName.textContent = user.name;
    }
    
    // تحديث صلاحيات المستخدم
    updateUserPermissions(user);
    
    // تحديث آخر تسجيل دخول
    updateLastLogin(user);
}

// تحديث صلاحيات المستخدم
function updateUserPermissions(user) {
    const userRole = user.role;
    
    // إخفاء/إظهار العناصر حسب الصلاحيات
    const restrictedElements = document.querySelectorAll('[data-permission]');
    restrictedElements.forEach(element => {
        const requiredPermission = element.dataset.permission;
        if (!hasPermission(userRole, requiredPermission)) {
            element.style.display = 'none';
        }
    });
}

// التحقق من الصلاحيات
function hasPermission(userRole, permission) {
    const permissions = {
        'admin': ['all'],
        'manager': ['view', 'edit', 'approve', 'reports'],
        'accountant': ['view', 'edit', 'journal', 'reports'],
        'cashier': ['view', 'sales', 'cash'],
        'employee': ['view']
    };
    
    const userPermissions = permissions[userRole] || [];
    return userPermissions.includes('all') || userPermissions.includes(permission);
}

// تحميل إعدادات النظام
function loadSystemSettings() {
    const settings = localStorage.getItem('systemSettings');
    if (settings) {
        window.systemSettings = JSON.parse(settings);
    } else {
        // إعدادات افتراضية
        window.systemSettings = {
            currency: 'YER',
            currencySymbol: 'ر.ي',
            dateFormat: 'dd/mm/yyyy',
            language: 'ar',
            theme: 'light',
            autoSave: true,
            autoBackup: true,
            notifications: true,
            auditLog: true,
            sessionTimeout: 30, // دقيقة
            maxLoginAttempts: 3,
            passwordExpiry: 90, // يوم
            companyInfo: {
                name: 'مخبز الأصالة',
                address: 'صنعاء - شارع الزبيري',
                phone: '+967 1 234567',
                email: '<EMAIL>',
                taxNumber: '*********',
                commercialRegister: 'CR123456'
            }
        };
        saveSystemSettings();
    }
}

// حفظ إعدادات النظام
function saveSystemSettings() {
    localStorage.setItem('systemSettings', JSON.stringify(window.systemSettings));
}

// تهيئة القائمة الجانبية
function initializeSidebar() {
    // استعادة حالة القائمة الجانبية
    const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (sidebarCollapsed) {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        if (sidebar && mainContent) {
            sidebar.classList.add('collapsed');
            mainContent.classList.add('expanded');
        }
    }
    
    // تفعيل الرابط النشط
    setActiveMenuItem();
}

// تعيين العنصر النشط في القائمة
function setActiveMenuItem() {
    const currentPage = window.location.pathname.split('/').pop();
    const menuLinks = document.querySelectorAll('.sidebar-menu a');
    
    menuLinks.forEach(link => {
        link.classList.remove('active');
        if (link.getAttribute('href') === currentPage) {
            link.classList.add('active');
        }
    });
}

// تهيئة الصفحة الحالية
function initializeCurrentPage() {
    const currentPage = window.location.pathname.split('/').pop();
    
    switch (currentPage) {
        case 'company.html':
            if (typeof initializeCompanyPage === 'function') {
                initializeCompanyPage();
            }
            break;
        case 'cash-banks.html':
            if (typeof initializeCashBanksPage === 'function') {
                initializeCashBanksPage();
            }
            break;
        case 'employees.html':
            if (typeof initializeEmployeesPage === 'function') {
                initializeEmployeesPage();
            }
            break;
        case 'inventory.html':
            if (typeof initializePage === 'function') {
                initializePage();
            }
            break;
        case 'products.html':
            if (typeof initializePage === 'function') {
                initializePage();
            }
            break;
        case 'journal-entries.html':
            if (typeof initializeJournalPage === 'function') {
                initializeJournalPage();
            }
            break;
    }
}

// تهيئة نظام التنبيهات
function initializeNotifications() {
    if (!window.systemSettings.notifications) return;
    
    // التحقق من التنبيهات المعلقة
    checkPendingNotifications();
    
    // تعيين فترة التحقق الدورية
    setInterval(checkPendingNotifications, 60000); // كل دقيقة
}

// التحقق من التنبيهات المعلقة
function checkPendingNotifications() {
    const notifications = [];
    
    // تنبيهات المخزون المنخفض
    if (typeof items !== 'undefined') {
        items.forEach(item => {
            const totalStock = Object.values(item.warehouseStocks || {}).reduce((sum, stock) => sum + stock, 0);
            if (totalStock <= item.minStock) {
                notifications.push({
                    type: 'warning',
                    title: 'مخزون منخفض',
                    message: `الصنف "${item.name}" وصل للحد الأدنى`,
                    action: () => window.location.href = 'inventory.html'
                });
            }
        });
    }
    
    // تنبيهات القيود المعلقة
    if (typeof journalEntries !== 'undefined') {
        const pendingEntries = journalEntries.filter(entry => entry.status === 'pending').length;
        if (pendingEntries > 0) {
            notifications.push({
                type: 'info',
                title: 'قيود معلقة',
                message: `يوجد ${pendingEntries} قيد في انتظار المراجعة`,
                action: () => window.location.href = 'journal-entries.html'
            });
        }
    }
    
    // عرض التنبيهات
    displayNotifications(notifications);
}

// عرض التنبيهات
function displayNotifications(notifications) {
    const container = document.getElementById('notificationsContainer');
    if (!container && notifications.length > 0) {
        createNotificationsContainer();
    }
    
    notifications.forEach(notification => {
        showNotification(notification);
    });
}

// إنشاء حاوي التنبيهات
function createNotificationsContainer() {
    const container = document.createElement('div');
    container.id = 'notificationsContainer';
    container.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        z-index: 9999;
        max-width: 300px;
    `;
    document.body.appendChild(container);
}

// عرض تنبيه
function showNotification(notification) {
    const notificationElement = document.createElement('div');
    notificationElement.className = `alert alert-${notification.type} alert-dismissible fade show`;
    notificationElement.style.cssText = `
        margin-bottom: 10px;
        cursor: pointer;
    `;
    
    notificationElement.innerHTML = `
        <strong>${notification.title}</strong><br>
        ${notification.message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    if (notification.action) {
        notificationElement.addEventListener('click', notification.action);
    }
    
    const container = document.getElementById('notificationsContainer');
    if (container) {
        container.appendChild(notificationElement);
        
        // إزالة التنبيه تلقائياً بعد 5 ثوان
        setTimeout(() => {
            if (notificationElement.parentNode) {
                notificationElement.remove();
            }
        }, 5000);
    }
}

// تهيئة نظام النسخ الاحتياطي
function initializeBackupSystem() {
    if (!window.systemSettings.autoBackup) return;
    
    // إنشاء نسخة احتياطية كل ساعة
    setInterval(createAutoBackup, 3600000);
    
    // إنشاء نسخة احتياطية عند إغلاق الصفحة
    window.addEventListener('beforeunload', createAutoBackup);
}

// إنشاء نسخة احتياطية تلقائية
function createAutoBackup() {
    const backupData = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        data: {
            systemSettings: window.systemSettings,
            users: JSON.parse(localStorage.getItem('users') || '[]'),
            journalEntries: typeof journalEntries !== 'undefined' ? journalEntries : [],
            items: typeof items !== 'undefined' ? items : [],
            employees: typeof employees !== 'undefined' ? employees : [],
            products: typeof products !== 'undefined' ? products : []
        }
    };
    
    localStorage.setItem('autoBackup', JSON.stringify(backupData));
    console.log('تم إنشاء نسخة احتياطية تلقائية');
}

// استعادة النسخة الاحتياطية
function restoreBackup() {
    const backup = localStorage.getItem('autoBackup');
    if (!backup) {
        alert('لا توجد نسخة احتياطية متاحة');
        return;
    }
    
    if (confirm('هل أنت متأكد من استعادة النسخة الاحتياطية؟ سيتم فقدان البيانات الحالية.')) {
        const backupData = JSON.parse(backup);
        
        // استعادة البيانات
        localStorage.setItem('systemSettings', JSON.stringify(backupData.data.systemSettings));
        localStorage.setItem('users', JSON.stringify(backupData.data.users));
        
        // إعادة تحميل الصفحة
        window.location.reload();
    }
}

// تهيئة نظام التدقيق
function initializeAuditSystem() {
    if (!window.systemSettings.auditLog) return;
    
    // تسجيل دخول المستخدم
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    if (user) {
        logAuditEvent('login', 'system', null, { userId: user.id, userName: user.name });
    }
    
    // مراقبة تغييرات البيانات
    setupDataChangeMonitoring();
}

// تسجيل حدث تدقيق
function logAuditEvent(action, module, recordId, details) {
    const user = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
    
    const auditEntry = {
        id: Date.now(),
        timestamp: new Date().toISOString(),
        userId: user ? user.id : null,
        userName: user ? user.name : 'نظام',
        action: action,
        module: module,
        recordId: recordId,
        details: details,
        ipAddress: '*************', // محاكاة
        userAgent: navigator.userAgent
    };
    
    // حفظ في سجل التدقيق
    const auditLog = JSON.parse(localStorage.getItem('auditLog') || '[]');
    auditLog.push(auditEntry);
    
    // الاحتفاظ بآخر 1000 سجل فقط
    if (auditLog.length > 1000) {
        auditLog.splice(0, auditLog.length - 1000);
    }
    
    localStorage.setItem('auditLog', JSON.stringify(auditLog));
}

// إعداد مراقبة تغييرات البيانات
function setupDataChangeMonitoring() {
    // مراقبة تغييرات localStorage
    const originalSetItem = localStorage.setItem;
    localStorage.setItem = function(key, value) {
        const oldValue = localStorage.getItem(key);
        originalSetItem.call(this, key, value);
        
        // تسجيل التغيير
        if (oldValue !== value) {
            logAuditEvent('update', 'storage', key, {
                oldValue: oldValue,
                newValue: value
            });
        }
    };
}

// تصدير البيانات
function exportData(format = 'json') {
    const data = {
        exportDate: new Date().toISOString(),
        systemSettings: window.systemSettings,
        journalEntries: typeof journalEntries !== 'undefined' ? journalEntries : [],
        items: typeof items !== 'undefined' ? items : [],
        employees: typeof employees !== 'undefined' ? employees : [],
        products: typeof products !== 'undefined' ? products : []
    };
    
    let content, filename, mimeType;
    
    switch (format) {
        case 'json':
            content = JSON.stringify(data, null, 2);
            filename = `bakery-data-${new Date().toISOString().split('T')[0]}.json`;
            mimeType = 'application/json';
            break;
        case 'csv':
            // تحويل إلى CSV (مبسط)
            content = convertToCSV(data);
            filename = `bakery-data-${new Date().toISOString().split('T')[0]}.csv`;
            mimeType = 'text/csv';
            break;
        default:
            alert('تنسيق غير مدعوم');
            return;
    }
    
    downloadFile(content, filename, mimeType);
}

// تحويل البيانات إلى CSV
function convertToCSV(data) {
    // تنفيذ مبسط - يمكن تحسينه
    let csv = 'النوع,البيانات\n';
    csv += `إعدادات النظام,"${JSON.stringify(data.systemSettings).replace(/"/g, '""')}"\n`;
    csv += `القيود المحاسبية,"${JSON.stringify(data.journalEntries).replace(/"/g, '""')}"\n`;
    return csv;
}

// تحميل ملف
function downloadFile(content, filename, mimeType) {
    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
}

// استيراد البيانات
function importData(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            
            if (confirm('هل أنت متأكد من استيراد البيانات؟ سيتم استبدال البيانات الحالية.')) {
                // استيراد البيانات
                if (data.systemSettings) {
                    localStorage.setItem('systemSettings', JSON.stringify(data.systemSettings));
                }
                
                // إعادة تحميل الصفحة
                window.location.reload();
            }
        } catch (error) {
            alert('خطأ في قراءة الملف: ' + error.message);
        }
    };
    reader.readAsText(file);
}

// تنظيف البيانات القديمة
function cleanupOldData() {
    const cutoffDate = new Date();
    cutoffDate.setMonth(cutoffDate.getMonth() - 6); // 6 أشهر
    
    // تنظيف سجل التدقيق
    const auditLog = JSON.parse(localStorage.getItem('auditLog') || '[]');
    const cleanedAuditLog = auditLog.filter(entry => new Date(entry.timestamp) > cutoffDate);
    localStorage.setItem('auditLog', JSON.stringify(cleanedAuditLog));
    
    console.log(`تم تنظيف ${auditLog.length - cleanedAuditLog.length} سجل قديم`);
}

// تشغيل تنظيف البيانات أسبوعياً
setInterval(cleanupOldData, 7 * 24 * 60 * 60 * 1000); // أسبوع

// تهيئة صفحة القيود المحاسبية
function initializeJournalPage() {
    if (typeof displayJournalEntries === 'function') {
        displayJournalEntries();
    }
    if (typeof updateStats === 'function') {
        updateStats();
    }
}

// إضافة نظام الطباعة لجميع الصفحات
function addPrintButtonToAllPages() {
    const printButton = document.querySelector('.btn[onclick="printPage()"]');
    if (printButton) {
        printButton.addEventListener('click', function() {
            const currentPage = window.location.pathname.split('/').pop();
            
            switch (currentPage) {
                case 'inventory.html':
                    printInventoryReport();
                    break;
                case 'employees.html':
                    printPayrollReport();
                    break;
                case 'journal-entries.html':
                    if (journalEntries.length > 0) {
                        printJournalEntry(journalEntries[0].id);
                    }
                    break;
                default:
                    window.print();
            }
        });
    }
}
